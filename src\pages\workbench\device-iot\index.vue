<template>
    <view style="padding: 10px">
        <InformationCard
            v-for="(v, k) in iotDeviceList"
            :key="k"
            @click:content="toIotDeviceDetails(v.deviceId || '')"
            :icon-image="gateWay"
            :status="v.status"
            :image-style="{ height: '100px', width: '100%', objectFit: 'cover' }"
        >
            <template #title>
                <view>{{ v.deviceName }}</view>
            </template>
            <template #line-first>
                <view>{{ `${$t('IotService.NodeId')}:${v.nodeId}` }}</view>
            </template>
            <template #line-second>
                <view>{{ `${$t('IotService.FwVersion')}:${v.fwVersion}` }}</view>
            </template>
            <template #line-third>
                <view>{{ `${$t('IotService.SwVersion')}:${v.swVersion}` }}</view>
            </template>
            <template #space-one>
                <nut-tag style="padding: 0" type="primary">{{ `${$t(`IotService.${v.nodeType}`)}` }}</nut-tag>
            </template>
            <template #space-two>
                <nut-tag style="padding: 0; margin-right: 0.5vh" :type="v.status === 'ONLINE' ? 'success' : 'danger'">
                    {{ `${$t(`IotService.${v.status}`)}` }}
                </nut-tag>
            </template>
        </InformationCard>
    </view>
    <view v-if="isNoMore" class="no-more-data">{{ $t('ui.noMore') }}</view>
    <nut-empty v-if="isFetchError" description="Error" />
</template>

<script lang="ts" setup>
import { IotDeviceService } from '@/api/proxy/iot-device-service/iot-device-service.service'
import {
    GetIotDeviceInput,
    IotDeviceListDto,
    IotDeviceUseStatus,
    IotNodeType,
} from '@/api/proxy/iot-device-service/models'
import { useDidShow, usePullDownRefresh, useReachBottom } from '@/hooks/component/hooks'
import { reactive, ref } from 'vue'
import { HideLoading, Loading, NavigateTo, RequestFail } from '@/utils/Taro-api'
import Taro from '@tarojs/taro'
import gateWay from '@/assets/images/gate-way.png'
import { t } from '@/locale/fanyi'

/**
 * @description 更改导航栏标题
 */
Taro.setNavigationBarTitle({ title: t('IotService.IotHubGateway') })

const iotDeviceService = new IotDeviceService()
const iotDeviceList = ref<IotDeviceListDto[]>([])
const isNoMore = ref<boolean>(false)
const isFetchError = ref<boolean>(false)

/**
 * 标记是否已经自动跳转过，避免从详情页返回时再次跳转造成循环
 */
const hasAutoJumped = ref<boolean>(false)

const parmas = reactive<GetIotDeviceInput>({
    nodeType: IotNodeType.Gateway,
    useStatus: IotDeviceUseStatus.Unknown,
    maxResultCount: 10,
    sorting: '',
    filterText: '',
    skipCount: 0,
})

/**
 * @description 获取物联网设备列表
 */
const fetchData = async () => {
    try {
        Loading()
        const result = await iotDeviceService.getPaged(parmas)
        if (result.items.length === 0) {
            HideLoading()
            isNoMore.value = true
            return
        }
        iotDeviceList.value = result.items
        HideLoading()
    } catch {
        RequestFail()
        isFetchError.value = true
    }
}

/**
 * @description 跳转到设备详情
 */
const toIotDeviceDetails = (id: string) => {
    NavigateTo(`/pages/workbench/device-iot/component/iot-device-details?id=${id}`)
}

/**
 * @description 获取二维码参数
 * @returns 解析后的参数对象
 */
const getQrCodeUrlParams = () => {
    // 获取当前页面实例
    const currentInstance = Taro.getCurrentInstance()
    const params = currentInstance.router?.params || {}
    const result: Record<string, string> = {}
    // 处理scene参数
    if (params.scene) {
        // 解码scene参数
        const sceneParams = decodeURIComponent(params.scene)
        // 解析参数
        sceneParams.split('&').forEach(item => {
            const [key, value] = item.split('=')
            result[key] = value
        })
    }
    // 处理q参数
    if (params.q) {
        try {
            // 解码URL
            const decodedUrl = decodeURIComponent(params.q)
            // 解析URL中的查询参数
            const queryIndex = decodedUrl.indexOf('?')
            if (queryIndex !== -1) {
                const queryString = decodedUrl.slice(queryIndex + 1)
                queryString.split('&').forEach(param => {
                    const [key, value] = param.split('=')
                    if (key && value) {
                        result[key] = decodeURIComponent(value)
                    }
                })
            }
        } catch (error) {
            console.error('解析URL参数失败:', error)
        }
    }

    // 处理常规URL参数
    for (const key in params) {
        if (key !== 'scene' && key !== 'q' && !result[key]) {
            result[key] = params[key] as string
        }
    }
    return result
}

/**
 * @description 页面显示时钩子
 */
useDidShow(() => {
    // 如果已经自动跳转过，则直接加载数据，不再执行跳转
    if (hasAutoJumped.value) {
        fetchData()
        return
    }
    // 获取URL参数
    const urlParams = getQrCodeUrlParams()
    // 如果存在id参数，直接跳转到设备详情页
    if (urlParams.id) {
        hasAutoJumped.value = true // 标记已经跳转过
        toIotDeviceDetails(urlParams.id)
        return
    }
    fetchData()
})

/**
 * @description 页面触底时钩子
 */
useReachBottom(async () => {
    try {
        if (isNoMore.value) {
            return
        }
        Loading()
        parmas.skipCount += 10
        const result = await iotDeviceService.getPaged(parmas)
        iotDeviceList.value.concat(result.items)
        if (result.items.length === 0) {
            isNoMore.value = true
        }
        HideLoading()
    } catch {
        RequestFail()
    }
})

/**
 * @description 页面下拉刷新
 */
usePullDownRefresh(async () => {
    parmas.skipCount = 0
    isNoMore.value = false
    await fetchData()
    Taro.stopPullDownRefresh()
})
</script>

<style lang="scss">
.no-more-data {
    text-align: center;
    padding: 10px;
    padding-bottom: 30px;
    color: #999;
}

.line-content {
    display: flex;
    align-items: center;
}
</style>
