<template>
    <view>
        <nut-row>
            <nut-col :span="24">
                <view style="display: flex; justify-content: space-between; align-items: center; padding: 0 20px">
                    <nut-button class="custom-btn primary-btn" plain type="info" size="small" @click="isAdd = true">
                        {{ t('text.add') }}
                    </nut-button>
                    <nut-button
                        class="custom-btn outline-btn"
                        :loading="isLoading"
                        plain
                        type="info"
                        size="small"
                        @click="loading"
                    >
                        {{ t('ui.refresh') }}
                    </nut-button>
                </view>
            </nut-col>
        </nut-row>
        <nut-popup v-model:visible="showRecycle" position="bottom" :style="{ height: '70%' }">
            <Recycle :recycleList="getRecycleList" />
        </nut-popup>
    </view>
    <view class="warehouse-list" v-if="!isEmpty">
        <view v-for="(item, index) in formattedWmInfo" :key="index" @click="openDetails(item.id)">
            <InformationCard
                :isActivate="item.isActivate"
                @update:showMore="updateShowMore(index, $event)"
                :show-empty-image="false"
            >
                <template #front-title> [{{ index + 1 }}] </template>
                <template #title>
                    {{ item.name }}
                </template>
                <template #line-first> {{ $t('text.encode') }}：{{ item.encode }} </template>
                <template #line-second> {{ $t('text.location') }}：{{ item.location }} </template>
                <template #line-third> {{ $t('text.areaSize') }}：{{ item.areaSize }} </template>
                <template #line-fourth>
                    {{ $t('text.dutyUserName') }}：{{ item.dutyUserName || $t('text.notYet') }}
                </template>
                <template #content-default> {{ $t('text.creationTime') }}：{{ item.creationTime }} </template>
                <template #space-one>
                    <nut-tag :color="item.isActivate ? '#30D479' : '#FF0000'" style="margin-bottom: 0.5vh">
                        {{ $t(item.isActivate ? 'Helper.Enable' : 'Helper.Disable') }}
                    </nut-tag>
                </template>
                <template #space-two>
                    <view style="margin-bottom: 0.5vh; margin-right: 0.5vh" @click.stop>
                        <nut-popover
                            v-model:visible="showMore[index]"
                            :list="list"
                            theme="light"
                            location="left"
                            @choose="choose(item.id, $event)"
                        >
                            <template #reference>
                                <nut-button plain type="info" size="small">{{ t('menu.more') }}</nut-button>
                            </template>
                        </nut-popover>
                    </view>
                </template>
            </InformationCard>
        </view>
    </view>
    <nut-empty v-else image="empty" :description="t('text.errorRequest')"></nut-empty>
    <nut-dialog
        :title="t('text.warmReminder')"
        :content="t('text.isDelete')"
        v-model:visible="isDelete"
        @cancel="isDelete = false"
        @ok="confirmDel"
        :ok-text="t('ui.confirm')"
        :cancel-text="t('ui.cancel')"
    />
    <Edit
        :editVisible="editVisible"
        @update:editVisible="editVisible = $event"
        :id="editId"
        :getWmEdit="getWmEdit"
        funName="confirmEdit"
        @fetchData="fetchData"
    />
    <Edit :editVisible="isAdd" @update:editVisible="isAdd = $event" funName="confirmAdd" @fetchData="fetchData" />
</template>

<script lang="ts" setup>
import { useDidShow } from '@tarojs/taro'
import { ref } from 'vue'
import { WmWarehouseService } from '@/api/proxy/enterprise/controller/wm-warehouse.service'
import { GetWmWarehouseInput } from '@/api/proxy/enterprise/wms/dtos'
import { GetForEditInput } from '@/api/proxy/helper/shared/models'
import Edit from './component/warehouse-edit-information.vue'
import Recycle from './component/warehouse-recycle-form.vue'
import { Loading, HideLoading, NavigateTo } from '@/utils/Taro-api'
import { t } from '@/locale/fanyi'
import Taro from '@tarojs/taro'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { GetIdentityUsersInput } from '@/api/proxy/identity-user-service/models'
import { PagedResultDto } from '@/shared/models/dtos'
import { IdentityUserDto } from '@/api/proxy/identity-user-service/models'

/**
 * @description 修改导航栏标题
 */
Taro.setNavigationBarTitle({ title: t('menu.warehouseSetup') })

// 存储获取的仓库信息
interface WmWarehouseItem {
    id: string
    name: string
    encode: string
    location: string
    areaSize: string
    creationTime: string
    isActivate: boolean
    dutyUserId?: string
    dutyUserName: string
}

// 分页请求参数接口
interface PagedRequestDto {
    skipCount: number
    maxResultCount: number
    filterText: string
    sorting: string
}

// 默认分页参数
const defaultParams: PagedRequestDto = {
    skipCount: 0,
    maxResultCount: 100,
    filterText: '',
    sorting: '',
}

// 菜单事件接口
interface MenuEvent {
    name: string
}

const formattedWmInfo = ref<WmWarehouseItem[]>([])
const showMore = ref<boolean[]>([])
const isAdd = ref<boolean>(false)
const isLoading = ref<boolean>(false)
const isEmpty = ref<boolean>(false)
const list = ref<{ name: string }[]>([{ name: t('menu.edit') }, { name: t('menu.delete') }])

// 存储编辑信息
const getWmEdit = ref({})

// 创建仓库服务实例
const wmWarehouseService = new WmWarehouseService()

// 创建用户服务实例
const identityUserService = new IdentityUserService()

// 编辑的仓库ID
const editId = ref<string>('')

// 控制删除确认框的显示状态
const isDelete = ref<boolean>(false)

// 当前操作的仓库ID
const currentId = ref<string>('')

const showRecycle = ref<boolean>(false)

const getRecycleList = ref([])

// 获取仓库数据
const fetchData = async (params: Partial<PagedRequestDto> = {}) => {
    const input: GetWmWarehouseInput = {
        ...defaultParams,
        ...params,
        isDeleted: false,
    }
    try {
        const result = await wmWarehouseService.getPaged(input)
        formattedWmInfo.value = result.items.map(item => ({
            ...item,
            location: item.location || '',
            areaSize: String(item.areaSize),
            creationTime: formatDate(item.creationTime || ''),
            dutyUserName: '', // 初始化空值，稍后在 fetchUserList 中填充
        }))
        isEmpty.value = false
    } catch (error) {
        isEmpty.value = true
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

const fetchUserList = async () => {
    const input: GetIdentityUsersInput = {
        maxResultCount: 1000,
        skipCount: 0,
    }
    try {
        const result = await identityUserService.getList(input)
        if (result && result.items) {
            for (const item of formattedWmInfo.value) {
                if (item.dutyUserId) {
                    item.dutyUserName =
                        (result.items.find(user => user.id === item.dutyUserId)?.surname || '') +
                        (result.items.find(user => user.id === item.dutyUserId)?.name || '')
                }
            }
        }
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

// 获取编辑数据的函数
const fetchEditData = async (id: string) => {
    const input: GetForEditInput = {
        id,
    }
    try {
        const result = await wmWarehouseService.getEditor(input)
        getWmEdit.value = result
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

// 回收站列表数据接口
// const fetchRecycleData = async (params: Partial<PagedRequestDto> = {}) => {
//     const input: GetWmWarehouseInput = {
//         ...defaultParams,
//         ...params,
//         isDeleted: true,
//     }
//     try {
//         const result = await wmWarehouseService.getPaged(input)
//         getRecycleList.value = result.items.map(item => ({
//             ...item,
//             creationTime: formatDate(item.creationTime), // 直接格式化日期
//         }))
//     } catch (error) {
//         console.error('获取数据错误:', error.response ? error.response.data : error.message)
//     }
// }

// 更新 showMore 状态的函数
const updateShowMore = (index: number, value: boolean) => {
    showMore.value[index] = value // 更新数组中的状态
}

// 选择事件处理函数的逻辑
const choose = async (id: string, event: MenuEvent) => {
    if (event.name === t('menu.edit')) {
        await showEdit(id)
    } else if (event.name === t('menu.delete')) {
        isDelete.value = true
        currentId.value = id
    }
}

// 确认删除的函数
const confirmDel = async () => {
    await wmWarehouseService.deleteById(currentId.value)
    await fetchData()
}

// 显示编辑框的函数
const showEdit = async (id: string) => {
    await fetchEditData(id)
    editVisible.value = true
    editId.value = id
}

// 刷新数据的函数
const loading = async () => {
    Loading()
    await fetchData()
    HideLoading()
}

// 格式化日期的函数
const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 控制编辑框的显示状态
const editVisible = ref<boolean>(false)

// 切换回收站显示状态
// const changeRecycle = (value: boolean) => {
//     showRecycle.value = value
// }

// 打开回收站
// const openRecycle = async () => {
//     await fetchRecycleData()
//     changeRecycle(true)
// }

// 打开详情
const openDetails = (id: string) => {
    NavigateTo(`/pages/workbench/warehouse-management/component/warehouse-details-table?id=${id}`)
}

// 页面显示时获取数据
useDidShow(async () => {
    Loading()
    await fetchData()
    await fetchUserList()
    HideLoading()
})
</script>
<style lang="scss">
.warehouse-list {
    padding: 20px 40px;
}
</style>
