<template>
    <view>
        <nut-navbar>
            <template #left>
                <view style="font-size: 16px; color: black">{{ t('text.add') }}</view>
            </template>
        </nut-navbar>
        <nut-form>
            <nut-form-item :label="t('text.name')">
                <nut-input v-model="addForm.name" type="text" :placeholder="t('text.pleaseEnterName')" />
            </nut-form-item>
            <nut-form-item :label="t('text.encode')">
                <nut-input v-model="addForm.encode" type="text" :placeholder="t('text.pleaseEnterEncode')" />
            </nut-form-item>
            <nut-form-item :label="t('text.location')">
                <nut-input v-model="addForm.location" type="text" :placeholder="t('text.pleaseEnterLocation')" />
            </nut-form-item>
            <nut-form-item :label="t('text.areaSize')">
                <nut-input-number v-model="addForm.areaSize" :button-size="30" :min="0" :max="10000" />
            </nut-form-item>
            <nut-form-item :label="t('text.dutyUser')">
                <view @click="showDutyUser = true">
                    <text>{{
                        addForm.dutyUserInfo?.surname && addForm.dutyUserInfo?.name
                            ? addForm.dutyUserInfo.surname + addForm.dutyUserInfo.name
                            : t('text.pleaseSelectDutyUser')
                    }}</text>
                </view>
            </nut-form-item>
            <nut-form-item :label="t('text.isActivate')">
                <nut-switch v-model="addForm.isActivate" />
            </nut-form-item>
            <nut-form-item :label="t('text.describe')">
                <nut-textarea v-model="addForm.describe" limit-show max-length="200" />
            </nut-form-item>
            <nut-space direction="vertical" fill>
                <nut-button style="width: 100%" @click="emit('update:addVisible', false)">{{
                    t('ui.cancel')
                }}</nut-button>
                <nut-button type="primary" style="width: 100%" @click="confirmAdd">{{ t('ui.submit') }}</nut-button>
            </nut-space>
        </nut-form>
        <nut-popup v-model:visible="showDutyUser" position="bottom" duration="0" round :style="{ height: '40%' }">
            <nut-picker
                :columns="userList"
                :title="t('text.pleaseSelectDutyUser')"
                @confirm="confirmSelectUser"
                @cancel="showDutyUser = false"
            />
        </nut-popup>
    </view>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue'
import { t } from '@/locale/fanyi'
import { WmWarehouseAreaService } from '@/api/proxy/enterprise/controller'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { IdentityUserDto } from '@/api/proxy/identity-user-service/models'
import { WmWarehouseAreaCreateDto } from '@/api/proxy/enterprise/wms/dtos'
import Taro from '@tarojs/taro'

// 添加默认导出
defineOptions({
    name: 'WarehouseAreaAdd',
})

interface AddForm {
    name: string
    encode: string
    location: string
    areaSize: number
    dutyUserId?: string
    dutyUserInfo?: IdentityUserDto
    isActivate: boolean
    describe?: string
    warehouseId?: string
}

const props = defineProps<{
    warehouseId?: string
    addVisible: boolean
}>()

const wmWarehouseAreaService = new WmWarehouseAreaService()
const identityUserService = new IdentityUserService()
const addForm = ref<AddForm>({
    name: '',
    encode: '',
    location: '',
    areaSize: 0,
    dutyUserId: '',
    dutyUserInfo: {} as IdentityUserDto,
    isActivate: false,
    describe: '',
    warehouseId: '',
})
const showDutyUser = ref(false)
const userList = ref<Array<{ text: string; value: string; id: string }>>([])
const emit = defineEmits<{
    (e: 'update:addVisible', value: boolean): void
    (e: 'refresh'): void
}>()

const initForm = () => {
    addForm.value = {
        name: '',
        encode: '',
        location: '',
        areaSize: 0,
        dutyUserId: '',
        dutyUserInfo: {} as IdentityUserDto,
        isActivate: false,
        describe: '',
        warehouseId: props.warehouseId || '',
    }
}

const fetchUserList = async () => {
    try {
        const defaultParams = {
            skipCount: 0,
            maxResultCount: 100,
            filterText: '',
            sorting: 'userName',
        }
        const response = await identityUserService.getList(defaultParams)
        if (response && Array.isArray(response.items)) {
            userList.value = response.items.map(item => ({
                text: item.userName + '：' + (item.surname || '') + (item.name || ''),
                value: item.id,
                id: item.id,
            }))
        } else {
            console.error('获取用户列表出错:', response)
        }
    } catch (error) {
        console.error('获取用户列表失败:', error)
    }
}

const confirmSelectUser = ({ selectedOptions }: { selectedOptions: any[] }) => {
    if (selectedOptions && selectedOptions.length > 0) {
        const selectedUser = selectedOptions[0]
        addForm.value.dutyUserId = selectedUser.value
        identityUserService
            .get(selectedUser.value)
            .then(result => {
                addForm.value.dutyUserInfo = result
            })
            .catch(() => {
                const nameParts = selectedUser.text.split('：')
                addForm.value.dutyUserInfo = {
                    id: selectedUser.value,
                    name: nameParts.length > 1 ? nameParts[1] : '',
                    surname: nameParts.length > 1 ? nameParts[0] : '',
                } as IdentityUserDto
            })
    }
    showDutyUser.value = false
}

const confirmAdd = async () => {
    const input: WmWarehouseAreaCreateDto = {
        ...addForm.value,
        isActivate: addForm.value.isActivate || false,
        warehouseId: props.warehouseId,
    }

    try {
        await wmWarehouseAreaService.create(input)
        emit('update:addVisible', false)
        emit('refresh')
        Taro.showToast({
            title: t('text.submitSuccess'),
            icon: 'success',
        })
        initForm()
    } catch (error) {
        console.error(error)
    }
}

watch(
    () => props.addVisible,
    newValue => {
        if (newValue) {
            initForm()
            fetchUserList()
        }
    },
    { immediate: true },
)

watch(
    () => showDutyUser.value,
    async newValue => {
        if (newValue) {
            await fetchUserList()
        }
    },
)
</script>

<style scoped lang="scss"></style>
