<template>
    <view class="out-warehouse-details">
        <nut-tabs v-model="tabActive" title-active-color="#0066ff" title-inactive-color="#616161">
            <nut-tab-pane :title="t('text.baseInfo')">
                <DetailsTable :outWarehouseBillId="outWarehouseBillId" />
            </nut-tab-pane>
            <nut-tab-pane :title="t('text.outWarehouseItemDetails')">
                <view v-if="!showItemDetails">
                    <OutWarehouseItemsTable
                        :outWarehouseBillId="outWarehouseBillId"
                        ref="itemsTableRef"
                        @showDetails="handleShowDetails"
                    />
                </view>
                <view v-else class="item-details-container">
                    <view class="details-header">
                        <nut-button size="small" type="primary" @click="showItemDetails = false"> {{ t('text.backToList') }} </nut-button>
                        <text class="header-title"> {{ t('text.outWarehouseItemDetails') }} </text>
                    </view>
                    <OutWarehouseItemDetails :itemId="currentItemId" ref="itemDetailsRef" />
                </view>
            </nut-tab-pane>
        </nut-tabs>
    </view>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { ref, nextTick } from 'vue'
import { t } from '@/locale/fanyi'
import { Toast } from '@/utils/Taro-api'
import DetailsTable from './component/out-warehouse-details-table.vue'
import OutWarehouseItemsTable from './component/out-warehouse-items-table.vue'
import OutWarehouseItemDetails from './component/out-warehouse-item-details.vue'
import { useDidShow } from '@/hooks/component/hooks'

// 当前激活的选项卡
const tabActive = ref(0)

// 出库单ID
const outWarehouseBillId = ref('')

// 当前查看的子项ID
const currentItemId = ref('')

// 引用子组件
const itemsTableRef = ref<{ refresh: () => void } | null>(null)
const itemDetailsRef = ref<{ fetchData: (id: string) => void } | null>(null)

// 子项详情显示控制
const showItemDetails = ref(false)

/**
 * 处理显示出库子项详情
 */
const handleShowDetails = (id: string) => {
    currentItemId.value = id
    showItemDetails.value = true

    // 手动触发子项详情刷新
    nextTick(() => {
        if (itemDetailsRef.value && typeof itemDetailsRef.value.fetchData === 'function') {
            itemDetailsRef.value.fetchData(id)
        }
    })
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    // 获取路由参数
    const id = Taro.getCurrentInstance()?.router?.params?.id

    if (!id) {
        console.error('ID不存在')
        Toast('参数错误', { icon: 'error' })
        return
    }

    // 设置ID并触发组件更新
    outWarehouseBillId.value = id

    // 手动刷新子组件数据
    nextTick(() => {
        if (itemsTableRef.value && typeof itemsTableRef.value.refresh === 'function') {
            itemsTableRef.value.refresh()
        }
    })
})
</script>

<style lang="scss">
.out-warehouse-details {
    padding: 16px;
    background-color: #f5f5f5;
    min-height: 100vh;

    :deep(.nut-tabs) {
        .nut-tabs__titles {
            background-color: #fff;
            padding: 8px 0;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .nut-tabs__content {
            padding-top: 10px;
        }

        .nut-tab-pane {
            padding: 0;
        }

        .nut-tabs__titles-item {
            font-size: 15px;

            &.active {
                font-weight: 500;
            }
        }
    }
}

.item-details-container {
    background-color: #fff;

    .details-header {
        display: flex;
        align-items: center;
        padding: 10px 16px;
        border-bottom: 1px solid #f0f0f0;

        .header-title {
            margin-left: 20px;
            font-size: 16px;
            font-weight: 500;
        }
    }
}
</style>
