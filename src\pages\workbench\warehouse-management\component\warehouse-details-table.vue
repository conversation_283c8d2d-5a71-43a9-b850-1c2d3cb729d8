<template>
    <view>
        <nut-tabs v-model="tabValue" :animated-time="0">
            <nut-tab-pane :title="t('text.warehouseInfo')" pane-key="1">
                <view>
                    <nut-cell :title="t('text.warehouseName')" :desc="infoList?.name"></nut-cell>
                    <nut-cell :title="t('text.warehouseCode')" :desc="infoList?.encode"></nut-cell>
                    <nut-cell :title="t('text.warehouseLocation')" :desc="infoList?.location"></nut-cell>
                    <nut-cell :title="t('text.warehouseAreaSize')" :desc="String(infoList?.areaSize)"></nut-cell>
                    <nut-cell
                        :title="t('text.warehouseDutyUser')"
                        :desc="
                            dutyUserInfo?.surname && dutyUserInfo?.name ? dutyUserInfo.surname + dutyUserInfo.name : '-'
                        "
                    ></nut-cell>
                    <nut-cell :title="t('text.warehouseLocation')" :desc="infoList?.location"></nut-cell>
                    <nut-cell :title="t('text.describe')" :desc="infoList?.describe"></nut-cell>
                    <nut-cell :title="t('text.creationTime')">
                        <template #desc>
                            {{ dayjs(infoList?.creationTime).format('YYYY-MM-DD HH:mm:ss') }}
                        </template>
                    </nut-cell>
                </view>
            </nut-tab-pane>
            <nut-tab-pane :title="t('text.warehouseArea')" pane-key="2">
                <warehouse-area-table :warehouse-id="infoList?.id" />
            </nut-tab-pane>
        </nut-tabs>
    </view>
</template>

<script setup lang="ts">
import { useDidShow } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import { WmWarehouseService } from '@/api/proxy/enterprise/controller/wm-warehouse.service'
import { WmWarehouseDetailDto } from '@/api/proxy/enterprise/wms/dtos'
import { ref } from 'vue'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import WarehouseAreaTable from './warehouse-area-table.vue'
import dayjs from 'dayjs'

Taro.setNavigationBarTitle({ title: t('menu.warehouseDetails') })

interface DutyUserInfo {
    surname: string
    name: string
}

const wmWarehouseService = new WmWarehouseService()
const identityUserService = new IdentityUserService()
const infoList = ref<WmWarehouseDetailDto>()
const tabValue = ref('1')
const dutyUserInfo = ref<DutyUserInfo>()

const fetchData = async () => {
    const id = Taro.useRouter().params.id
    if (id) {
        try {
            const result: WmWarehouseDetailDto = await wmWarehouseService.getById(id)
            infoList.value = result
        } catch (error) {
            console.error('获取数据错误:', error.response ? error.response.data : error.message)
        }
    }
}

const fetchUserInfo = async () => {
    try {
        if (infoList.value?.dutyUserId) {
            const result = await identityUserService.get(infoList.value?.dutyUserId)
            if (result.surname && result.name) {
                dutyUserInfo.value = {
                    surname: result.surname,
                    name: result.name,
                }
            }
        }
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

useDidShow(async () => {
    await fetchData()
    await fetchUserInfo()
})
</script>

<style scoped></style>
