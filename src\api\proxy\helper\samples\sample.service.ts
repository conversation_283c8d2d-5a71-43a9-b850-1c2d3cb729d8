import type { GetSampleInput, SampleDto } from './models'
import type { PagedResultDto } from '/@/api/app/model/baseModel'
import { defHttp } from '/@/utils/http/axios'
import { RequestOptions } from '/@/utils/http/axios/axiosModels'
import type { GetForEditInput, NodeSelectDataDto, NodeTreeDataDto, ReturnResult } from '../shared/models'

export class SampleService {
	apiName = 'Helper'

	get = (options?: RequestOptions) =>
		defHttp.request<SampleDto>(
			{
				method: 'GET',
				url: '/api/Helper/sample',
			},
			options
		)

	getAuthorized = (options?: RequestOptions) =>
		defHttp.request<SampleDto>(
			{
				method: 'GET',
				url: '/api/Helper/sample/authorized',
			},
			options
		)

	getById = (input: GetForEditInput, options?: RequestOptions) =>
		defHttp.request<ReturnResult>(
			{
				method: 'POST',
				url: '/api/Helper/sample/id',
				data: input,
			},
			options
		)

	getNodeSelect = (input: NodeSelectDataDto, options?: RequestOptions) =>
		defHttp.request<ReturnResult>(
			{
				method: 'POST',
				url: '/api/Helper/sample/node-select',
				data: input,
			},
			options
		)

	getNodeTree = (input: NodeTreeDataDto, options?: RequestOptions) =>
		defHttp.request<ReturnResult>(
			{
				method: 'POST',
				url: '/api/Helper/sample/node-tree',
				data: input,
			},
			options
		)

	getPaged = (input: GetSampleInput, options?: RequestOptions) =>
		defHttp.request<PagedResultDto<SampleDto>>(
			{
				method: 'POST',
				url: '/api/Helper/sample/paged',
				data: input,
			},
			options
		)
}
