import { usePermissionStoreWithOut } from '@/stores/modules/permission'
import { isArray } from '@/utils/other/is'
import { ACLCanType } from './acl.type'

const permissionStore = usePermissionStoreWithOut()
/**
 * 根据权限判断
 * @param permissions
 * @returns
 */
export function isGranted(permissions?: ACLCanType): boolean {
    return hasPermission(permissions)
}

/**
 * 根据角色判断
 * @param roles
 * @returns
 */
export function isAuthority(roles?: ACLCanType): boolean {
    return hasRole(roles)
}

/**
 * 根据权限判断
 * @param permissions
 * @returns
 */
export function hasPermission(permissions?: ACLCanType): boolean {
    if (!permissions || permissions.length == 0) {
        return false
    }
    if (!isArray(permissions)) {
        return permissionStore.getGrantedPolicies.includes(permissions)
    } else {
        // 传入数组时，其中一个存在则有权限
        const result = permissions.some(item => permissionStore.getGrantedPolicies.includes(item))
        return result
    }
}

/**
 * 根据角色判断
 * @param roles
 * @returns
 */
export function hasRole(roles?: ACLCanType): boolean {
    if (!roles || roles.length == 0) {
        return false
    }
    if (!isArray(roles)) {
        return permissionStore.getGrantedPolicies.includes(roles)
    } else {
        // 传入数组时，其中一个存在则有权限
        const result = roles.some(item => permissionStore.getGrantedPolicies.includes(item))
        return result
    }
}
