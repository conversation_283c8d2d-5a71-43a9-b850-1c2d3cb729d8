import { Toast } from '@/utils/Taro-api'
import { BaseModalDto } from './models/BaseModalDto'

export function useAppModal() {
    let setModalProps: Function
    let submitFormFun: Function

    function getService(par: any) {
        return
    }

    //注册模态窗口
    function register(setProps: Function) {
        setModalProps = setProps
    }

    // 模态窗口点击确认提交事件
    function submitFun(e: { submit: Function }) {
        if (e?.submit) {
            // 保存提交事件
            submitFormFun = e.submit
        } else {
            // 触发提交
            submitFormFun().then(handleSubmit)
        }
    }

    // 处理提交事件
    function handleSubmit(res: any | undefined) {
        if (res) {
            Toast('提交成功', { duration: 400 })
            closeModal()
        } else {
            Toast('提交失败')
        }
    }

    // 打开模态窗口
    function openModal(props: BaseModalDto) {
        setModalProps({
            visible: true,
            ...props,
        })
    }

    // 关闭模态窗口
    function closeModal() {
        setModalProps({
            visible: false,
        })
    }

    return {
        register,
        openModal,
        closeModal,
        submitFun,
        getService,
    }
}
