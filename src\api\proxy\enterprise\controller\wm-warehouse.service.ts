import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { GetForEditInput, NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetWmWarehouseForEditorOutput,
    GetWmWarehouseInput,
    WmWarehouseCreateDto,
    WmWarehouseDetailDto,
    WmWarehouseEditDto,
    WmWarehouseListDto,
} from '../wms/dtos/models'

export class WmWarehouseService {
    apiName = 'EnterpriseService'

    create = (input: WmWarehouseCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-warehouse',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-warehouse/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmWarehouseDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-warehouse/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmWarehouseDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmWarehouseForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetWmWarehouseInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmWarehouseListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse',
                params: {
                    productParentId: input.productParentId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-warehouse/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    update = (input: WmWarehouseEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-warehouse',
                data: input,
            },
            options,
        )
}
