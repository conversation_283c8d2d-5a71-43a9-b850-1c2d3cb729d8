// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动
// The axios configuration can be changed according to the project, just change the file, other files can be left unchanged

import type { AxiosResponse } from 'axios'
import type { AxiosTransform, CreateAxiosOptions } from './axiosTransform'
import { VAxios } from './Axios'
import { deepMerge } from '@/utils'
import { ContentTypeEnum } from '@/enums/httpEnum'
import { checkStatus } from './checkStatus'
import { useAuthStore } from '@/stores/modules/auth'
import { RequestOptions, Result } from './model/axios'
import { useLocaleStoreWithOut } from '@/stores/modules/locale'
import Taro from '@tarojs/taro'

const authHost = AUTH_URL_HOST
const apiHost = API_URL_HOST
const tenantKey = TenantKey

/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform: AxiosTransform = {
    /**
     * @description: 处理请求数据。如果数据不是预期格式，可直接抛出错误
     */
    transformRequestHook: (res: AxiosResponse<Result>, options: RequestOptions) => {
        const { isReturnNativeResponse } = options
        // 是否返回原生响应头 比如：需要获取响应头时使用该属性
        if (isReturnNativeResponse) {
            return res
        }
        const { data } = res
        return data
    },

    // 请求之前处理config
    beforeRequestHook: config => {
        return config
    },

    /**
     * @description: 请求拦截器处理
     */
    requestInterceptors: (config, options) => {
        // 请求之前处理config
        if (!config.baseURL) {
            config.baseURL = config.url?.startsWith('/api')
                ? apiHost
                : config.url?.startsWith('/connect')
                  ? authHost
                  : config.url?.startsWith('/auth')
                    ? authHost
                    : config.url?.startsWith('/Abp')
                      ? authHost
                      : config.baseURL
        }
        config.headers = config.headers ?? {}
        // 删除值为undefined的key
        for (const key in config.params) {
            if (config.params[key] == undefined) {
                delete config.params[key]
            }
        }

        const authStore = useAuthStore()
        if (authStore.getToken && (config as any)?.requestOptions?.withToken !== false) {
            // jwt token
            ;(config as any).headers.Authorization = options.authenticationScheme
                ? `${options.authenticationScheme} ${authStore.getToken}`
                : authStore.getToken
        }
        //设置租户
        if ((config as any)?.requestOptions?.withTenant !== false && authStore.getTenant) {
            const tenant = authStore.getTenant
            if (tenant) {
                ;(config as any).headers[tenantKey] = tenant.tenantId
            }
        }
        // 设置语言
        if ((config as any)?.requestOptions?.withAcceptLanguage !== false) {
            const localeStore = useLocaleStoreWithOut()
            config.headers['Accept-Language'] = localeStore.getLocale
        }
        return config
    },

    /**
     * @description: 响应拦截器处理
     */
    responseInterceptors: (res: AxiosResponse<any>) => {
        checkStatus(res.status, res.data)
        return res
    },

    /**
     * @description: 响应错误处理
     */
    responseInterceptorsCatch: (_, error: any) => {
        // return error;
        // console.log('responseInterceptorsCatch');
        // const resMessage = checkResponse(error.response);
        // if (resMessage) {
        //   // Toast(resMessage, { duration: 3000 })
        // }
        return Promise.reject(error)
    },
}

function createAxios(opt?: Partial<CreateAxiosOptions>) {
    return new VAxios(
        deepMerge(
            {
                // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Authentication#authentication_schemes
                // authentication schemes，e.g: Bearer
                // authenticationScheme: 'Bearer',
                authenticationScheme: 'Bearer',
                timeout: 30 * 1000,
                // 基础接口地址
                // baseURL: 'https://api.yudi.cn',
                headers: { 'Content-Type': ContentTypeEnum.JSON },
                // 如果是form-data格式
                // headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
                // 数据处理方式
                transform,
                xsrfCookieName: 'XSRF-TOKEN',
                // ASP.NET Core
                xsrfHeaderName: 'RequestVerificationToken',
                //配置项，下面的选项都可以在独立的接口请求中覆盖
                requestOptions: {
                    // 默认将prefix 添加到url
                    // joinPrefix: true,
                    // 是否返回原生响应头 比如：需要获取响应头时使用该属性
                    isReturnNativeResponse: false,
                    // 需要对返回数据进行处理
                    isTransformRequestResult: false,
                    isTransformResponse: true,
                    // post请求的时候添加参数到url
                    joinParamsToUrl: false,
                    // 格式化提交参数时间
                    formatDate: true,
                    // 消息提示类型
                    errorMessageMode: 'message',
                    // 接口地址
                    apiUrl: apiHost,
                    // 接口拼接地址
                    // urlPrefix: urlPrefix,
                    //  是否加入时间戳
                    joinTime: true,
                    // 忽略重复请求
                    ignoreCancelToken: true,
                    // 是否携带token
                    withToken: true,
                    // 是否携带 Accept-Language 标头
                    withAcceptLanguage: true,
                    // 是否携带Tenant
                    withTenant: true,
                    retryRequest: {
                        isOpenRetry: true,
                        count: 5,
                        waitTime: 100,
                    },
                },
            },
            opt || {},
        ),
    )
}

export const defHttp = createAxios({})
