<template>
    <view class="production-board">
        <nut-tabs
            v-model="tabsValue"
            :animated-time="0"
            :title-scroll="true"
            title-gutter="10"
            :auto-height="true"
            class="board-tabs"
        >
            <nut-tab-pane :title="$t('ui.productionBarChart')" pane-key="1">
                <view v-if="isActive('1')" class="tab-content">
                    <ProductionEquipmentRanking />
                    <ProductionProgramRanking />
                </view>
            </nut-tab-pane>
            <nut-tab-pane :title="$t('Platform.ProductionProgress')" pane-key="2">
                <view v-if="isActive('2')" class="tab-content">
                    <ProductionProgress />
                </view>
            </nut-tab-pane>
            <nut-tab-pane :title="$t('Platform.ProductionStatistics')" pane-key="3">
                <view class="tab-content">
                    <ListScreen
                        is-show-select-factory
                        is-show-select-time
                        :is-show-search="false"
                        @update:show-menu="handleShowMenu"
                        @update:screen-factory="handleSelectFactory"
                        @update:selected-time="handleSelectTime"
                    />
                    <ProductionStatistics
                        v-if="isActive('3') && showCanvas"
                        :prop-factory-model-id="factoryId"
                        :start-time="startTime"
                        :end-time="endTime"
                    />
                </view>
            </nut-tab-pane>
        </nut-tabs>
    </view>
</template>

<script lang="ts" setup>
import { useDidShow } from '@/hooks/component/hooks'
import { computed, ref } from 'vue'
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import ProductionEquipmentRanking from '@/pages/workbench/device-list/data-view/production-equipment-ranking.vue'
import ProductionProgress from '../device-list/data-view/production-progress.vue'
import ProductionProgramRanking from '../device-list/data-view/production-program-ranking.vue'
import ProductionStatistics from '../device-list/data-view/production-statistics.vue'

/**
 * @description 修改导航栏标题
 */
Taro.setNavigationBarTitle({ title: t('menu.productionKanban') })

const tabsValue = ref<string>('1')
const showCanvas = ref<boolean>(true)
const factoryId = ref<string>('')
const startTime = ref<string>('')
const endTime = ref<string>('')
const isActive = computed(() => {
    return (key: string) => {
        return tabsValue.value === key
    }
})

/**
 * @description 打开菜单
 */
const handleShowMenu = (isShowMenu: boolean) => {
    showCanvas.value = !isShowMenu
}

/**
 *@description 选择工厂节点
 */
const handleSelectFactory = (id: string) => {
    factoryId.value = id
}

/**
 * @description 选择日期单元
 */
const handleSelectTime = (time: string[]) => {
    startTime.value = time[0]
    endTime.value = time[1]
}

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {})
</script>

<style lang="scss">
.production-board {
    min-height: 100vh;
    background: #f8f9fa;
    padding-top: 12px;
}

.board-tabs {
    --nutui-tabs-titles-item-active-color: #4361ee;
    --nutui-tabs-horizontal-tab-line-color: #4361ee;
    --nutui-tabs-titles-background-color: #ffffff;
    --nutui-tabs-titles-item-font-size: 15px;
    --nutui-tabs-horizontal-titles-height: 50px;

    .nut-tab-pane {
        transition: opacity 0.3s, transform 0.3s;
    }
}

.tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
