import { getAbpAppConfig } from '@/api/app/appConfig'
import { useI18n } from '@/hooks/web/i18n/i18nBaseService'
import { useLocaleStore } from '@/stores/modules/locale'
export type LocaleType = 'es' | 'en' | 'zh-Hans' | 'tr' | 'sl' | 'de-DE'
import localMessages from './lang'

/**
 * 设置语言环境
 * @param locale
 * @returns
 */
export function setLocaleMessage(locale: LocaleType) {
    const localMessage = localMessages[locale]
    const localeStore = useLocaleStore()
    const messages = { ...localMessage, ...localeStore.getDictionary }
    const { setLocaleMessage } = useI18n()
    setLocaleMessage(locale, messages)
    return locale
}

/**
 * 改变当前语言
 * @param locale 语言
 */
export function changeLocale(locale: LocaleType) {
    const localeStore = useLocaleStore()
    localeStore.setLocale(locale)
    getAbpAppConfig()
}
