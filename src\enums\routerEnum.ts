export enum NAVIGATE_TYPE {
    NAVIGATE_TO = 'navigateTo',
    REDIRECT_TO = 'redirectTo',
    RE_LAUNCH = 'reLaunch',
    SWITCH_TAB = 'switchTab',
    NAVIGATE_BACK = 'navigateBack',
}

export const NAVIGATE_TYPE_LIST = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']

export const HOME_PAGE = '/pages/index/index'
export const LOGIN_PAGE = '/pages/subpackages/login/index'
export const NOT_FOUND_PAGE = '/pages/notFound/404'
