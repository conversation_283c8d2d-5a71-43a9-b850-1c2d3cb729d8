<template>
    <view>
        <nut-empty v-if="hasError || materialFlowLogList.length === 0" :description="t('text.noMaterialFlowLogData')" />
        <view v-else>
            <nut-table :columns="columns" :data="materialFlowLogList" />
        </view>
    </view>
</template>

<script setup lang="ts">
import { WmMaterialFlowLogService } from '@/api/proxy/enterprise/controller'
import { WmMaterialFlowLogDto } from '@/api/proxy/enterprise/wms/dtos/models'
import { useDidShow } from '@tarojs/taro'
import { ref, watch } from 'vue'
import { t } from '@/locale/fanyi'

const props = defineProps<{
    materialId: string
}>()

const wmMaterialFlowLogService = new WmMaterialFlowLogService()
const materialFlowLogList = ref<WmMaterialFlowLogDto[]>([])
const hasError = ref(false)
const columns = [
    { title: t('text.materialName'), key: 'materialName' },
    { title: t('text.quantity'), key: 'quantity' },
    { title: t('text.type'), key: 'type' },
    { title: t('text.storageType'), key: 'storageType' },
    { title: t('text.outboundType'), key: 'outboundType' },
    { title: t('text.creationTime'), key: 'creationTime' },
    { title: t('text.describe'), key: 'describe' },
]
const fetchData = async () => {
    if (!props.materialId) {
        console.warn('无法获取物料日志')
        return
    }
    try {
        const result = await wmMaterialFlowLogService.getPaged({
            materialId: props.materialId,
        })
        materialFlowLogList.value = result.items || []
        console.log(materialFlowLogList.value)
    } catch (error) {
        console.error('获取物料流水日志数据失败', error)
        hasError.value = true
    }
}

watch(
    () => props.materialId,
    () => {
        if (props.materialId) {
            fetchData()
        }
    },
    { immediate: true },
)

useDidShow(() => {
    if (props.materialId) {
        fetchData()
    }
})
</script>

<style scoped></style>
