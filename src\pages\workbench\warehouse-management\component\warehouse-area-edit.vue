<template>
    <view>
        <nut-navbar>
            <template #left>
                <view style="font-size: 16px; color: black">{{ t('text.edit') }}</view>
            </template>
        </nut-navbar>
        <nut-form>
            <nut-form-item :label="t('text.name')">
                <nut-input v-model="editForm.name" type="text" :placeholder="t('text.pleaseEnterName')" />
            </nut-form-item>
            <nut-form-item :label="t('text.encode')">
                <nut-input v-model="editForm.encode" type="text" :placeholder="t('text.pleaseEnterEncode')" />
            </nut-form-item>
            <nut-form-item :label="t('text.location')">
                <nut-input v-model="editForm.location" type="text" :placeholder="t('text.pleaseEnterLocation')" />
            </nut-form-item>
            <nut-form-item :label="t('text.areaSize')">
                <nut-input-number v-model="editForm.areaSize" :button-size="30" :min="0" :max="10000" />
            </nut-form-item>
            <nut-form-item :label="t('text.dutyUser')">
                <view @click="showDutyUser = true">
                    <text>{{
                        editForm.dutyUserInfo?.surname && editForm.dutyUserInfo?.name
                            ? editForm.dutyUserInfo.surname + editForm.dutyUserInfo.name
                            : t('text.pleaseSelectDutyUser')
                    }}</text>
                </view>
            </nut-form-item>
            <nut-form-item :label="t('text.isActivate')">
                <nut-switch v-model="editForm.isActivate" />
            </nut-form-item>
            <nut-form-item :label="t('text.describe')">
                <nut-textarea v-model="editForm.describe" limit-show max-length="200" />
            </nut-form-item>
            <nut-space direction="vertical" fill>
                <nut-button style="width: 100%" @click="emit('update:editVisible', false)">{{
                    t('ui.cancel')
                }}</nut-button>
                <nut-button type="primary" style="width: 100%" @click="confirmEdit">{{ t('ui.submit') }}</nut-button>
            </nut-space>
        </nut-form>
        <nut-popup v-model:visible="showDutyUser" position="bottom" duration="0" round :style="{ height: '40%' }">
            <nut-picker
                :columns="userList"
                :title="t('text.pleaseSelectDutyUser')"
                @confirm="confirmSelectUser"
                @cancel="showDutyUser = false"
                :defaultValue="[editForm.dutyUserId]"
            />
        </nut-popup>
    </view>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue'
import { t } from '@/locale/fanyi'
import { WmWarehouseAreaService } from '@/api/proxy/enterprise/controller'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { IdentityUserDto } from '@/api/proxy/identity-user-service/models'
import { WmWarehouseAreaEditDto } from '@/api/proxy/enterprise/wms/dtos'
import Taro from '@tarojs/taro'

defineOptions({
    name: 'WarehouseAreaEdit',
})

interface EditForm {
    name: string
    encode: string
    location: string
    areaSize: number
    dutyUserId?: string
    dutyUserInfo?: IdentityUserDto
    isActivate?: boolean
    describe?: string
}

const props = defineProps<{
    id: string
    editVisible: boolean
}>()

const wmWarehouseAreaService = new WmWarehouseAreaService()
const identityUserService = new IdentityUserService()
const editForm = ref<EditForm>({
    name: '',
    encode: '',
    location: '',
    areaSize: 0,
    dutyUserId: '',
    dutyUserInfo: {} as IdentityUserDto,
    isActivate: false,
    describe: '',
})
const showDutyUser = ref(false)
const userList = ref<Array<{ text: string; value: string; id: string }>>([])
const emit = defineEmits<{
    (e: 'update:editVisible', value: boolean): void
    (e: 'refresh'): void
}>()

const fetchEditData = async () => {
    try {
        const result = await wmWarehouseAreaService.getEditor({ id: props.id })
        editForm.value = {
            ...result.wmWarehouseArea,
            isActivate: result.wmWarehouseArea.isActivate !== undefined ? result.wmWarehouseArea.isActivate : false,
        }
    } catch (error) {
        console.error(error)
    }
}

const fetchUserInfo = async () => {
    try {
        if (editForm.value.dutyUserId) {
            const result = await identityUserService.get(editForm.value.dutyUserId)
            editForm.value.dutyUserInfo = result
        }
    } catch (error) {
        console.error(error)
    }
}

const fetchUserList = async () => {
    try {
        const defaultParams = {
            skipCount: 0,
            maxResultCount: 100,
            filterText: '',
            sorting: 'userName',
        }
        const response = await identityUserService.getList(defaultParams)
        if (response && Array.isArray(response.items)) {
            userList.value = response.items.map(item => ({
                text: item.userName + '：' + (item.surname || '') + (item.name || ''),
                value: item.id,
                id: item.id,
            }))
        } else {
            console.error('获取用户列表出错:', response)
        }
    } catch (error) {
        console.error('获取用户列表失败:', error)
    }
}

const confirmSelectUser = ({ selectedOptions }: { selectedOptions: any[] }) => {
    if (selectedOptions && selectedOptions.length > 0) {
        const selectedUser = selectedOptions[0]
        editForm.value.dutyUserId = selectedUser.value
        identityUserService
            .get(selectedUser.value)
            .then(result => {
                editForm.value.dutyUserInfo = result
            })
            .catch(() => {
                const nameParts = selectedUser.text.split('：')
                editForm.value.dutyUserInfo = {
                    id: selectedUser.value,
                    name: nameParts.length > 1 ? nameParts[1] : '',
                    surname: nameParts.length > 1 ? nameParts[0] : '',
                } as IdentityUserDto
            })
    }
    showDutyUser.value = false
}

const confirmEdit = async () => {
    const input: WmWarehouseAreaEditDto = {
        ...editForm.value,
        isActivate: editForm.value.isActivate || false,
    }
    try {
        await wmWarehouseAreaService.update(input)
        emit('update:editVisible', false)
        Taro.showToast({
            title: t('text.submitSuccess'),
            icon: 'success',
        })
        emit('refresh')
    } catch (error) {
        console.error(error)
    }
}

watch(
    () => props.id,
    async () => {
        if (props.editVisible) {
            await fetchEditData()
            await fetchUserInfo()
            await fetchUserList()
        }
    },
    { immediate: true },
)

watch(
    () => showDutyUser.value,
    async newValue => {
        if (newValue) {
            await fetchUserList()
        }
    },
)
</script>

<style scoped lang="scss"></style>
