<template>
    <view style="width: 90vw; height: 60vh" v-show="showCanvas">
        <nut-empty v-if="isFetchError" description="Error" />
        <EChart v-else ref="canvas" style="width: 100%; height: 100%" />
    </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import Taro from '@tarojs/taro'
import EChart from './echarts/index'
import { useDidShow } from '@/hooks/component/hooks'
import { t } from '@/locale/fanyi'
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { DeviceOptModeRate, GetDeviceDataInput } from '@/api/proxy/data-view/models'
import { Toast } from '@/utils/Taro-api'

export interface DeviceUseRate {
    deviceName?: string
    powerOnRate: number
    utilizationRate: number
    spareRate: number
    failureRate: number
    time?: string
}

const props = defineProps({
    datePicker: { type: String, required: true },
    isDevice: { type: Boolean, default: false },
    deviceId: { type: String, default: null },
    factoryModelId: { type: String, default: null },
})

const canvas = ref<any>(null)
const chartOption = ref<any>()
const datavDataFlowService = new DatavDataFlowService()
const showCanvas = ref<boolean>(true)
const isFetchError = ref<boolean>(false)

const buildOption = (input: DeviceOptModeRate, colors: string[]) => {
    const datas: { value: number; name: string }[] = []
    for (let key of Object.keys(input)) {
        switch (key) {
            case 'autoRate':
            case 'editRate':
            case 'handleRate':
            case 'jogRate':
            case 'mdiRate':
            case 'unavailableRate':
            case 'unknownRate':
            case 'zrnRate': {
                datas.push({ value: input[key], name: t(`Platform.${key}`) })
                break
            }
            default: {
                break
            }
        }
    }
    const option = {
        title: {
            text: t('Platform.DeviceWorkModeRate'),
            // subtext: 'Fake Data',
            left: 'center',
            top: '8%',
        },
        // 鼠标提示信息
        tooltip: {
            formatter(params: { seriesName: string; marker: any; data: { name: string; value: number } }) {
                return `${params.marker}${t(`${params.data.name}`)}${params.data.value}%`
            },
        },
        legend: {
            show: false,
        },
        color: colors,
        series: [
            {
                name: t('Platform.DeviceWorkModeRate'),
                type: 'pie',
                radius: ['0%', '90%'],
                center: ['50%', '60%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 5,
                    borderColor: '#fff',
                    borderWidth: 2,
                },
                label: {
                    show: true,
                    position: 'inside',
                    formatter: (params: { value: number }) => {
                        return params.value === 0 ? '' : `${params.value}%`
                    },
                    fontSize: 14,
                    fontWeight: 'bold',
                    color: '#fff',
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 40,
                        fontWeight: 'bold',
                    },
                },
                labelLine: {
                    show: false,
                },
                data: datas,
            },
        ],
    }
    return option
}

/**
 * @description 获取列表
 */
const fetchData = async () => {
    try {
        Taro.showLoading({ title: t('WeChat.LoadingWithThreeDot') })
        const input: GetDeviceDataInput = {
            deviceId: props.deviceId,
            factoryModelId: props.factoryModelId,
            dateTime: props.datePicker,
        }
        const result = await datavDataFlowService.getDeviceModeRateByInput(input)
        chartOption.value = buildOption(result.totalRate, result.colors)
        Taro.hideLoading()
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
        isFetchError.value = true
    }
}

/**
 * @description 渲染图表
 */
const renderChart = () => {
    const echartComponentInstance: any = canvas.value // 组件实例
    Taro.nextTick(() => {
        if (!echartComponentInstance) return
        // 初始化图表
        echartComponentInstance.refresh(chartOption.value).then(myChart => {
            myChart.showLoading()
            /** 异步更新图表数据 */
            setInterval(() => {
                myChart.setOption(chartOption.value) // myChart 即为 echarts 实例，可使用的实例方法，具体可参考 echarts 官网
                myChart.hideLoading()
            }, 1000)
        })
    })
}

watch(
    () => props.datePicker,
    async () => {
        if (!props.datePicker) {
            return
        }
        await fetchData()
        renderChart()
    },
    { deep: true, immediate: true },
)

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {})
</script>
