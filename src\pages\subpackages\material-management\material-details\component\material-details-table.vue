<template>
    <view>
        <nut-cell :title="t('text.materialName')" :desc="detailsList.name"></nut-cell>
        <nut-cell :title="t('text.materialCode')" :desc="detailsList.encode"></nut-cell>
        <nut-cell :title="t('text.materialmodel')" :desc="detailsList.model"></nut-cell>
        <nut-cell :title="t('text.materialSpecification')" :desc="detailsList.specification"></nut-cell>
        <nut-cell :title="t('text.materialtype')" :desc="detailsList.type"></nut-cell>
        <nut-cell :title="t('text.safetyStock')" :desc="detailsList.safeStock"></nut-cell>
        <nut-cell :title="t('text.minStock')" :desc="String(detailsList.minStock)"></nut-cell>
        <nut-cell :title="t('text.maxStock')" :desc="String(detailsList.maxStock)"></nut-cell>
        <nut-cell :title="t('text.measureUnitName')" :desc="detailsList.unitName"></nut-cell>
        <nut-cell :title="t('text.supplierName')">
            <template #desc>
                <nut-space :gap="6">
                    <nut-tag v-for="supplier in supplierList" :key="supplier.value" type="primary">
                        {{ supplier.label }}
                    </nut-tag>
                </nut-space>
            </template>
        </nut-cell>
        <nut-cell :title="t('text.isEnable')">
            <template #desc>
                <nut-tag :color="detailsList.isActivate ? '#30D479' : '#FF0000'">
                    {{ $t(detailsList.isActivate ? 'Helper.Enable' : 'Helper.Disable') }}
                </nut-tag>
            </template>
        </nut-cell>
    </view>
</template>

<script setup lang="ts">
import { useDidShow } from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import { NodeSelectDataDto } from '@/api/proxy/helper/shared/models'
import { WmMaterialDetailDto } from '@/api/proxy/enterprise/wms/dtos'

interface TableData {
    detailsList: WmMaterialDetailDto
    supplierList?: NodeSelectDataDto[] // 供应商列表
}

const props = defineProps<TableData>()

useDidShow(() => {})
</script>

<style lang="scss" scoped></style>
