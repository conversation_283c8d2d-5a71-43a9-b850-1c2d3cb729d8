{"name": "taro", "version": "1.0.0", "private": true, "description": "", "author": "", "scripts": {"build:alipay": "taro build --type alipay", "build:h5": "taro build --type h5", "build:jd": "taro build --type jd", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "build:rn": "taro build --type rn", "build:swan": "taro build --type swan", "build:tt": "taro build --type tt", "build:weapp": "taro build --type weapp", "dev:alipay": "npm run build:alipay -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:weapp": "npm run build:weapp -- --watch"}, "dependencies": {"@babel/eslint-parser": "^7.24.8", "@babel/runtime": "^7.7.7", "@nutui/icons-vue": "^0.1.1", "@nutui/icons-vue-taro": "^0.0.9", "@nutui/nutui-cat": "^0.2.0", "@nutui/nutui-taro": "^4.3.0", "@nutui/touch-emulator": "^1.0.0", "@tarojs/components": "3.6.34", "@tarojs/helper": "3.6.34", "@tarojs/plugin-framework-react": "^4.0.6", "@tarojs/plugin-framework-vue3": "3.6.34", "@tarojs/plugin-html": "3.6.34", "@tarojs/plugin-http": "^3.6.34", "@tarojs/plugin-platform-alipay": "3.6.34", "@tarojs/plugin-platform-h5": "3.6.34", "@tarojs/plugin-platform-jd": "3.6.34", "@tarojs/plugin-platform-qq": "3.6.34", "@tarojs/plugin-platform-swan": "3.6.34", "@tarojs/plugin-platform-tt": "3.6.34", "@tarojs/plugin-platform-weapp": "3.6.34", "@tarojs/runtime": "3.6.34", "@tarojs/shared": "3.6.34", "@tarojs/taro": "3.6.34", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "arale-qrcode": "^3.0.5", "axios": "^1.7.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "echarts4taro3": "^1.8.0", "eslint-plugin-prettier": "^4.2.1", "pinia": "^2.1.7", "prettier": "^2.8.8", "qrcode": "^1.5.4", "taro-ui": "^3.3.0", "view-design": "^4.0.0", "view-ui-plus": "^1.3.19", "vue": "^3.2.40", "wxbarcode": "^1.0.2"}, "devDependencies": {"@babel/core": "^7.8.0", "@nutui/auto-import-resolver": "^1.0.0", "@tarojs/cli": "3.6.34", "@tarojs/taro-loader": "3.6.34", "@tarojs/webpack5-runner": "3.6.34", "@types/node": "^18.15.11", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "@vue/babel-plugin-jsx": "^1.0.6", "@vue/compiler-sfc": "^3.2.40", "babel-plugin-import": "^1.8.0", "babel-preset-taro": "3.6.34", "css-loader": "3.4.2", "eslint": "^8.12.0", "eslint-config-taro": "3.6.34", "eslint-plugin-vue": "^8.0.0", "less": "^2.7.3", "less-loader": "^4.0.6", "style-loader": "1.3.0", "stylelint": "9.3.0", "ts-node": "^10.9.1", "typescript": "^4.1.0", "unplugin-vue-components": "^0.27.4", "vue-cli-plugin-iview": "~2.0.0", "vue-loader": "^17.0.0", "webpack": "^5.78.0"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "templateInfo": {"name": "vue3-NutUI4", "typescript": true, "css": "Sass", "framework": "Vue3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}