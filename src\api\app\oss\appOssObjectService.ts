import { useAuthStore } from '@/stores/modules/auth'
import { format } from '@/utils/index'
import { getAppTenant, getAppToken } from '../../../utils/getAppInfo'

const Api = {
    DownloadObject: '/api/files/static/{bucket}/p/{path}/{name}',
}

const authStore = useAuthStore()

export class AppOssObjectService {
    downloadUrl = Api.DownloadObject

    generateOssUrl = (bucket: string, path: string, object: string) => {
        const token = getAppToken()
        const tenant = getAppTenant()
        if (path) {
            // 对 Path部分的 URL 编码
            path = encodeURIComponent(path)
            if (path !== '.%2F' && path.endsWith('%2F')) {
                path = path.substring(0, path.length - 3)
            }
        }
        const url = format(this.downloadUrl, { bucket, path, name: object })
        return `${API_URL_HOST}${url}?access_token=${token}&__tenant=${tenant?.tenantId}`
    }
}
