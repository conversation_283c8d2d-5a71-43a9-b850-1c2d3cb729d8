import { Menu } from './interface'
import { hasRoutePermission } from '../acl/permissionGuard'

/**
 * 根据权限标记菜单项的可见性
 * @param menus 原始菜单列表
 * @returns 处理后的菜单列表（所有项都保留，但增加了visible属性）
 */
export function processMenuVisibility(menus: Menu[]): Menu[] {
    if (!menus || menus.length === 0) {
        return []
    }
    menus.forEach(menu => {
        // 检查当前菜单项是否有权限访问
        const hasPermission = hasRoutePermission(menu)
        // 设置可见性属性
        menu.visible = hasPermission
        // 递归处理子菜单
        if (menu.children && menu.children.length > 0) {
            processMenuVisibility(menu.children)
            // 如果所有子菜单都不可见，则父菜单也不可见，如果至少有一个子菜单可见，则父菜单保持可见
            if (menu.children.every(child => child.visible === false)) {
                menu.visible = false
            }
        }
    })
    return menus
}

/**
 * 根据权限过滤菜单项
 * @param menus 原始菜单列表
 * @returns 过滤后的菜单列表（仅包含有权限访问的菜单项）
 */
export function filterMenusByPermission(menus: Menu[]): Menu[] {
    // 先处理可见性
    processMenuVisibility(menus)
    // 然后按传统方式过滤不可见项
    if (!menus || menus.length === 0) {
        return []
    }
    return menus.filter(menu => {
        if (!menu.visible) {
            return false
        }
        if (menu.children && menu.children.length > 0) {
            menu.children = filterMenusByPermission(menu.children)
        }
        return true
    })
}

/**
 * 检查菜单项是否可见
 * @param menu 菜单项
 * @returns 是否可见
 */
export function isMenuVisible(menu: Menu): boolean {
    return hasRoutePermission(menu)
}
