import type { GetForEditInput, NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetWmMeasureUnitForEditorOutput,
    GetWmMeasureUnitInput,
    WmMeasureUnitCreateDto,
    WmMeasureUnitDetailDto,
    WmMeasureUnitEditDto,
    WmMeasureUnitListDto,
} from '../wms/dtos/models'
import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class WmMeasureUnitService {
    apiName = 'EnterpriseService'

    create = (input: WmMeasureUnitCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-measure-unit',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-measure-unit/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmMeasureUnitDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-measure-unit/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmMeasureUnitDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-measure-unit/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmMeasureUnitForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-measure-unit/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-measure-unit/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetWmMeasureUnitInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmMeasureUnitListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-measure-unit',
                params: {
                    primaryUnitId: input.primaryUnitId,
                    isPrimaryUnit: input.isPrimaryUnit,
                    isActivate: input.isActivate,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    update = (input: WmMeasureUnitEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-measure-unit',
                data: input,
            },
            options,
        )
}
