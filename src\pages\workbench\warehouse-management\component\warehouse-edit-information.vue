<template>
    <view class="warehouse-edit">
        <nut-popup
            v-model:visible="localEditVisible"
            :style="{ padding: '16px', height: '85%', width: '92%' }"
            @close="onClosed"
            round
            class="edit-popup"
        >
            <view class="edit-container">
                <view class="edit-header">
                    <text class="header-title">{{
                        props.funName === 'confirmAdd' ? t('text.add') : t('menu.edit')
                    }}</text>
                </view>

                <nut-form
                    ref="formRef"
                    :model-value="editObj"
                    :rules="formRules"
                    class="edit-form"
                    star-position="right"
                >
                    <nut-form-item :label="t('text.name')" prop="name">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.name"
                            type="text"
                            @blur="customBlurValidate('name')"
                            :placeholder="t('text.pleaseEnterName')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.encode')" prop="encode">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.encode"
                            type="text"
                            @blur="customBlurValidate('encode')"
                            :placeholder="t('text.pleaseEnterCode')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.describe')" prop="describe">
                        <nut-textarea
                            v-model="editObj.describe"
                            class="custom-textarea"
                            :autosize="{ maxHeight: 80, minHeight: 60 }"
                            limit-show
                            :max-length="128"
                            :placeholder="t('text.pleaseEnterDescribe')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.location')" prop="location">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.location"
                            type="text"
                            @blur="customBlurValidate('location')"
                            :placeholder="t('text.pleaseEnterLocation')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.areaSize')" prop="areaSize">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.areaSize"
                            type="text"
                            @blur="customBlurValidate('areaSize')"
                            :placeholder="t('text.pleaseEnterAreaSize')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.productionManager')">
                        <view class="manager-selector" @click="isShow(true)">
                            <text class="selector-text">
                                {{ userName || t('text.pleaseSelectProductionManager') }}
                            </text>
                        </view>
                    </nut-form-item>

                    <nut-form-item :label="t('text.isEnable')" class="switch-item">
                        <nut-switch v-model="editObj.isActivate" />
                    </nut-form-item>

                    <nut-form-item :label="t('text.picture')" class="uploader-item">
                        <template v-if="hasPhotoPermission && hasCameraPermission">
                            <nut-uploader url="" />
                        </template>
                        <template v-else>
                            <nut-button size="small" type="primary" @click="requestImageUploadPermission">
                                请求授权
                            </nut-button>
                        </template>
                    </nut-form-item>
                </nut-form>

                <view class="edit-footer">
                    <nut-button class="footer-btn cancel" size="large" @click="onClosed">
                        {{ t('ui.cancel') }}
                    </nut-button>
                    <nut-button
                        class="footer-btn submit"
                        size="large"
                        type="primary"
                        v-if="props.funName === 'confirmEdit'"
                        @click="confirmEdit"
                    >
                        {{ t('ui.submit') }}
                    </nut-button>
                    <nut-button
                        class="footer-btn submit"
                        size="large"
                        type="primary"
                        v-if="props.funName === 'confirmAdd'"
                        @click="confirmAdd"
                    >
                        {{ t('ui.submit') }}
                    </nut-button>
                </view>
            </view>
        </nut-popup>

        <nut-popup v-model:visible="selectUser" position="bottom" round class="picker-popup">
            <nut-picker
                :columns="personList"
                :title="t('text.pleaseSelectProductionManager')"
                @confirm="confirmSelect"
                @cancel="selectUser = false"
            />
        </nut-popup>
    </view>
</template>

<script lang="ts" setup>
import { WmWarehouseService } from '@/api/proxy/enterprise/controller/wm-warehouse.service'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import { useDidShow } from '@tarojs/taro'
import { nextTick, ref, watch, computed, onMounted } from 'vue'
import { formRules } from '@/rules/formRules'
import { useAuth, AuthScope } from '@/hooks/auth/useAuth'

// 使用授权钩子
const { authState, requestMediaAuth, checkAuth } = useAuth()

// 计算属性：检查是否有相机和相册授权
const hasPhotoPermission = computed(() => authState.value[AuthScope.PhotoAlbum])
const hasCameraPermission = computed(() => authState.value[AuthScope.Camera])

// 定义表单数据接口
interface WmWarehouse {
    id?: string
    name: string
    encode: string
    describe: string
    location: string
    areaSize: string
    dutyUserId?: string
    isActivate?: boolean
}

interface WmWarehouseEditDto {
    id?: string
    name: string
    encode: string
    describe: string
    location: string
    areaSize: number
    dutyUserId?: string
    isActivate?: boolean
}

interface WmWarehouseCreateDto {
    name: string
    encode: string
    describe: string
    location: string
    areaSize: number
    dutyUserId?: string
    isActivate?: boolean
}

// 组件属性接口
interface Props {
    editVisible: boolean
    getWmEdit?: any
    wmWarehouse?: any
    funName?: 'confirmEdit' | 'confirmAdd'
}

const props = withDefaults(defineProps<Props>(), {
    editVisible: false,
})

const emit = defineEmits(['update:editVisible', 'fetchData'])

// 响应式变量
const formRef = ref()
const editObj = ref<WmWarehouse>({
    name: '',
    encode: '',
    describe: '',
    location: '',
    areaSize: '',
})
const localEditVisible = ref(props.editVisible)
const userName = ref('')
const selectUser = ref(false)
const personList = ref<Array<{ text: string; value: string; id: string }>>([])

// 监听父组件传入的数据变化
watch(
    () => props.getWmEdit,
    newValue => {
        if (newValue) {
            editObj.value = { ...newValue }
        }
    },
    { immediate: true },
)

// 获取用户列表
const getPersonList = async () => {
    const defaultParams = {
        skipCount: 0,
        maxResultCount: 100,
        filterText: '',
        sorting: 'userName',
    }
    const identityUserService = new IdentityUserService()
    try {
        const response: any = await identityUserService.getList(defaultParams)
        if (response && Array.isArray(response.items)) {
            personList.value = response.items.map(item => ({
                text: item.userName + '：' + item.surname + item.name,
                value: item.name,
                id: item.id,
            }))
            userName.value = ''
            personList.value.forEach((item: any) => {
                if (item.id === editObj.value['dutyUserId']) {
                    userName.value = item.text
                }
            })
        } else {
            console.error('Unexpected response format:', response)
        }
    } catch (error) {
        console.error('Error fetching person list:', error)
    }
}

// 显示编辑信息
const showEditInfo = async () => {
    if (props.getWmEdit && props.getWmEdit.wmWarehouse) {
        editObj.value = { ...props.getWmEdit.wmWarehouse }
        await getPersonList()
    } else {
        console.error('getWmEdit or wmWarehouse is not defined')
    }
}

// 控制选择用户弹窗的显示状态
const isShow = (value: boolean) => {
    selectUser.value = value
}

// 确认选择
const confirmSelect = ({ selectedOptions }: { selectedOptions: any[] }) => {
    editObj.value['dutyUserId'] = selectedOptions[0].id
    userName.value = selectedOptions[0].text
    isShow(false)
}

// 自定义表单验证
const customBlurValidate = (prop: string) => {
    formRef.value?.validate(prop)
}

// 确认编辑
const confirmEdit = async () => {
    formRef.value?.validate().then(async ({ valid }: { valid: boolean }) => {
        if (valid) {
            try {
                const wmWarehouseService = new WmWarehouseService()
                const editData: WmWarehouseEditDto = {
                    ...editObj.value,
                    areaSize: Number(editObj.value.areaSize),
                }
                await wmWarehouseService.update(editData)
                emit('fetchData')
                localEditVisible.value = false
            } catch (error) {
                console.error('编辑失败', error)
            }
        }
    })
}

// 确认添加
const confirmAdd = async () => {
    formRef.value?.validate().then(async ({ valid }: { valid: boolean }) => {
        if (valid) {
            try {
                const wmWarehouseService = new WmWarehouseService()
                const createData: WmWarehouseCreateDto = {
                    ...editObj.value,
                    areaSize: Number(editObj.value.areaSize),
                }
                await wmWarehouseService.create(createData)
                emit('fetchData')
                localEditVisible.value = false
            } catch (error) {
                console.error('新增失败', error)
            }
        }
    })
}

// 弹窗关闭时的处理
const onClosed = async () => {
    emit('update:editVisible', false)
    await nextTick()
    editObj.value = {
        name: '',
        encode: '',
        describe: '',
        location: '',
        areaSize: '',
    }
    selectUser.value = false
}

// 监听 props.editVisible 的变化，同步到 localEditVisible
watch(
    () => props.editVisible,
    async newValue => {
        localEditVisible.value = newValue
        if (newValue) {
            checkAuth([AuthScope.Camera, AuthScope.PhotoAlbum])
            props.funName === 'confirmEdit' ? await showEditInfo() : await getPersonList()
        }
    },
    { immediate: true },
)

/**
 * @description 请求图片上传授权
 */
const requestImageUploadPermission = async () => {
    try {
        await requestMediaAuth()
    } catch (error) {
        console.error('授权请求失败', error)
    }
}

// 组件挂载时检查授权
onMounted(() => {
    checkAuth([AuthScope.Camera, AuthScope.PhotoAlbum])
})

useDidShow(() => {})
</script>

<style lang="scss" scoped>
.warehouse-edit {
    .edit-popup {
        border-radius: 24px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    }

    .edit-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        background: #ffffff;
    }

    .edit-header {
        padding: 20px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        position: relative;

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            position: relative;
            display: inline-block;
            padding: 0 16px;

            &::before,
            &::after {
                content: '';
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 20px;
                background: #0066ff;
                border-radius: 2px;
            }

            &::before {
                left: 0;
            }

            &::after {
                right: 0;
            }
        }
    }

    .edit-form {
        height: calc(100% - 180px);
        overflow-y: auto;
        padding: 0 16px;

        :deep(.nut-form-item) {
            margin-bottom: 16px;

            .nut-form-item__label {
                font-size: 14px;
                color: #333;
                font-weight: 500;
            }
        }

        .custom-input {
            border-bottom: 1px solid #eee;
            transition: all 0.3s;

            &:focus {
                border-color: #0066ff;
            }
        }

        .custom-textarea {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 8px;
            transition: all 0.3s;

            &:focus {
                border-color: #0066ff;
            }
        }

        .manager-selector {
            padding: 8px 0;
            border-bottom: 1px solid #eee;

            .selector-text {
                color: #666;
                font-size: 14px;
            }
        }

        .switch-item {
            :deep(.nut-switch) {
                transform: scale(0.8);
            }
        }

        .uploader-item {
            :deep(.nut-uploader) {
                border-radius: 8px;
                overflow: hidden;
            }
        }
    }

    .edit-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 16px 20px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        justify-content: space-between;
        gap: 16px;
        z-index: 1;

        .footer-btn {
            flex: 1;
            height: 44px;
            border-radius: 22px;
            font-size: 15px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

            &.cancel {
                background: #f8fafc;
                color: #64748b;
                border: 2px solid #e2e8f0;

                &:active {
                    background: #f1f5f9;
                    transform: scale(0.98);
                }
            }

            &.submit {
                background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%);
                color: #fff;
                border: none;

                &:active {
                    transform: scale(0.98);
                    background: linear-gradient(135deg, #0052cc 0%, #004099 100%);
                }
            }
        }
    }
}

.picker-popup {
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.08);
}

.edit-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px 20px;
    padding-bottom: 90px;

    :deep(.nut-form-item) {
        margin-bottom: 20px;
        background: #fff;
        border-radius: 12px;
        padding: 4px 8px;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
        }

        .nut-form-item__label {
            font-size: 15px;
            color: #2c3e50;
            padding: 10px 0;
            font-weight: 500;
        }

        .nut-form-item__body {
            min-height: 40px;
        }
    }

    .form-input {
        border: none;
        border-bottom: 2px solid #eef2f7;
        transition: all 0.3s ease;
        font-size: 14px;
        padding: 8px 0;

        &:focus {
            border-color: #0066ff;
        }
    }

    .form-textarea {
        border: 2px solid #eef2f7;
        border-radius: 12px;
        padding: 12px;
        transition: all 0.3s ease;
        font-size: 14px;
        line-height: 1.6;

        &:focus {
            border-color: #0066ff;
            box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.1);
        }
    }

    .manager-select {
        padding: 10px 0;
        border-bottom: 2px solid #eef2f7;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            border-color: #0066ff;
        }

        .select-text {
            color: #4a5568;
            font-size: 14px;
        }
    }

    .form-switch {
        transform: scale(0.9);
        transition: all 0.3s ease;
    }

    .form-uploader {
        :deep(.nut-uploader__upload) {
            width: 100px !important;
            height: 100px !important;
            background: #f8fafc;
            border: 2px dashed #e2e8f0;
            border-radius: 16px;
            transition: all 0.3s ease;

            &:hover {
                border-color: #0066ff;
                background: #f0f7ff;
            }
        }
    }
}
</style>
