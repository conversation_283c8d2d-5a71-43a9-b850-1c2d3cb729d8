<template>
    <view class="content">
        <!-- <view style="background-color: aquamarine; height: 10px"></view> -->
        <nut-popup :overlay="false" position="bottom" round :style="{ height: '80%' }" v-model:visible="showRound">
            <view class="login">
                <view class="login-title">登录</view>
                <nut-config-provider :theme-vars="themeVars">
                    <view class="g-flex-centen login-input-item">
                        <My color="#3c9fb9" size="16"></My>
                        <nut-input v-model="account" placeholder="请输入账号" class="login-input" />
                    </view>
                    <view class="g-flex-centen login-input-item">
                        <Success color="#3c9fb9" size="16"></Success>
                        <nut-input v-model="password" placeholder="请输入密码" class="login-input" />
                    </view>
                    <nut-button class="login-btn" plain block :disabled="logining" @click="loginClick">登录</nut-button>
                    <view class="login-tips">--其它登录方式--</view>
                </nut-config-provider>
                <view class="login-manner" @click="wechatLogin">
                    <img :src="wechatPng" />
                </view>
            </view>
        </nut-popup>
    </view>
</template>

<script lang="ts" setup>
import { useAuthStore } from '@/stores/modules/auth'
import { Toast } from '@/utils/Taro-api'
import { My, Success } from '@nutui/icons-vue-taro'
import Taro from '@tarojs/taro'
import { reactive, ref } from 'vue'
import wechatPng from '../../../assets/images/wechat.png'

const auth = useAuthStore()
const showRound = ref<boolean>(true)
const account = ref<string>('logi')
const password = ref<string>('Qwe!@#456')
const logining = ref<boolean>(false)

/**
 * 自定义按钮变量
 */
const themeVars = reactive({
    buttonBorderRadius: '9px',
    buttonDefaultBorderColor: '#3c9fb9',
    buttonDefaultColor: '#3c9fb9',
})

/**
 * @description 登录事件
 */
const loginClick = () => {
    Taro.showLoading({ title: '登录中...' })
    logining.value = true
    getTenantByName()
}

/**
 * @description 设置租户
 */
const getTenantByName = () => {
    // 获取租户
    const authStore = useAuthStore()
    authStore
        .getTenantByName('')
        .then(res => {
            login()
        })
        .catch(err => {
            Toast(err ?? '获取租户失败', { icon: 'error' })
            failInit()
        })
}

/**
 * @description 登录
 */
const login = () => {
    auth.login({ name: account.value, password: password.value })
        .then(() => {
            Taro.switchTab({
                url: '/pages/home/<USER>',
            })
        })
        .catch(error => {
            Toast(error ?? '登录失败', { icon: 'error' })
        })
        .finally(() => {
            failInit()
        })
}

/**
 * @description 登录失败，恢复页面状态
 */
const failInit = () => {
    logining.value = false
    Taro.hideLoading()
}

/**
 * @description 微信登录
 */
const wechatLogin = () => {
    Taro.reLaunch({
        url: '/pages/passport/wechat-login/index',
    })
}
</script>

<style lang="scss">
$login-input-item-color: #f5f9fb;
.content {
    height: 100vh;
    width: 100%;
    background-color: antiquewhite;
    background-image: url(../../../assets/images/login-bj.png);
}
.login {
    margin: 70px;
    .login-title {
        color: #3c9fb9;
        font-size: 46px;
        font-weight: 520;
    }
    .login-input-item {
        border-radius: 13px;
        padding-left: 14px;
        padding-right: 14px;
        margin-top: 70px;
        background-color: $login-input-item-color;
        .login-input {
            padding: 28px 0px;
            padding-left: 18rpx;
            background-color: $login-input-item-color;
        }
    }
    .login-btn {
        margin-top: 70px;
    }
    .login-tips {
        margin-top: 120px;
        text-align: center;
        color: rgb(155, 155, 155);
        font-size: 24px;
    }
    .login-manner {
        text-align: center;
        margin-top: 68px;
        img {
            width: 102px;
            height: 102px;
            border-radius: 51px;
        }
    }
}
</style>
