import type { PagedAndSortedResultRequestDto } from '/@/api/app/model/baseModel'

export interface PagedSortedAndFilteredInputDto extends PagedAndSortedResultRequestDto {
	filterText?: string
	sorting?: string
}

export interface GetForEditInput {
	id?: string
}

export interface NodeSelectDataDto extends OptionMenuItemBaseDto {
	value?: string
}

export interface NodeTreeDataDto {
	value?: string
	label?: string
	path?: string
	sort?: number
	isLeaf?: boolean
	extra?: string
	parent?: string
	children: NodeTreeDataDto[]
}

export interface OptionMenuItemBaseDto {
	label?: string
	encode?: string
}

export interface ReturnResult {
	isSuccess: boolean
	message?: string
	errorCode: number
}
