import { EntityDto, PagedAndSortedResultRequestDto } from '@/shared/models/dtos'

export interface GetFactoryDeviceInput extends PagedSortedAndFilteredInputDto {
    isDeleted: any
    factoryParentId?: string
    sorting?: string
}

export interface PagedSortedAndFilteredInputDto extends PagedAndSortedResultRequestDto {
    filterText?: string
    sorting?: string
}

export interface FactoryDeviceListDto extends EntityDto<string> {
    name?: string
    serialNo: number
    encode?: string
    describe?: string
    images?: string
    imagesUrl?: string[]
    deviceForm: FactoryDeviceForm
    deviceType: FactoryDeviceType
    isActivate: boolean
    produceDutyUserId?: string
    maintainDutyUserId?: string
    produceDutyUserName?: string
    maintainDutyUserName?: string
    iotGatewayId?: string
    iotDeviceId?: string
    iotDeviceName?: string
    factoryParentId?: string
    factoryParentName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface FactoryDeviceDetailDto extends FactoryDeviceListDto {}

export interface FactoryDeviceCreateDto {
    name: string
    serialNo: number
    encode?: string
    describe?: string
    images?: string
    deviceForm: FactoryDeviceForm
    deviceType: FactoryDeviceType
    isActivate: boolean
    produceDutyUserId?: string
    maintainDutyUserId?: string
    factoryParentId?: string
    extraProperties?: Record<string, object>
}

export interface FactoryDeviceEditDto extends EntityDto<string> {
    name: string
    serialNo: number
    encode?: string
    describe?: string
    images?: string
    deviceForm: FactoryDeviceForm
    deviceType: FactoryDeviceType
    isActivate: boolean
    produceDutyUserId?: string
    maintainDutyUserId?: string
    factoryParentId?: string
    extraProperties?: Record<string, object>
    concurrencyStamp?: string
}

export interface GetForEditInput {
    id?: string
}

export interface GetFactoryDeviceForEditorOutput {
    factoryDevice: FactoryDeviceEditDto
}

export interface BindingIotDeviceInput {
    factoryDeviceId: string
    iotDeviceId: string
}

export interface NodeSelectDataDto {
    id: string
    name: string
    displayName?: string
    isSelected?: boolean
}

export enum FactoryDeviceForm {
    SingleDevice = 'SingleDevice',
    VirtualUnit = 'VirtualUnit',
    SolidUnit = 'SolidUnit',
    FlexibleUnit = 'FlexibleUnit',
}

export enum FactoryDeviceType {
    Unknown = 'Unknown',
    Machine = 'Machine',
    Robot = 'Robot',
    Agv = 'Agv',
    SimpleMachine = 'SimpleMachine',
    Unit = 'Unit',
}
