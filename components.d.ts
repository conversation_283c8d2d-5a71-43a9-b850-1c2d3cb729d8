/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActionMenu: typeof import('./src/components/ActionMenu/ActionMenu.vue')['default']
    BaseCard: typeof import('./src/components/BaseCard.vue')['default']
    BaseModal: typeof import('./src/components/Modal/BaseModal.vue')['default']
    CategoryTree: typeof import('./src/components/CategoryTree.vue')['default']
    ComponentTemplate: typeof import('./src/components/ComponentTemplate.vue')['default']
    DialogSelectDate: typeof import('./src/components/DialogSelectDate.vue')['default']
    InformationCard: typeof import('./src/components/InformationCard.vue')['default']
    ListScreen: typeof import('./src/components/ListScreen.vue')['default']
    NutAvatar: typeof import('@nutui/nutui-taro')['Avatar']
    NutButton: typeof import('@nutui/nutui-taro')['Button']
    NutCascader: typeof import('@nutui/nutui-taro')['Cascader']
    NutCell: typeof import('@nutui/nutui-taro')['Cell']
    NutCellGroup: typeof import('@nutui/nutui-taro')['CellGroup']
    NutCheckbox: typeof import('@nutui/nutui-taro')['Checkbox']
    NutCol: typeof import('@nutui/nutui-taro')['Col']
    NutCollapse: typeof import('@nutui/nutui-taro')['Collapse']
    NutCollapseItem: typeof import('@nutui/nutui-taro')['CollapseItem']
    NutConfigProvider: typeof import('@nutui/nutui-taro')['ConfigProvider']
    NutDatePicker: typeof import('@nutui/nutui-taro')['DatePicker']
    NutDialog: typeof import('@nutui/nutui-taro')['Dialog']
    NutEmpty: typeof import('@nutui/nutui-taro')['Empty']
    NutForm: typeof import('@nutui/nutui-taro')['Form']
    NutFormItem: typeof import('@nutui/nutui-taro')['FormItem']
    NutGrid: typeof import('@nutui/nutui-taro')['Grid']
    NutGridItem: typeof import('@nutui/nutui-taro')['GridItem']
    NutImagePreview: typeof import('@nutui/nutui-taro')['ImagePreview']
    NutInput: typeof import('@nutui/nutui-taro')['Input']
    NutInputNumber: typeof import('@nutui/nutui-taro')['InputNumber']
    NutMenu: typeof import('@nutui/nutui-taro')['Menu']
    NutMenuItem: typeof import('@nutui/nutui-taro')['MenuItem']
    NutNavbar: typeof import('@nutui/nutui-taro')['Navbar']
    NutNoticebar: typeof import('@nutui/nutui-taro')['Noticebar']
    NutOverlay: typeof import('@nutui/nutui-taro')['Overlay']
    NutPagination: typeof import('@nutui/nutui-taro')['Pagination']
    NutPicker: typeof import('@nutui/nutui-taro')['Picker']
    NutPopover: typeof import('@nutui/nutui-taro')['Popover']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
    NutRow: typeof import('@nutui/nutui-taro')['Row']
    NutSearchbar: typeof import('@nutui/nutui-taro')['Searchbar']
    NutSkeleton: typeof import('@nutui/nutui-taro')['Skeleton']
    NutSpace: typeof import('@nutui/nutui-taro')['Space']
    NutSwiper: typeof import('@nutui/nutui-taro')['Swiper']
    NutSwiperItem: typeof import('@nutui/nutui-taro')['SwiperItem']
    NutSwitch: typeof import('@nutui/nutui-taro')['Switch']
    NutTable: typeof import('@nutui/nutui-taro')['Table']
    NutTabPane: typeof import('@nutui/nutui-taro')['TabPane']
    NutTabs: typeof import('@nutui/nutui-taro')['Tabs']
    NutTag: typeof import('@nutui/nutui-taro')['Tag']
    NutTextarea: typeof import('@nutui/nutui-taro')['Textarea']
    NutUploader: typeof import('@nutui/nutui-taro')['Uploader']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatusCard: typeof import('./src/components/StatusCard.vue')['default']
  }
}
