import { PagedSortedAndFilteredInputDto } from '../factory-device-service/models'

export interface GetIotDeviceInput extends PagedSortedAndFilteredInputDto {
    nodeType: IotNodeType
    gatewayId?: string
    useStatus: IotDeviceUseStatus
    tenantId?: string
    maxResultCount: number
    sorting?: string
}

export interface IotDeviceListDto extends IotHubDeviceExtendDto {
    id?: string
    appId?: string
    appName?: string
    productId?: string
    productName?: string
    gatewayId?: string
    nodeId?: string
    nodeType: IotNodeType
    deviceId?: string
    deviceName?: string
    deviceSecret?: string
    description?: string
    hubSupplier: IotHubSupplier
    useStatus: IotDeviceUseStatus
    tenantId?: string
    tenantName?: string
}

export interface IotHubDeviceExtendDto {
    ipAddress?: string
    status: IotDeviceStatus
    fwVersion?: string
    swVersion?: string
    deviceSdkVersion?: string
    authInfo: IotHubAuthInfoResDto
    createTime?: string
    connectionStatusUpdateTime?: string
    activeTime?: string
    tags: TagV5DTO[]
    extensionInfo: object
}

export interface IotHubAuthInfoResDto {
    authType?: string
    secret?: string
    secondarySecret?: string
    fingerprint?: string
    secondaryFingerprint?: string
    secureAccess?: boolean
    timeout?: number
}

export interface TagV5DTO {
    tag_key?: string
    tag_value?: string
}

export interface ReturnResult {
    isSuccess: boolean
    message?: string
    errorCode: number
}

export interface IotHubDeviceDetailedDto {
    appId?: string
    appName?: string
    deviceId?: string
    nodeId?: string
    gatewayId?: string
    deviceName?: string
    nodeType: IotDeviceNodeType
    description?: string
    fwVersion?: string
    swVersion?: string
    deviceSdkVersion?: string
    authInfo: IotHubAuthInfoResDto
    productId?: string
    productName?: string
    status: IotHubDeviceStatus
    createTime?: string
    connectionStatusUpdateTime?: string
    activeTime?: string
    tags: TagV5DTO[]
    extensionInfo: object
    tenantId?: string
}

export enum IotDeviceNodeType {
    UNKNOWN = 'UNKNOWN',
    ENDPOINT = 'ENDPOINT',
    GATEWAY = 'GATEWAY',
}

export enum IotHubDeviceStatus {
    UNKNOWN = 'UNKNOWN',
    INACTIVE = 'INACTIVE',
    ONLINE = 'ONLINE',
    OFFLINE = 'OFFLINE',
    ABNORMAL = 'ABNORMAL',
    FROZEN = 'FROZEN',
}

export enum IotNodeType {
    None = 'None',
    Device = 'Device',
    Gateway = 'Gateway',
}

export enum IotDeviceUseStatus {
    Unknown = 'Unknown',
    NotUsed = 'NotUsed',
    UsedAlready = 'UsedAlready',
}

export enum IotHubSupplier {
    None = 'None',
    Aliyun = 'Aliyun',
    Huawei = 'Huawei',
    Tencent = 'Tencent',
}

export enum IotDeviceStatus {
    UNKNOWN = 'UNKNOWN',
    INACTIVE = 'INACTIVE',
    ONLINE = 'ONLINE',
    OFFLINE = 'OFFLINE',
    ABNORMAL = 'ABNORMAL',
    FROZEN = 'FROZEN',
}
