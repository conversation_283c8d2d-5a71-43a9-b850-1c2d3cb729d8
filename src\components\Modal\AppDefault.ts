// export const useAppDefault = {
//   id: 'user',
//   state: (): UserState => ({
//     id: undefined,
//     userName: undefined,
//     fullName: undefined,
//     email: undefined
//   }),
//   getters: {
//     getUserId: state => state.id,
//     getUserName: state => state.userName,
//     getFullName: state => state.fullName,
//     getUserEmail: state => state.email
//   },
//   actions: {
//     setEmail(email?: string) {
//       email != undefined ? setCache(USER_EMIL, email) : removeCache(USER_EMIL);
//       this.email = email;
//     }
//   }
// };
