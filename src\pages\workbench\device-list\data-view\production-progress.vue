<template>
    <view class="chart-card" v-show="showCanvas">
        <nut-empty v-if="isFetchError" description="Error" />
        <EChart v-else ref="canvas" style="width: 100%; height: 100%" />
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Taro from '@tarojs/taro'
import EChart from './echarts/index'
import { useDidShow } from '@/hooks/component/hooks'
import { t } from '@/locale/fanyi'
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { DeviceProductionTotalProgressDto, GetDataViewDeviceInput } from '@/api/proxy/data-view/models'
import { HideLoading, Loading, Toast } from '@/utils/Taro-api'

export interface DeviceUseRate {
    deviceName?: string
    powerOnRate: number
    utilizationRate: number
    spareRate: number
    failureRate: number
    time?: string
}

const canvas = ref<any>(null)
const chartOption = ref<any>()
const datavDataFlowService = new DatavDataFlowService()
const showCanvas = ref<boolean>(true)
const isFetchError = ref<boolean>(false)

const buildOption = (input: DeviceProductionTotalProgressDto) => {
    const gaugeData = [
        {
            value: input.productivity,
            name: t('Platform.ProductionProgress'),
            title: {
                offsetCenter: ['0%', '-20%'],
                fontSize: 18,
                fontWeight: 600,
                color: '#1D3557',
            },
            detail: {
                valueAnimation: true,
                offsetCenter: ['0%', '20%'],
                fontSize: 24,
                fontWeight: 'bold',
                color: '#4361EE',
            },
        },
    ]
    const option = {
        series: [
            {
                type: 'gauge',
                startAngle: 90,
                endAngle: -270,
                pointer: {
                    show: false,
                },
                progress: {
                    show: true,
                    overlap: false,
                    roundCap: true,
                    clip: false,
                    itemStyle: {
                        borderWidth: 1,
                        color: '#4361EE',
                    },
                },
                axisLine: {
                    lineStyle: {
                        width: 30,
                        color: [[1, '#E9ECEF']],
                    },
                },
                splitLine: {
                    show: false,
                    distance: 0,
                    length: 10,
                },
                axisTick: {
                    show: false,
                },
                axisLabel: {
                    show: false,
                    distance: 50,
                },
                data: gaugeData,
                title: {
                    fontSize: 18,
                },
                detail: {
                    width: 120,
                    height: 40,
                    fontSize: 24,
                    color: '#4361EE',
                    borderColor: '#4361EE',
                    borderRadius: 20,
                    borderWidth: 1,
                    formatter: '{value}%',
                },
            },
        ],
    }
    return option
}

/**
 * @description 获取列表
 */
const fetchData = async () => {
    try {
        Loading()
        const input: GetDataViewDeviceInput = {
            factoryParentId: '',
        }
        const result = await datavDataFlowService.getProductionTotalProgress(input)
        chartOption.value = buildOption(result)
        HideLoading()
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
        isFetchError.value = true
    }
}

/**
 * @description 渲染图表
 */
const renderChart = () => {
    const echartComponentInstance: any = canvas.value // 组件实例
    Taro.nextTick(() => {
        if (!echartComponentInstance) return
        // 初始化图表
        echartComponentInstance.refresh(chartOption.value).then(myChart => {
            myChart.showLoading()
            /** 异步更新图表数据 */
            setInterval(() => {
                myChart.setOption(chartOption.value) // myChart 即为 echarts 实例，可使用的实例方法，具体可参考 echarts 官网
                myChart.hideLoading()
            }, 1000)
        })
    })
}

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {
    await fetchData()
    renderChart()
})

defineExpose({})
</script>

<style lang="scss">
.chart-card {
    width: 90vw;
    height: 50vh;
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 6px;
        height: 60px;
        background: #4361ee;
        border-radius: 0 3px 3px 0;
    }
}
</style>
