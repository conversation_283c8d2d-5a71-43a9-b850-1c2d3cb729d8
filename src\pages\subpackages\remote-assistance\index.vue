<template>
    <view class="assistance-top-button">
        <nut-button type="info" size="small" @click="fetchData" plain>{{ t('ui.refresh') }}</nut-button>
    </view>
    <view
        class="content-wrapper"
        v-for="(item, index) in remoteAssistanceList"
        :key="index"
        @click="handleDetail(item.id)"
    >
        <InformationCard :show-empty-image="false">
            <template #front-title> [{{ index + 1 }}] </template>
            <template #title>{{ item.name }}</template>
            <template #line-first>{{ t('text.deviceName') }}:{{ item.deviceName }}</template>
            <template #line-second>
                {{ t('text.deviceForm') }}:
                <nut-tag type="primary" size="small">{{ t(`tag.${item.deviceForm}`) }}</nut-tag>
            </template>
            <template #line-third>
                {{ t('text.deviceType') }}:
                <nut-tag type="primary" size="small">{{ t(`tag.${item.deviceType}`) }}</nut-tag>
            </template>
            <template #line-fourth>
                {{ t('text.status') }}:
                <nut-tag type="primary" size="small">{{
                    item.isComplete ? t('text.isComplete') : t('text.isNotComplete')
                }}</nut-tag>
            </template>
            <template #line-fifth> {{ t('text.creatorName') }}: {{ item.creatorName }} </template>
            <template #space-one>
                {{ t('text.creationTime') }}: {{ dayjs(item.creationTime).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
            <template #space-two>
                <view @click.stop>
                    <nut-popover
                        v-model:visible="showMore[index]"
                        :list="moreList"
                        location="left"
                        @choose="event => choose(event, item.id, index)"
                    >
                        <template #reference>
                            <nut-button type="info" size="small" plain>{{ t('text.more') }}</nut-button>
                        </template>
                    </nut-popover>
                </view>
            </template>
        </InformationCard>
        <view @click.stop>
            <nut-dialog
                :content="t('text.confirmAssistanceComplete')"
                v-model:visible="assistCompleteDialogVisible"
                @cancel="assistCompleteDialogVisible = false"
                @ok="completeAssistance"
            />
        </view>
    </view>
</template>

<script setup lang="ts">
import { RemoteAssistanceService } from '@/api/proxy/platform/controller/remote-assistance.service'
import { t } from '@/locale/fanyi'
import { Toast } from '@/utils/Taro-api'
import { useDidShow } from '@tarojs/taro'
import { ref, watch } from 'vue'
import { RemoteAssistanceListDto } from '@/api/proxy/platform/dtos/models'
import dayjs from 'dayjs'
import Taro from '@tarojs/taro'

const remoteAssistanceService = new RemoteAssistanceService()
const remoteAssistanceList = ref<RemoteAssistanceListDto[]>([])
const showMore = ref<boolean[]>([])
const currentId = ref<string>('')
const assistCompleteDialogVisible = ref(false)
const moreList = ref([
    {
        name: t('text.assistanceComplete'),
    },
])

// 更新 showMore 状态的函数
const updateShowMore = (index: number, value: boolean) => {
    showMore.value[index] = value
}

// 处理选择事件
const choose = async (event: { name: string }, id: string, index: number) => {
    updateShowMore(index, false)
    currentId.value = id
    if (event.name === t('text.assistanceComplete')) {
        assistCompleteDialogVisible.value = true
    }
}

// 完成远程协助的方法
const completeAssistance = async () => {
    if (!currentId.value) return
    try {
        await remoteAssistanceService.update({
            id: currentId.value,
            isComplete: true,
        })
        Toast(t('text.operationSuccess'), { icon: 'success' })
        fetchData()
    } catch (error) {
        console.error('完成远程协助失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    }
}

const fetchData = async () => {
    const input = {
        isComplete: false,
        sorting: '',
        filterText: '',
        skipCount: 0,
        maxResultCount: 1000,
    }
    try {
        const result = await remoteAssistanceService.getPaged(input)
        remoteAssistanceList.value = result.items
        showMore.value = new Array(result.items.length).fill(false)
    } catch (error) {
        console.error('获取远程协助列表失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    }
}

const handleDetail = (id: string) => {
    Taro.navigateTo({
        url: `/pages/subpackages/remote-assistance/components/index?id=${id}`,
        success: result => {
            const clickedItem = remoteAssistanceList.value.find(item => item.id === id)
            if (clickedItem) {
                result.eventChannel.emit('acceptClickedItemData', { data: clickedItem })
            }
        },
        fail: error => {
            console.error(`${t('text.navigateToFail')}:`, error)
            Toast(t('text.navigateToFail'), { icon: 'none' })
        },
    })
}

// 监听列表变化，确保 showMore 数组长度正确
watch(remoteAssistanceList, newList => {
    if (newList.length !== showMore.value.length) {
        showMore.value = new Array(newList.length).fill(false)
    }
})

useDidShow(() => {
    fetchData()
})
</script>

<style lang="scss">
.assistance-top-button {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
}

.content-wrapper {
    margin-top: 10px;
}
</style>
