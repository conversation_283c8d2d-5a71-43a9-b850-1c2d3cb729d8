import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { ReturnResult } from '../../helper/shared/models'
import type {
    PurchaseItemsApprovalDto,
    PurchaseItemsOrderDto,
    PurchaseItemsProcurementDto,
    PurchaseItemsRefundedDto,
    PurchaseItemsReturnModifyDto,
    PurchaseItemsSubmitApprovalDto,
} from '../wms/dtos/models'

export class WorkflowsPurchaseItemsService {
    apiName = 'EnterpriseService'

    approvalByInput = (input: PurchaseItemsApprovalDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/approval',
                data: input,
            },
            options,
        )

    inProcurementByInput = (input: PurchaseItemsProcurementDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/in-procurement',
                data: input,
            },
            options,
        )

    orderByInput = (input: PurchaseItemsOrderDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/order',
                data: input,
            },
            options,
        )

    refundedByInput = (input: PurchaseItemsRefundedDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/refunded',
                data: input,
            },
            options,
        )

    returnModifyByInput = (input: PurchaseItemsReturnModifyDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/return-modify',
                data: input,
            },
            options,
        )

    submitApprovalByInput = (input: PurchaseItemsSubmitApprovalDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/submit-approval',
                data: input,
            },
            options,
        )
}
