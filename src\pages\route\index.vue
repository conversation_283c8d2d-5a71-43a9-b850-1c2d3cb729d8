<template>
    <view class="route-jump-container">
        <view v-if="loading" class="loading-container">
            <nut-skeleton rows="3" title animated />
            <view class="loading-text">{{ $t('ui.loading') }}</view>
        </view>
        <view v-else-if="error" class="error-container">
            <nut-empty :description="$t('ui.pageJumpFailed')" />
            <nut-button @click="parseUrlAndNavigate" type="primary" size="small">{{ $t('ui.retry') }}</nut-button>
        </view>
        <view v-else-if="showMachineInfo" class="machine-info-container">
            <view class="machine-card custom-card">
                <view class="card-title">
                    <view class="card-title-line"></view>
                    <text>{{ $t('ui.deviceInfo') }}</text>
                </view>
                <view class="card-content">
                    <nut-grid :column-num="3">
                        <nut-grid-item :text="$t('ui.deviceInfo')" @click="goToMachineDetails">
                            <image :src="deviceIcon" class="grid-icon" />
                        </nut-grid-item>
                        <nut-grid-item :text="$t('menu.productionDaily')" @click="goToProductionDaily">
                            <image :src="dailyIcon" class="grid-icon" />
                        </nut-grid-item>
                        <nut-grid-item :text="$t('ui.viewDeviceTask')" @click="showDevelopingTip">
                            <image :src="taskIcon" class="grid-icon" />
                        </nut-grid-item>
                        <nut-grid-item :text="$t('ui.productionReport')" @click="showDevelopingTip">
                            <image :src="reportIcon" class="grid-icon" />
                        </nut-grid-item>
                        <nut-grid-item :text="$t('ui.remoteAssist')" @click="showDevelopingTip">
                            <image :src="assistIcon" class="grid-icon" />
                        </nut-grid-item>
                    </nut-grid>
                </view>
            </view>
        </view>
        <view v-else class="content-wrapper">
            <view class="title">{{ $t('ui.redirecting') }}</view>
            <view class="description">{{ $t('ui.redirectingDescription') }}</view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import { NavigateTo } from '@/utils/Taro-api'
import { ref, onMounted } from 'vue'
import deviceIcon from '@/assets/images/icon/device.png'
import dailyIcon from '@/assets/images/icon/daily.png'
import taskIcon from '@/assets/images/icon/device-dashboard.png'
import reportIcon from '@/assets/images/icon/production-dashboard.png'
import assistIcon from '@/assets/images/icon/camera.png'

Taro.setNavigationBarTitle({ title: t('ui.redirecting') })

// 状态变量
const loading = ref<boolean>(true)
const error = ref<boolean>(false)
const showMachineInfo = ref<boolean>(false)
const machineId = ref<string>('')

// 路径映射
const pathMapping: Record<string, (id: string) => string> = {
    iotgw: (id: string) => `/pages/workbench/device-iot/component/iot-device-details?id=${id}`,
    material: (id: string) => `/pages/subpackages/material-management/material-details/index?id=${id}`,
    machine: (id: string) => `/pages/workbench/device-list/device-details/index?id=${id}`,
}

/**
 * @description
 * @returns
 */
const getUrlParams = () => {
    // 获取当前页面实例
    const currentInstance = Taro.getCurrentInstance()
    const params = currentInstance.router?.params || {}
    const result: Record<string, string> = {}
    // 处理scene参数
    if (params.scene) {
        const sceneParams = decodeURIComponent(params.scene)
        sceneParams.split('&').forEach(item => {
            const [key, value] = item.split('=')
            if (key && value) {
                result[key] = value
            }
        })
    }
    if (params.q) {
        try {
            const decodedUrl = decodeURIComponent(params.q)
            const queryIndex = decodedUrl.indexOf('?')
            if (queryIndex !== -1) {
                const queryString = decodedUrl.slice(queryIndex + 1)
                queryString.split('&').forEach(param => {
                    const [key, value] = param.split('=')
                    if (key && value) {
                        result[key] = decodeURIComponent(value)
                    }
                })
            }
        } catch (error) {
            console.error('解析q参数失败:', error)
        }
    }
    // 处理直接参数
    for (const key in params) {
        if (key !== 'scene' && key !== 'q' && !result[key]) {
            result[key] = params[key] as string
        }
    }
    return result
}

/**
 * @description
 */
const parseUrlAndNavigate = () => {
    loading.value = true
    error.value = false
    showMachineInfo.value = false
    try {
        const urlParams = getUrlParams()
        const path = urlParams.path
        const id = urlParams.id
        if (!path || !id) {
            console.error('缺少必要的参数:', urlParams)
            error.value = true
            loading.value = false
            return
        }
        if (path === 'machine') {
            machineId.value = id
            showMachineInfo.value = true
            loading.value = false
            Taro.setNavigationBarTitle({ title: t('ui.deviceInfo') })
            return
        }
        const getTargetUrl = pathMapping[path]
        if (!getTargetUrl) {
            console.error('未知的路径类型:', path)
            error.value = true
            loading.value = false
            return
        }
        const targetUrl = getTargetUrl(id)
        setTimeout(() => {
            NavigateTo(targetUrl)
            loading.value = false
        }, 500)
    } catch (err) {
        console.error('解析URL参数或导航失败:', err)
        error.value = true
        loading.value = false
    }
}

/**
 * @description 跳转到设备详情页面
 */
const goToMachineDetails = () => {
    if (machineId.value) {
        const targetUrl = pathMapping.machine(machineId.value)
        NavigateTo(targetUrl)
    }
}

/**
 * @description 跳转到生产日报页面
 */
const goToProductionDaily = () => {
    if (machineId.value) {
        const targetUrl = `/pages/workbench/production-daily/index?deviceId=${machineId.value}`
        NavigateTo(targetUrl)
    }
}

/**
 * @description 显示功能正在开发中的提示
 */
const showDevelopingTip = () => {
    Taro.showToast({
        title: t('ui.featureDeveloping'),
        icon: 'none',
        duration: 2000
    })
}

// 页面加载时自动解析参数并导航
onMounted(() => {
    parseUrlAndNavigate()
})
</script>

<style lang="scss">
.route-jump-container {
    background: #f7f8fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: 0;

    .loading-container {
        text-align: center;
        width: 90%;
        max-width: 500px;

        .loading-text {
            margin-top: 16px;
            color: #666;
            font-size: 14px;
        }
    }

    .error-container {
        text-align: center;

        .nut-button {
            margin-top: 20px;
        }
    }

    .machine-info-container {
        width: 100%;
        padding: 0;
        
        .machine-card {
            width: 100%;
            border-radius: 0;
            background: #fff;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
            margin: 0;
            
            &.custom-card {
                .card-title {
                    align-items: center;
                    display: flex;
                    height: 88px;
                    line-height: 88px;
                    font-size: 32px;
                    font-weight: bold;
                    color: #339af0;
                    padding-left: 10px;
                    
                    &-line {
                        width: 6px;
                        height: 32px;
                        background-color: #339af0;
                        margin-right: 10px;
                    }
                }
                
                .card-content {
                    padding: 15px;
                }
            }
            
            .grid-icon {
                width: 48px;
                height: 48px;
                margin-bottom: 8px;
            }
            
            :deep(.nut-grid-item__text) {
                font-size: 16px;
                color: #333;
            }
        }
    }

    .content-wrapper {
        padding: 20px;
        text-align: center;

        .title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #333;
        }

        .description {
            font-size: 16px;
            color: #666;
        }
    }
}
</style>
