<template>
    <view>
        <nut-cell :title="t('text.name')" :desc="detailsList.name"></nut-cell>
        <nut-cell :title="t('text.encode')" :desc="detailsList.encode"></nut-cell>
        <nut-cell :title="t('text.describe')" :desc="detailsList.describe"></nut-cell>
        <nut-cell :title="t('text.creationTime')" :desc="formatTime(detailsList.creationTime)"></nut-cell>
        <nut-cell :title="'入库状态'">
            <template #desc>
                <nut-tag :color="getStatusColor(detailsList.status)">
                    {{ getStatusText(detailsList.status) }}
                </nut-tag>
            </template>
        </nut-cell>
        <nut-cell :title="'入库类型'">
            <template #desc>
                <nut-tag :color="getTypeColor(detailsList.storageType)">
                    {{ getTypeText(detailsList.storageType) }}
                </nut-tag>
            </template>
        </nut-cell>
        <nut-cell :title="'入库时间'" :desc="formatTime(detailsList.storageTime)"></nut-cell>
        <nut-cell :title="'申请人'" :desc="detailsList.applicantUserName || '-'"></nut-cell>
        <nut-cell :title="'审核人'" :desc="detailsList.reviewerUserName || '-'"></nut-cell>
        <nut-cell :title="'执行人'" :desc="detailsList.executorUserName || '-'"></nut-cell>
        <nut-cell :title="'最后修改时间'" :desc="formatTime(detailsList.lastModificationTime)"></nut-cell>
    </view>
</template>

<script setup lang="ts">
import { t } from '@/locale/fanyi'
import dayjs from 'dayjs'

// 定义 props
defineProps({
    detailsList: {
        type: Object,
        required: true,
    },
})

// 入库类型映射
const storageTypeMap = {
    Ordinary: { type: 'default', text: '普通', color: '#909399' },
    Purchase: { type: 'success', text: '采购', color: '#67C23A' },
    FinishedProduct: { type: 'warning', text: '成品', color: '#E6A23C' },
    SemiFinishedProduct: { type: 'warning', text: '半成品', color: '#E6A23C' },
    RawMaterial: { type: 'primary', text: '原料', color: '#3460fa' },
    Return: { type: 'danger', text: '退货', color: '#F56C6C' },
    Pending: { type: 'warning', text: '待入库', color: '#E6A23C' },
    Other: { type: 'default', text: '其他', color: '#909399' },
    SalesReturn: { type: 'danger', text: '销售退货', color: '#F56C6C' },
}

// 审核状态映射
const auditStatusMap = {
    Newly: { type: 'default', text: '新建', color: '#909399' },
    Pending: { type: 'warning', text: '待审批', color: '#E6A23C' },
    InProgress: { type: 'primary', text: '处理中', color: '#3460fa' },
    Received: { type: 'success', text: '已完成', color: '#67C23A' },
    Returned: { type: 'danger', text: '已退回', color: '#F56C6C' },
    Cancelled: { type: 'default', text: '已取消', color: '#909399' },
    PartiallyReturned: { type: 'warning', text: '部分退回', color: '#E6A23C' },
}

// 获取状态颜色
const getStatusColor = (status: string) => {
    return auditStatusMap[status]?.color || '#909399'
}

// 获取状态文本
const getStatusText = (status: string) => {
    return auditStatusMap[status]?.text || status
}

// 获取类型颜色
const getTypeColor = (type: string) => {
    return storageTypeMap[type]?.color || '#909399'
}

// 获取类型文本
const getTypeText = (type: string) => {
    return storageTypeMap[type]?.text || type
}

// 格式化时间
const formatTime = (time: string) => {
    if (!time) return '-'
    return dayjs(time).format('YYYY-MM-DD HH:mm')
}
</script>

<style lang="scss">
.details-table {
    padding: 0;

    .cell-value {
        text-align: right;
        color: #333;
    }
}
</style>
