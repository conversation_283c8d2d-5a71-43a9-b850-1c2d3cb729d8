import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/model/axios'
import { IdentityUserDto, IdentityUserUpdateDto } from './models'
import { GetIdentityUsersInput } from './models'
import { PagedResultDto } from '@/shared/models/dtos'

export class IdentityUserService {
    get = (id: string, options?: RequestOptions) =>
        defHttp.request<IdentityUserDto>(
            {
                method: 'GET',
                url: `/api/identity/users/${id}`,
            },
            options,
        )

    getList = (input: GetIdentityUsersInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<IdentityUserDto>>(
            {
                method: 'GET',
                url: '/api/identity/users',
                params: {
                    filter: input.filter,
                    sorting: input.sorting,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    update = (id: string, input: IdentityUserUpdateDto, options?: RequestOptions) =>
        defHttp.request<IdentityUserDto>(
            {
                method: 'PUT',
                url: `/api/identity/users/${id}`,
                data: input,
            },
            options,
        )
}
