import type { ReturnResult } from '../../helper/shared/models'
import type {
    WmInWarehouseListInProgressDto,
    WmInWarehouseListIsCancelDto,
    WmInWarehouseListIsInDto,
    WmInWarehouseListSubmitDto,
} from '../wms/dtos/models'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class WorkflowsInWarehouseListService {
    apiName = 'EnterpriseService'

    inProgressByInput = (input: WmInWarehouseListInProgressDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-in-warehouse-list/in-progress',
                data: input,
            },
            options,
        )

    isCancelByInput = (input: WmInWarehouseListIsCancelDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-in-warehouse-list/is-cancel',
                data: input,
            },
            options,
        )

    isInByInput = (input: WmInWarehouseListIsInDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-in-warehouse-list/is-in',
                data: input,
            },
            options,
        )

    submitByInput = (input: WmInWarehouseListSubmitDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-in-warehouse-list/submit',
                data: input,
            },
            options,
        )

    updateStatus = (id: string, status: any, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-in-warehouse-list/update-status',
                params: {
                    id,
                    status,
                },
            },
            options,
        )
}
