<template>
    <view>
        <nut-empty v-if="isFetchError" description="Error" />
        <nut-table v-else :columns="columns" :data="tableData"></nut-table>
    </view>
</template>

<script lang="ts" setup>
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { FactoryDeviceIotFlowListDto } from '@/api/proxy/data-view/models'
import { useDidShow } from '@/hooks/component/hooks'
import Taro from '@tarojs/taro'
import { ref, computed, h } from 'vue'
import { t } from '@/locale/fanyi'
import { handleImages } from '@/utils/image'
import dayjs from 'dayjs'
import { HideLoading, Loading, Toast } from '@/utils/Taro-api'
import { Tag, Progress } from '@nutui/nutui-taro'

const props = withDefaults(
    defineProps<{
        id: string
    }>(),
    {
        id: '',
    },
)

const datavDataFlowService = new DatavDataFlowService()
const deviceDetail = ref<FactoryDeviceIotFlowListDto>()
const isFetchError = ref<boolean>(false)

// 定义表格列
const columns = [
    { title: t('IotService.PropertyName'), key: 'label' },
    {
        title: t('IotService.PropertyValue'),
        key: 'value',
        render(row: any) {
            if (row.renderTag) {
                return h(Tag, { type: row.tagType }, { default: () => row.value })
            } else if (row.renderProgress) {
                return h('view', {}, [
                    h('span', {}, row.value),
                    h(Progress, { style: { marginTop: '10px' }, percentage: row.percentage }),
                ])
            } else if (row.renderImage) {
                return h(
                    'view',
                    {
                        onClick: handleShowImage,
                        style: {
                            color: '#4970F2',
                            cursor: 'pointer',
                        },
                    },
                    t('ui.viewImage') + '>',
                )
            } else {
                return row.value
            }
        },
    },
]

// 计算处理后的表格数据
const tableData = computed(() => {
    if (!deviceDetail.value) return []
    return [
        {
            label: t('Helper.Name'),
            value: deviceDetail.value.name || '-',
            renderTag: false,
        },
        {
            label: t('Helper.Encode'),
            value: deviceDetail.value.encode || '-',
            renderTag: false,
        },
        {
            label: t('Helper.SerialNo'),
            value: deviceDetail.value.serialNo?.toString() || '-',
            renderTag: false,
        },
        {
            label: t('IotService.DeviceForm'),
            value: t(`Platform.${deviceDetail.value.deviceForm}`) || '-',
            renderTag: false,
        },
        {
            label: t('IotService.DeviceType'),
            value: t(`Platform.${deviceDetail.value.deviceType}`) || '-',
            renderTag: false,
        },
        {
            label: t('Helper.IsEnabled'),
            value: deviceDetail.value.isActivate ? t('Helper.Enable') : t('Helper.Disable'),
            renderTag: true,
            tagType: deviceDetail.value.isActivate ? 'success' : 'danger',
        },
        {
            label: t('Helper.DutyUser'),
            value: deviceDetail.value.produceDutyUserName || '-',
            renderTag: false,
        },
        {
            label: t('Platform.MaintainDutyUser'),
            value: deviceDetail.value.maintainDutyUserName || '-',
            renderTag: false,
        },
        {
            label: t('Platform.IotDeviceName'),
            value: deviceDetail.value.iotDeviceName || '-',
            renderTag: false,
        },
        {
            label: t('AbpIdentity.CreationTime'),
            value: deviceDetail.value.creationTime
                ? dayjs(deviceDetail.value.creationTime).format('YYYY-MM-DD HH:mm:ss')
                : '-',
            renderTag: false,
        },
        {
            label: t('Helper.TargetNum'),
            value: deviceDetail.value.targetNum?.toString() || '-',
            renderTag: false,
        },
        {
            label: t('Helper.WorkCount'),
            value: deviceDetail.value.workCount?.toString() || '-',
            renderTag: false,
        },
        {
            label: t('Platform.ProductionProgress'),
            value: `${deviceDetail.value.productivity}%`,
            renderProgress: true,
            percentage: deviceDetail.value.productivity,
        },
        ...(deviceDetail.value.images && deviceDetail.value.images !== '[]'
            ? [
                  {
                      label: t('IotService.DeviceImages'),
                      value: t('ui.viewImage'),
                      renderImage: true,
                  },
              ]
            : []),
    ]
})

/**
 * @description 获取设备详细信息
 */
const fetchData = async () => {
    try {
        Loading()
        if (props.id === undefined || props.id === undefined) {
            Taro.showToast({ title: '设备ID不存在', icon: 'none' })
            return false
        }
        deviceDetail.value = await datavDataFlowService.getByDeviceId(props.id)
        HideLoading()
    } catch (e) {
        Toast(t('ui.requestFailed'), { icon: 'error' })
        isFetchError.value = true
    }
}

/**
 * @description 查看图片
 */
const handleShowImage = () => {
    if (deviceDetail.value?.images === undefined || deviceDetail.value?.images === '{}') {
        return
    }
    const urls = handleImages(deviceDetail.value?.images)
    Taro.previewImage({
        urls: urls ?? [],
    })
}

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {
    await fetchData()
})
</script>
<style>
.nut-cell__value {
    color: black;
}

.nut-table {
    width: 100%;
}
</style>
