<template>
    <view v-if="!warehouseLocationDetail">
        <nut-empty :description="t('helper.noData')"></nut-empty>
    </view>
    <view v-else>
        <nut-cell :title="t('text.warehouseLocationName')" :desc="warehouseLocationDetail?.name" />
        <nut-cell :title="t('text.encode')" :desc="warehouseLocationDetail?.encode" />
        <nut-cell :title="t('text.maxLoad')" :desc="String(warehouseLocationDetail?.maxLoad)" />
        <nut-cell :title="t('text.isActivate')">
            <template #desc>
                <nut-tag :type="warehouseLocationDetail?.isActivate ? 'primary' : 'danger'">
                    {{ warehouseLocationDetail?.isActivate ? t('tag.enable') : t('tag.false') }}
                </nut-tag>
            </template>
        </nut-cell>
        <nut-cell :title="t('text.positionX')" :desc="String(warehouseLocationDetail?.positionX)" />
        <nut-cell :title="t('text.positionY')" :desc="String(warehouseLocationDetail?.positionY)" />
        <nut-cell :title="t('text.positionZ')" :desc="String(warehouseLocationDetail?.positionZ)" />
        <nut-cell :title="t('text.dutyUser')" :desc="dutyUserName || '-'" />
        <nut-cell :title="t('text.describe')" :desc="warehouseLocationDetail?.describe" />
        <nut-cell :title="t('text.creationTime')">
            <template #desc>
                {{ dayjs(warehouseLocationDetail?.creationTime).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
        </nut-cell>
    </view>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import { WmWarehouseLocationService } from '@/api/proxy/enterprise/controller/wm-warehouse-location.service'
import { WmWarehouseLocationDetailDto } from '@/api/proxy/enterprise/wms/dtos/models'
import { ref, watch } from 'vue'
import { useDidShow, useRouter } from '@tarojs/taro'
import dayjs from 'dayjs'

Taro.setNavigationBarTitle({ title: t('menu.warehouseLocationDetails') })

const wmWarehouseLocationService = new WmWarehouseLocationService()
const warehouseLocationDetail = ref<WmWarehouseLocationDetailDto>()
const locationId = ref<string>()
const dutyUserName = ref<string>()

const fetchData = async () => {
    try {
        if (locationId.value) {
            const result = await wmWarehouseLocationService.get(locationId.value)
            warehouseLocationDetail.value = result
        }
    } catch (error) {
        console.error('获取仓库库位详情失败', error)
    }
}

watch(
    () => locationId.value,
    () => {
        fetchData()
    },
)

useDidShow(() => {
    const route = useRouter()
    locationId.value = route.params.id
    dutyUserName.value = route.params.dutyUserName ? decodeURIComponent(route.params.dutyUserName) : '-'
})
</script>

<style lang="scss"></style>
