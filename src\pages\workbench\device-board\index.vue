<template>
    <nut-tabs
        v-model="tabsValue"
        :animated-time="0"
        :title-scroll="true"
        title-gutter="25"
        :auto-height="true"
        :ellipsis="false"
    >
        <nut-tab-pane :title="$t('ui.dataView')" pane-key="1">
            <DialogSelectDate @show:visible="handleShowDateSelect" @update:selectDate="handleSelectDate" />
            <view v-if="isActive('1') && !isShowDataSelect">
                <ProportionOfEquipmentUsage :date-picker="selectDate" />
                <DeviceWorkModeRate :date-picker="selectDate" />
                <DeviceStatusRate :date-picker="selectDate" />
                <DeviceHistoryWorkMode :date-picker="selectDate" />
                <DeviceHistoryStatus :date-picker="selectDate" />
            </view>
        </nut-tab-pane>
        <nut-tab-pane :title="$t('ui.startup')" pane-key="2" style="padding: 0">
            <PowerOnDeviceTable v-if="isActive('2')"></PowerOnDeviceTable>
        </nut-tab-pane>
    </nut-tabs>
</template>

<script lang="ts" setup>
import { useDidShow } from '@/hooks/component/hooks'
import { computed, ref } from 'vue'
import ProportionOfEquipmentUsage from '@/pages/workbench/device-list/data-view/proportion-of-equipment-usage.vue'
import DeviceWorkModeRate from '@/pages/workbench/device-list/data-view/device-work-mode-rate.vue'
import DeviceStatusRate from '@/pages/workbench/device-list/data-view/device-status-rate.vue'
import PowerOnDeviceTable from '@/pages/workbench/device-board/component/power-on-device-table.vue'
import DeviceHistoryStatus from '@/pages/workbench/device-list/data-view/device-history-status.vue'
import DeviceHistoryWorkMode from '@/pages/workbench/device-list/data-view/device-history-work-mode.vue'
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'

/**
 * @description 修改导航栏标题
 */
Taro.setNavigationBarTitle({ title: t('menu.deviceKanban') })

const tabsValue = ref<string>('1')
const isShowDataSelect = ref<boolean>(false)
const selectDate = ref<string>('')
const isActive = computed(() => {
    return (key: string) => {
        return tabsValue.value === key
    }
})

const click = () => {
    console.log('click')
}

/**
 * @description 选择日期弹出框显示
 */
const handleShowDateSelect = (isShow: boolean) => {
    isShowDataSelect.value = isShow
}

/**
 * @description 选择日期
 */
const handleSelectDate = (date: string) => {
    selectDate.value = date
}

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {})
</script>

<style>
.nut-cell__value {
    color: black;
}

.backtop-demo .item {
    height: 46px;
    background: #efefef;
    line-height: 46px;
    padding: 0px 20px;
    margin-bottom: 20px;
}
</style>
