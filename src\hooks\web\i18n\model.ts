export interface AlainI18NService {
    [key: string]: NzSafeAny

    /**
     * Call `use` to trigger change notification
     *
     * 调用 `use` 触发变更通知
     */
    //   readonly change: Observable<string>;

    /**
     * Get the default language
     *
     * 获取默认语言
     */
    readonly defaultLang: string

    /**
     * Get current language
     *
     * 获取当前语言
     */
    readonly currentLang: string

    /**
     * Change language
     *
     * 变更语言
     *
     * @param emit 是否触发 `change`，默认：true ; Should be removed, please use `change` event instead.
     */
    use(lang: string, data?: Record<string, unknown>): void

    /**
     * Return to the current language list
     *
     * 返回当前语言列表
     */
    getLangs(): NzSafeAny[]

    /**
     * Translate 翻译
     *
     * @param params 模板所需要的参数对象
     * @param isSafe 是否返回安全字符，自动调用 `bypassSecurityTrustHtml`; Should be removed, If you need SafeHtml support, please use `| html` pipe instead.
     */
    fanyi(path: string, params?: unknown): string
}

export declare type NzSafeAny = any
