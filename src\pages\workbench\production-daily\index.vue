<template>
    <view>
        <nut-cell :title="`${$t('ui.dataDeadline')}`" :desc="dailyData.dataDeadline" />
        <nut-cell :title="`${$t('Platform.TotalNum')}`" :desc="dailyData.total" />
        <nut-cell :title="`${$t('Platform.QualifiedNum')}`" :desc="dailyData.qualified" />
        <nut-cell :title="`${$t('ui.qualifiedRate')}`" :desc="`${dailyData.qualifiedRate}%`" />
        <view style="margin-top: 20px; padding: 20px">
            <ProductionStatistics :deviceId="fetchParams.deviceId" :isDevice="!!fetchParams.deviceId" />
        </view>
    </view>
    <nut-empty v-if="isFetchError" description="Error" />
</template>

<script lang="ts" setup>
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { GetDeviceTimeRangeInput } from '@/api/proxy/data-view/models'
import { reactive, ref } from 'vue'
import ProductionStatistics from '@/pages/workbench/device-list/data-view/production-statistics.vue'
import { useDidShow, usePullDownRefresh } from '@/hooks/component/hooks'
import { HideLoading, Loading, Toast } from '@/utils/Taro-api'
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import dayjs from 'dayjs'

/**
 * @description 更改导航栏标题
 */
Taro.setNavigationBarTitle({ title: t('menu.productionDaily') })

const datavDataFlowService = new DatavDataFlowService()
const isFetchError = ref<boolean>(false)
const fetchParams = reactive<GetDeviceTimeRangeInput>({
    factoryModelId: '',
    deviceId: '',
    startTime: `${dayjs().subtract(24, 'hour').format('YYYY-MM-DD HH:mm:ss').toString()}`,
    endTime: `${dayjs().format('YYYY-MM-DD HH:mm:ss').toString()}`,
})
const dailyData = reactive<any>({
    total: 0, //总数
    qualified: 0, //合格数
    qualifiedRate: 0, //合格率
    dataDeadline: '', //数据截至时间
})

/**
 * @description 获取生产日报数据
 */
const fetchData = async () => {
    try {
        Loading()
        const result = await datavDataFlowService.getWorkCountStatistics(fetchParams)
        dailyData.total = result.map((item: any) => item.totalNum).reduce((prev: any, next: any) => prev + next)
        dailyData.qualified = result.map((item: any) => item.qualifiedNum).reduce((prev: any, next: any) => prev + next)
        dailyData.qualifiedRate = dailyData.total === 0 ? 0 : (dailyData.qualified / dailyData.total) * 100
        dailyData.dataDeadline = result[result.length - 1].time //获取数据截至时间
        HideLoading()
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
        isFetchError.value = true
    }
}

/**
 * @description 从URL参数中获取设备ID
 */
const getDeviceIdFromParams = () => {
    const params = Taro.getCurrentInstance().router?.params
    if (params && params.deviceId) {
        fetchParams.deviceId = params.deviceId
    }
}

useDidShow(() => {
    getDeviceIdFromParams()
    fetchData()
})

usePullDownRefresh(async () => {
    await fetchData()
    Taro.stopPullDownRefresh()
})
</script>
<style></style>
