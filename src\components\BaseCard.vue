<template>
    <view class="card" :class="{ shadow: props.isShadow }">
        <view class="card-title">
            <view class="card-title-line"></view>
            <text> {{ props.title }}</text>
            <view class="card-title-sub">
                <slot name="sub-title">
                    <text> {{ props.subTitle }}</text>
                </slot>
            </view>
        </view>
        <view class="card-content">
            <slot></slot>
        </view>
    </view>
</template>

<script lang="ts" setup>
const props = withDefaults(
    defineProps<{
        title?: string
        subTitle?: string
        isShadow?: boolean
    }>(),
    {
        title: '',
        subTitle: '',
        isShadow: true,
    },
)

defineOptions({
    name: 'BaseCard',
})
</script>

<style lang="scss">
.card {
    margin: 12px 10px;
    background-color: #ffffff;

    &-title {
        align-items: center;
        display: flex;
        height: 88px;
        line-height: 88px;
        font-size: 32px;
        font-weight: bold;
        color: #339af0;
        padding-left: 10px;
        &-line {
            width: 6px;
            height: 32px;
            background-color: #339af0;
            margin-right: 10px;
        }
        &-sub {
            display: flex;
            align-items: center;
            color: gray;
            font-size: 28px;
        }
    }
}
.shadow {
    box-shadow: 0px 0px 3px 1px rgba(166, 165, 165, 0.08);
}
</style>
