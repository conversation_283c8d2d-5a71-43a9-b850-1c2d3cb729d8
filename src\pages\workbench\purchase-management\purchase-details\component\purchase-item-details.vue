<template>
    <view class="purchase-item-details">
        <view v-if="loading" class="loading-container">
            <nut-skeleton rows="10" title animated />
        </view>
        <view v-else>
            <nut-cell :title="'类型'">
                <template #desc>
                    <nut-tag :color="getTypeColor(itemDetails.type)">
                        {{ getTypeText(itemDetails.type) }}
                    </nut-tag>
                </template>
            </nut-cell>
            <nut-cell :title="t('text.name')" :desc="itemDetails.name || '-'"></nut-cell>
            <nut-cell :title="t('text.encode')" :desc="itemDetails.encode || '-'"></nut-cell>
            <nut-cell :title="'状态'">
                <template #desc>
                    <nut-tag :color="getStatusColor(itemDetails.status)">
                        {{ getStatusText(itemDetails.status) }}
                    </nut-tag>
                </template>
            </nut-cell>

            <template v-if="itemDetails.type === 'Material'">
                <nut-cell :title="'物料名称'" :desc="materialName || '-'"></nut-cell>
            </template>

            <template v-else>
                <nut-cell :title="'分类'" :desc="categoryName || '-'"></nut-cell>
                <nut-cell :title="'计量单位'" :desc="measureUnitName || '-'"></nut-cell>
                <nut-cell :title="'供应商'" :desc="supplierName || '-'"></nut-cell>
            </template>

            <nut-cell :title="'数量'" :desc="itemDetails.quantity || 0"></nut-cell>
            <nut-cell :title="'单价'">
                <template #desc>
                    <text class="price-value">¥{{ itemDetails.unitPrice || 0 }}</text>
                </template>
            </nut-cell>
            <nut-cell :title="'总价'">
                <template #desc>
                    <text class="total-price-value">¥{{ itemDetails.totalPrice || 0 }}</text>
                </template>
            </nut-cell>
            <nut-cell :title="t('text.describe')" :desc="itemDetails.description || '-'"></nut-cell>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { t } from '@/locale/fanyi'
import { PurchaseItemsService } from '@/api/proxy/enterprise/controller/purchase-items.service'
import { WmCategoryService } from '@/api/proxy/enterprise/controller/wm-category.service'
import { WmMeasureUnitService } from '@/api/proxy/enterprise/controller/wm-measure-unit.service'
import { WmSupplierService } from '@/api/proxy/enterprise/controller/wm-supplier.service'
import { WmMaterialService } from '@/api/proxy/enterprise/controller/wm-material.service'
import { Loading, HideLoading, Toast } from '@/utils/Taro-api'

// 初始化服务
const purchaseItemsService = new PurchaseItemsService()
const wmCategoryService = new WmCategoryService()
const wmMeasureUnitService = new WmMeasureUnitService()
const wmSupplierService = new WmSupplierService()
const wmMaterialService = new WmMaterialService()

// 详情数据
const itemDetails = ref<any>({})
const loading = ref(false)
const categoryName = ref('')
const measureUnitName = ref('')
const supplierName = ref('')
const materialName = ref('')

// 类型颜色映射
const getTypeColor = (type: string) => {
    switch (type) {
        case 'Material':
            return '#67C23A' // 绿色
        case 'Temporary':
            return '#E6A23C' // 橙色
        default:
            return '#909399' // 灰色
    }
}

// 类型文本映射
const getTypeText = (type: string) => {
    switch (type) {
        case 'Material':
            return '物料'
        case 'Temporary':
            return '临时'
        default:
            return type
    }
}

// 状态颜色映射
const getStatusColor = (status: string) => {
    switch (status) {
        case 'Newly':
            return '#909399' // 灰色
        case 'PendingApproval':
            return '#E6A23C' // 橙色
        case 'Approved':
            return '#67C23A' // 绿色
        case 'Purchasing':
            return '#E6A23C' // 橙色
        case 'Ordered':
            return '#E6A23C' // 橙色
        case 'Rejected':
            return '#F56C6C' // 红色
        case 'Cancelled':
            return '#909399' // 灰色
        case 'Completed':
            return '#67C23A' // 绿色
        case 'Returned':
            return '#F56C6C' // 红色
        default:
            return '#909399' // 灰色
    }
}

// 状态文本映射
const getStatusText = (status: string) => {
    switch (status) {
        case 'Newly':
            return '新建'
        case 'PendingApproval':
            return '待审批'
        case 'Approved':
            return '已审批'
        case 'Purchasing':
            return '采购中'
        case 'Ordered':
            return '已下单'
        case 'Rejected':
            return '已拒绝'
        case 'Cancelled':
            return '已取消'
        case 'Completed':
            return '已完成'
        case 'Returned':
            return '有退回'
        default:
            return status
    }
}

/**
 * 获取分类名称
 */
const fetchCategoryName = async (id: string) => {
    if (!id) return
    try {
        const result = await wmCategoryService.get(id)
        categoryName.value = result.name || '-'
    } catch (error) {
        console.error('获取分类名称失败:', error)
        categoryName.value = '-'
    }
}

/**
 * 获取计量单位名称
 */
const fetchMeasureUnitName = async (id: string) => {
    if (!id) return
    try {
        const result = await wmMeasureUnitService.get(id)
        measureUnitName.value = result.name || '-'
    } catch (error) {
        console.error('获取计量单位名称失败:', error)
        measureUnitName.value = '-'
    }
}

/**
 * 获取供应商名称
 */
const fetchSupplierName = async (id: string) => {
    if (!id) return
    try {
        const result = await wmSupplierService.get(id)
        supplierName.value = result.name || '-'
    } catch (error) {
        console.error('获取供应商名称失败:', error)
        supplierName.value = '-'
    }
}

/**
 * 获取物料名称
 */
const fetchMaterialName = async (id: string) => {
    if (!id) return
    try {
        const result = await wmMaterialService.get(id)
        materialName.value = result.name || '-'
    } catch (error) {
        console.error('获取物料名称失败:', error)
        materialName.value = '-'
    }
}

/**
 * 获取采购子项详细信息
 */
const fetchItemDetails = async (id: string) => {
    if (!id) return

    try {
        loading.value = true
        Loading()

        // 获取采购子项详情
        const result = await purchaseItemsService.get(id)

        if (result) {
            itemDetails.value = result

            // 如果是物料类型，获取物料名称
            if (result.type === 'Material' && result.materialId) {
                await fetchMaterialName(result.materialId)
            }
            // 如果是临时类型，获取额外信息
            else if (result.type === 'Temporary') {
                await Promise.all([
                    result.categoryId && fetchCategoryName(result.categoryId),
                    result.measureUnitId && fetchMeasureUnitName(result.measureUnitId),
                    result.supplierId && fetchSupplierName(result.supplierId),
                ])
            }
        } else {
            itemDetails.value = {}
            Toast('未找到数据', { icon: 'error' })
        }
    } catch (error) {
        console.error('获取数据错误:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        loading.value = false
        HideLoading()
    }
}

// 导出方法
defineExpose({
    fetchData: fetchItemDetails,
})
</script>

<style lang="scss" scoped>
.purchase-item-details {
    background-color: #fff;

    .loading-container {
        padding: 20px 0;
    }

    .price-value {
        color: #0066ff;
        font-weight: 500;
    }

    .total-price-value {
        color: #f56c6c;
        font-weight: 500;
    }
}
</style>
