page {
  background-color: $page-bg-color;
}

// 盒子的子级元素按行排
.g-flex-centen {
  display: flex;
  align-items: center;
}

// 盒子的子级元素按列排
.g-flex-column {
  display: flex;
  flex-direction: column;
}

// 大的标题
.g-title-large {
  @include title-font($title-font-size-large);
}

// 正常的标题
.g-title {
  @include title-font();
}

// 水平居中
.g-horizontal-center {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
}




// page, :root {
//     --nut-primary-color: #fa2c19;
//     --nut-primary-color-end: #fa6419;
// }
