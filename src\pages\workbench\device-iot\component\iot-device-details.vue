<template>
    <nut-tabs v-model="tabValue">
        <nut-tab-pane :title="$t('IotService.IotDevice')" pane-key="1" style="padding: 0">
            <view v-if="tabValue === '1'">
                <IotDeviceDetailsInfo />
            </view>
        </nut-tab-pane>
        <nut-tab-pane :title="$t('IotService.SubDevice')" pane-key="2">
            <view v-if="tabValue === '2'">
                <IotDeviceDetailsSubDevice />
            </view>
        </nut-tab-pane>
    </nut-tabs>
    <nut-empty v-if="isFetchError" description="Error" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useDidShow, usePullDownRefresh } from '@/hooks/component/hooks'
import IotDeviceDetailsInfo from './iot-device-details-info.vue'
import IotDeviceDetailsSubDevice from './iot-device-details-sub-device.vue'

definePageConfig({
    enablePullDownRefresh: true,
})

const tabValue = ref<string>('1')
const isFetchError = ref<boolean>(false)

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {})

/**
 * @description 页面下拉刷新
 */
usePullDownRefresh(async () => {})
</script>

<style lang="scss"></style>
