import { getAppToken } from '@/utils/getAppInfo'
import { Toast } from '@/utils/Taro-api'
import Taro from '@tarojs/taro'

/**
 * @description 路由拦截器
 */
export const routerInterceptor = () => {
    //当前页面路径
    const currentPath = Taro.getCurrentInstance().router?.path
    //判断是否在白名单中
    const isWhite = whiteList.includes(currentPath ?? '')

    //拦截处理
    if (!isWhite) {
        //判断是否登录
        const isLogin = getAppToken()
        if (!isLogin) {
            Taro.reLaunch({
                url: '/pages/subpackages/login/index',
                complete: () => {
                    Toast('该用户未登录', { icon: 'error' })
                },
            })
        }
    }
}

const whiteList = [
    '/pages/subpackages/login/index',
    '/pages/subpackages/wechat-login/index',
    '/pages/home/<USER>',
    '/pages/workbench/index',
    '/pages/profile/index',
]
