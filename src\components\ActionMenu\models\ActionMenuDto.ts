export interface ActionMenuItem {
    /**
     * 操作项文本
     */
    text: string
    /**
     * 操作项值
     */
    value: string | number
    /**
     * 是否禁用
     */
    disabled?: boolean
    /**
     * 图标名称（如果需要）
     */
    icon?: string
}

export interface ActionMenuProps {
    /**
     * 操作项列表
     */
    options: ActionMenuItem[]
    /**
     * 触发按钮文本
     */
    buttonText?: string
    /**
     * 按钮类型
     */
    buttonType?: string
    /**
     * 按钮大小
     */
    buttonSize?: string
    /**
     * 按钮是否为朴素样式
     */
    buttonPlain?: boolean
    /**
     * 是否禁用
     */
    disabled?: boolean
    /**
     * 自定义样式
     */
    customStyle?: string
    /**
     * 自定义类名
     */
    customClass?: string
    /**
     * 点击外部是否自动关闭
     */
    closeOnClickOutside?: boolean
    /**
     * 下拉菜单展开方向，可选值：'left' | 'right' | 'top' | 'bottom'
     */
    direction?: 'left' | 'right' | 'top' | 'bottom'
}
