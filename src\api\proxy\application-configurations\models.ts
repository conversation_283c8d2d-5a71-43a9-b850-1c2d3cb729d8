import type { ObjectExtensionsDto } from './object-extending/models'

export interface ApplicationAuthConfigurationDto {
    policies: Record<string, boolean>
    grantedPolicies: Record<string, boolean>
}

export interface ApplicationConfigurationDto {
    localization: any
    auth: ApplicationAuthConfigurationDto
    setting: ApplicationSettingConfigurationDto
    currentUser: CurrentUserDto
    features: ApplicationFeatureConfigurationDto
    multiTenancy: any
    currentTenant: any
    timing: TimingDto
    clock: ClockDto
    objectExtensions: ObjectExtensionsDto
}

export interface ApplicationFeatureConfigurationDto {
    values: Record<string, string>
}

export interface ApplicationSettingConfigurationDto {
    values: Record<string, string>
}

export interface ClockDto {
    kind?: string
}

export interface CurrentCultureDto {
    displayName?: string
    englishName?: string
    threeLetterIsoLanguageName?: string
    twoLetterIsoLanguageName?: string
    isRightToLeft: boolean
    cultureName?: string
    name?: string
    nativeName?: string
    dateTimeFormat: DateTimeFormatDto
}

export interface CurrentUserDto {
    isAuthenticated: boolean
    id?: string
    tenantId?: string
    userName?: string
    name?: string
    surName?: string
    email?: string
    emailVerified: boolean
    phoneNumber?: string
    phoneNumberVerified: boolean
    roles: string[]
}

export interface DateTimeFormatDto {
    calendarAlgorithmType?: string
    dateTimeFormatLong?: string
    shortDatePattern?: string
    fullDateTimePattern?: string
    dateSeparator?: string
    shortTimePattern?: string
    longTimePattern?: string
}

export interface IanaTimeZone {
    timeZoneName?: string
}

export interface TimeZone {
    iana: IanaTimeZone
    windows: WindowsTimeZone
}

export interface TimingDto {
    timeZone: TimeZone
}

export interface WindowsTimeZone {
    timeZoneId?: string
}
