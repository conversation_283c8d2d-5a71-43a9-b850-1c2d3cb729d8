import { l } from '@/locale/setupI18n'
import { Toast } from '@/utils/Taro-api'
import { isAuthority, isGranted } from '../web/acl/permission'
import { useDidShow, usePullDownRefresh, useReachBottom } from './hooks'

export const useAppBase = () => {
    return {
        l,
        isGranted,
        isAuthority,
        Toast,
        useDidShow,
        usePullDownRefresh,
        useReachBottom,
    }
}
