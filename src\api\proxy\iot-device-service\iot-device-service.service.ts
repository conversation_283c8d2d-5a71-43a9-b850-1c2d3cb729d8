import { PagedResultDto } from '@/shared/models/dtos'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/model/axios'
import { GetIotDeviceInput, IotDeviceListDto, IotHubDeviceDetailedDto, ReturnResult } from './models'

export class IotDeviceService {
    getPaged = (input: GetIotDeviceInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<IotDeviceListDto>>(
            {
                method: 'GET',
                url: '/api/platform/iot-device/paged',
                params: {
                    nodeType: input.nodeType,
                    gatewayId: input.gatewayId,
                    useStatus: input.useStatus,
                    tenantId: input.tenantId,
                    maxResultCount: input.maxResultCount,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                },
            },
            options,
        )

    getDeviceById = (deviceId: string, options?: RequestOptions) =>
        defHttp.request<ReturnResult<IotHubDeviceDetailedDto>>(
            {
                method: 'GET',
                url: '/api/iot-hub/device/detailed',
                params: {
                    deviceId,
                },
            },
            options,
        )

    connectGateway = (gatewayId: string, port: string, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/platform/iot-device/connec-to-gateway',
                params: {
                    gatewayId,
                    port,
                },
            },
            options,
        )
}
