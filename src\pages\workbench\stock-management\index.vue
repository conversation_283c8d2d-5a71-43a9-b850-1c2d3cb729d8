<template>
    <view>
        <view>
            <nut-menu>
                <nut-menu-item ref="searchBox" :title="t('ui.search')">
                    <nut-searchbar v-model="editSearch" :clearable="true" :placeholder="t('text.pleaseEnterName')">
                        <template #leftin>
                            <Search2 />
                        </template>
                        <template #rightout>
                            <view style="display: flex; gap: 10px">
                                <nut-button type="primary" size="small" @click="search">
                                    {{ t('ui.search') }}
                                </nut-button>
                                <nut-button type="primary" plain size="small" @click="resetSearch">
                                    {{ t('ui.reset') }}
                                </nut-button>
                            </view>
                        </template>
                    </nut-searchbar>
                </nut-menu-item>
            </nut-menu>
        </view>
        <view class="category-section">
            <nut-collapse v-model="activeCollapse">
                <nut-collapse-item :title="t('text.materailCategory')" name="1">
                    <CategoryTree ref="categoryTreeRef" @click-node="handleCategorySelect" />
                </nut-collapse-item>
            </nut-collapse>
        </view>

        <view style="display: flex; justify-content: space-between; align-items: center; padding: 12px">
            <view v-if="selectedCategory.name">
                <view style="display: flex; align-items: center; gap: 8px">
                    <nut-tag type="primary">{{ selectedCategory.name }}</nut-tag>
                    <nut-button size="small" type="default" @click="resetCategory">
                        {{ t('ui.resetCategory') }}
                    </nut-button>
                </view>
            </view>
            <view>
                <nut-button class="custom-btn outline-btn" size="small" @click="loading">
                    {{ t('ui.refresh') }}
                </nut-button>
            </view>
        </view>

        <view class="content-wrapper">
            <view v-if="stockList.length > 0">
                <view v-for="(item, index) in stockList" :key="index" @click="handleInfo(item)">
                    <InformationCard :isActivate="!item.isFrozen" :show-empty-image="false">
                        <template #front-title> [{{ index + 1 }}] </template>
                        <template #title>
                            {{ getMaterialName(item.materialId) || t('text.unName') }}
                        </template>
                        <template #line-first> {{ t('text.stock') }}：{{ item.quantity }} </template>
                        <template #line-second> {{ t('text.batchCode') }}：{{ item.batchCode }} </template>
                        <template #space-one>
                            <view style="display: flex; gap: 8px">
                                {{ t('text.stockStatus') }}：
                                <nut-tag :type="getStatusType(item.stockStatus)" style="margin-bottom: 0.5vh">
                                    {{ getStatusText(item.stockStatus) }}
                                </nut-tag>
                            </view>
                        </template>
                        <template #space-two>
                            <nut-tag type="warning" style="margin-bottom: 0.5vh">
                                {{ item.isFrozen ? t('text.isFrozen') : t('text.isNotFrozen') }}
                            </nut-tag>
                        </template>
                    </InformationCard>
                </view>
            </view>
            <view v-else class="empty-state">
                <nut-empty description="暂无数据" image="empty"> </nut-empty>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { useDidShow } from '@/hooks/component/hooks'
import { ref } from 'vue'
import { t } from '@/locale/fanyi'
import { Search2 } from '@nutui/icons-vue-taro'
import { WmStockQuantityService } from '@/api/proxy/enterprise/controller/wm-stock-quantity.service'
import { WmMaterialService } from '@/api/proxy/enterprise/controller'
import { NavigateTo, Toast, Loading, HideLoading } from '@/utils/Taro-api'
import InformationCard from '@/components/InformationCard.vue'
import CategoryTree from '@/components/CategoryTree.vue'
import dayjs from 'dayjs'

// 库存接口，匹配API返回的WmStockQuantityListDto结构
interface StockQuantityItem {
    id: string
    name: string
    encode: string
    quantity: number
    costPrice: number
    stockStatus: string
    batchCode?: string
    expireDate?: string
    isFrozen: boolean
    locationId?: string
    materialId?: string
    specification?: string
    model?: string
    creationTime?: string
    type: string
    isActivate: boolean
    isDeleted: boolean
    categoryId?: string
    measureUnitId?: string
    maxStock: number
    minStock: number
}

const stockQuantityService = new WmStockQuantityService()
const materialService = new WmMaterialService()

// 添加物料缓存
const materialMap = ref<Map<string, any>>(new Map())
// 添加请求中的物料ID集合，避免重复请求
const pendingMaterialIds = ref<Set<string>>(new Set())

// 库存状态映射
const stockStatusMap = {
    Available: { type: 'success', text: '可用' },
    Reserved: { type: 'warning', text: '已预留' },
    InTransit: { type: 'info', text: '在途' },
    Damaged: { type: 'danger', text: '损坏' },
    Expired: { type: 'danger', text: '过期' },
    OutOfStock: { type: 'danger', text: '缺货' },
}

const searchBox = ref()
const editSearch = ref('')
const stockList = ref<StockQuantityItem[]>([])
const originalList = ref<StockQuantityItem[]>([])
const isLoading = ref(false)

// 添加分类选择相关状态
const activeCollapse = ref<string[]>([]) // 默认不展开分类面板
const selectedCategory = ref<{ id: string; name: string }>({ id: '', name: '' })
const categoryTreeRef = ref<any>(null)

// 获取状态类型
const getStatusType = (status: string) => {
    return stockStatusMap[status]?.type || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
    return stockStatusMap[status]?.text || status
}

// 加载缺失的物料数据
const loadMissingMaterial = async (materialId: string) => {
    // 如果该物料ID已经在请求中，则不重复请求
    if (pendingMaterialIds.value.has(materialId)) {
        return ''
    }
    // 将物料ID添加到请求中的集合
    pendingMaterialIds.value.add(materialId)

    try {
        const result = await materialService.get(materialId)
        if (result && result.name) {
            // 将获取到的物料信息添加到缓存中
            materialMap.value.set(materialId, {
                name: result.name,
                encode: result.encode,
                model: result.model,
                type: result.type,
                // WmMaterialDetailDto中没有materialsAuditStatus属性，使用默认值
                materialsAuditStatus: 'Approved', // 默认设置为已审核状态
            })
            // 强制触发视图更新
            stockList.value = [...stockList.value]
            return result.name
        }
    } catch (error) {
        console.error(`获取物料(ID: ${materialId})信息失败:`, error)
    } finally {
        pendingMaterialIds.value.delete(materialId)
    }
    return ''
}

// 获取物料名称 - 同步函数，用于模板中显示
const getMaterialName = (materialId?: string) => {
    if (!materialId) return ''

    // 首先从缓存中查找
    const cachedMaterial = materialMap.value.get(materialId)
    if (cachedMaterial?.name) {
        return cachedMaterial.name
    }

    loadMissingMaterial(materialId)
    return '未命名'
}

// 加载物料数据
const loadMaterialData = async () => {
    try {
        // 如果已经加载过，就不再重复加载
        if (materialMap.value.size > 0) return

        const result = await materialService.getPaged({
            isDeleted: false,
            sorting: '',
        })

        if (result?.items) {
            materialMap.value = new Map(
                result.items.map(item => [
                    item.id,
                    {
                        name: item.name,
                        encode: item.encode,
                        model: item.model,
                        type: item.type,
                        materialsAuditStatus: item.materialsAuditStatus,
                    },
                ]),
            )
        }
    } catch (error) {
        console.error('加载物料数据失败:', error)
    }
}

// 添加关闭折叠面板的方法
const closeCollapsePanel = () => {
    // 从数组中移除面板名称，而不是设置为空数组
    activeCollapse.value = activeCollapse.value.filter(name => name !== '1')
}

// 处理分类选择
const handleCategorySelect = (category: { id?: string; name?: string }) => {
    selectedCategory.value = {
        id: category.id || '',
        name: category.name || '',
    }

    // 选择后自动关闭面板
    closeCollapsePanel()

    // 重新加载数据，根据分类筛选
    loadData()
}

// 重置分类选择
const resetCategory = () => {
    // 清除分类选择
    if (categoryTreeRef.value) {
        categoryTreeRef.value.clearSelection()
    }
    selectedCategory.value = { id: '', name: '' }

    // 重新加载数据
    loadData()
}

// 修改加载数据函数，添加分类筛选
const loadData = async () => {
    try {
        Loading()
        isLoading.value = true

        // 确保物料数据已加载
        await loadMaterialData()

        // 构建查询参数，添加分类ID
        const params: any = {
            isDeleted: false,
        }

        // 如果选择了分类，则添加分类ID参数
        if (selectedCategory.value.id && selectedCategory.value.id !== 'root') {
            params.categoryId = selectedCategory.value.id
        }

        const result = await stockQuantityService.getPaged(params)

        if (result && result.items) {
            originalList.value = result.items
            stockList.value = result.items
        } else {
            originalList.value = []
            stockList.value = []
        }
    } catch (error) {
        console.error('获取数据失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
        originalList.value = []
        stockList.value = []
    } finally {
        HideLoading()
        isLoading.value = false
    }
}

// 搜索
const search = async () => {
    try {
        Loading()
        // 如果搜索框为空，直接重新加载所有数据
        if (editSearch.value.trim() === '') {
            await loadData()
        } else {
            // 构建搜索参数，支持按名称、编码和描述搜索
            const searchParams = {
                filterText: editSearch.value.trim(),
                maxResultCount: 50,
            }
            // 使用 API 进行搜索
            const result = await stockQuantityService.getPaged(searchParams)

            if (result && result.items) {
                originalList.value = result.items
                stockList.value = result.items
            } else {
                originalList.value = []
                stockList.value = []
            }
        }

        if (searchBox.value && typeof searchBox.value.toggle === 'function') {
            searchBox.value.toggle()
        }
    } catch (error) {
        console.error('搜索失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

// 重置搜索，同时清除分类选择
const resetSearch = async () => {
    editSearch.value = ''
    // 清除分类选择
    resetCategory()

    await loadData()
    if (searchBox.value && typeof searchBox.value.toggle === 'function') {
        searchBox.value.toggle()
    }
}

// 刷新数据
const loading = async () => {
    Loading()
    await loadData()
    HideLoading()
}

// 查看详情
const handleInfo = (item: StockQuantityItem) => {
    NavigateTo(`/pages/workbench/stock-management/stock-details/index?id=${item.id}`)
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    // 确保页面加载数据
    loadData()
})
</script>

<style lang="scss">
.content-wrapper {
    padding: 12px;
    background: #f5f5f5;
    min-height: calc(100vh - 100px);
}

.empty-state {
    padding: 40px 0;
    text-align: center;
}

.custom-btn {
    &.primary-btn {
        background-color: #4970f2;
    }

    &.outline-btn {
        color: #4970f2;
        border: 1px solid #4970f2;
    }
}

.category-section {
    margin: 0 12px;
    border-radius: 8px;
    overflow: hidden;
}
</style>
