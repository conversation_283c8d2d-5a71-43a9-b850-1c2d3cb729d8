import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/model/axios'
import { FactoryModelsTreeNodesDto } from './models'

export class FactoryModelsService {
    getTreeNodes = (filterText: string, options?: RequestOptions) =>
        defHttp.request<FactoryModelsTreeNodesDto[]>(
            {
                method: 'GET',
                url: '/api/platform/factory-models/options-trees',
                params: {
                    filterText,
                },
            },
            options,
        )
}
