import { ref } from 'vue'
import Taro from '@tarojs/taro'

// 定义授权类型枚举
export enum AuthScope {
    Camera = 'scope.camera',
    PhotoAlbum = 'scope.writePhotosAlbum',
    UserLocation = 'scope.userLocation',
    UserInfo = 'scope.userInfo',
    // 可以根据需要添加其他授权类型
}

// 授权状态接口
export interface AuthState {
    [key: string]: boolean
}

/**
 * 通用授权处理钩子
 */
export function useAuth() {
    // 所有授权的状态
    const authState = ref<AuthState>({})
    /**
     * 检查指定授权的状态
     * @param scopes 要检查的授权范围数组
     */
    const checkAuth = (scopes: AuthScope[]) => {
        Taro.getSetting({
            success: res => {
                scopes.forEach(scope => {
                    authState.value[scope] = !!res.authSetting[scope]
                })
            },
        })
    }

    /**
     * 检查所有授权状态
     */
    const checkAllAuth = () => {
        Taro.getSetting({
            success: res => {
                // 遍历所有可能的授权类型并更新状态
                Object.values(AuthScope).forEach(scope => {
                    authState.value[scope] = !!res.authSetting[scope]
                })
            },
        })
    }

    /**
     * 显示授权设置对话框
     * @param content 对话框内容
     */
    const showAuthDialog = (content: string) => {
        return new Promise<void>(resolve => {
            Taro.showModal({
                title: '提示',
                content,
                success: modalRes => {
                    if (modalRes.confirm) {
                        Taro.openSetting({
                            success: settingRes => {
                                // 更新所有授权状态
                                Object.values(AuthScope).forEach(scope => {
                                    authState.value[scope] = !!settingRes.authSetting[scope]
                                })
                                resolve()
                            },
                        })
                    } else {
                        resolve()
                    }
                },
            })
        })
    }

    /**
     * 请求单个授权
     * @param scope 授权范围
     * @param message 拒绝时的提示信息
     * @returns Promise
     */
    const requestAuth = (
        scope: AuthScope,
        message: string = '需要您授权才能继续，是否去设置打开？',
    ): Promise<boolean> => {
        return new Promise((resolve, reject) => {
            Taro.authorize({
                scope,
                success: () => {
                    authState.value[scope] = true
                    resolve(true)
                },
                fail: async () => {
                    await showAuthDialog(message)
                    // 重新检查授权状态
                    Taro.getSetting({
                        success: res => {
                            const granted = !!res.authSetting[scope]
                            authState.value[scope] = granted
                            if (granted) {
                                resolve(true)
                            } else {
                                reject(new Error(`${scope} 授权被拒绝`))
                            }
                        },
                        fail: () => {
                            reject(new Error('获取授权设置失败'))
                        },
                    })
                },
            })
        })
    }

    /**
     * 请求多个授权
     * @param scopes 授权范围数组
     * @param messages 拒绝时的提示信息对象
     * @returns Promise 包含授权结果的对象
     */
    const requestMultiAuth = async (
        scopes: AuthScope[],
        messages: { [key in AuthScope]?: string } = {},
    ): Promise<{ [key in AuthScope]?: boolean }> => {
        const result: { [key in AuthScope]?: boolean } = {}
        // 先检查现有授权状态
        await new Promise<void>(resolve => {
            Taro.getSetting({
                success: res => {
                    scopes.forEach(scope => {
                        result[scope] = !!res.authSetting[scope]
                        authState.value[scope] = !!res.authSetting[scope]
                    })
                    resolve()
                },
                fail: () => {
                    scopes.forEach(scope => {
                        result[scope] = false
                    })
                    resolve()
                },
            })
        })
        // 请求未授权的权限
        for (const scope of scopes) {
            if (!result[scope]) {
                try {
                    result[scope] = await requestAuth(scope, messages[scope])
                } catch (error) {
                    console.error(`${scope} 授权请求失败`, error)
                    result[scope] = false
                }
            }
        }

        return result
    }

    /**
     * 请求媒体相关授权（相机和相册）
     * @returns Promise 包含授权结果的对象
     */
    const requestMediaAuth = () => {
        return requestMultiAuth([AuthScope.Camera, AuthScope.PhotoAlbum], {
            [AuthScope.Camera]: '需要您授权使用相机权限，是否去设置打开？',
            [AuthScope.PhotoAlbum]: '需要您授权保存图片到相册的权限，是否去设置打开？',
        })
    }

    /**
     * 请求相机权限
     * @returns Promise<boolean> 权限是否授予
     */
    const requestCameraAuth = () => {
        return requestAuth(AuthScope.Camera, '需要您授权使用相机权限，是否去设置打开？')
    }

    /**
     * 请求相册权限
     * @returns Promise<boolean> 权限是否授予
     */
    const requestPhotoAlbumAuth = () => {
        return requestAuth(AuthScope.PhotoAlbum, '需要您授权保存图片到相册的权限，是否去设置打开？')
    }

    // 初始检查授权状态
    checkAllAuth()

    return {
        authState,
        checkAuth,
        checkAllAuth,
        requestAuth,
        requestMultiAuth,
        requestMediaAuth,
        requestCameraAuth,
        requestPhotoAlbumAuth,
        showAuthDialog,
    }
}
