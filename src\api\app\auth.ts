import { ContentTypeEnum } from '@/enums/httpEnum'
// import { getEnvValue } from '/@/utils/env'
import { defHttp } from '@/utils/http/axios'

const LOGIN = '/connect/token'
// const LOGIN_OUT = '/logout'
// const REFRESH_TOKEN = '/refresh/token'
const WECHAT_APPLET = '/connect/token'
// const TRENANT_SETTING = '/Abp/MultiTenancy/TenantSwitchModal'

// const client_id = getEnvValue('VITE_AUTH_CLIENT_ID')
// const client_secret = getEnvValue('VITE_AUTH_CLIENT_SECRET')
// const grant_type = getEnvValue('VITE_AUTH_GRANT_TYPE')
// const scope = getEnvValue('VITE_AUTH_SCOPE')

/**
 * 微信小程序快捷登录
 */
export const externalAuthenticate = (raw_info: string) => {
    const data = {
        client_id: 'Microservice_Wechat',
        client_secret: 'U#%Y67hjo0^2B',
        grant_type: 'wechat_applet',
        scope: 'openid profile email address offline_access IdentityService AdministrationService SaasService PrimaryService ProductService IotService PlatformService EnterpriseService WeChatService AftersaleService DataViewService',
        raw_info: raw_info,
    }
    // const data = {
    //   client_id: client_id,
    //   client_secret: client_secret,
    //   grant_type: grant_type,
    //   scope:scope,
    //   raw_info: raw_info,
    // };
    return defHttp.request<LoginModel>({
        method: 'POST',
        url: WECHAT_APPLET,
        data: data,
        headers: {
            'Content-Type': ContentTypeEnum.FORM_URLENCODED,
        },
    })
}

/**
 * 用户名密码登录
 * @param params
 */
export function login(params: LoginParams) {
    const tokenParams = {
        client_id: 'Microservice_Vue',
        client_secret: 'U#%Y67hjo0^2B',
        grant_type: 'password',
        username: params.name,
        password: params.password,
        TwoFactorProvider: undefined,
        TwoFactorCode: undefined,
    }
    return defHttp.request<LoginModel>({
        method: 'POST',
        url: LOGIN,
        data: tokenParams,
        headers: {
            'Content-Type': ContentTypeEnum.FORM_URLENCODED,
        },
    })
}

/**
 * 租户设置
 * @param params
 * @returns
 */
// export function tenantSwitchModal(name: string) {
//   const Params = {
//     'Input.Name':name
//   };
//   return request.post<any>(TRENANT_SETTING, Params, {
//     custom: {
//       auth: false,
//     },
//     header: {
//       "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
//     },
//   });
// }

/**
 * 登出
 */
// export function logout() {
//   return request.post(LOGIN_OUT, {});
// }

/**
 * 刷新token
 */
// export function refreshToken() {
//   return request.post<LoginModel>(REFRESH_TOKEN, {});
// }
