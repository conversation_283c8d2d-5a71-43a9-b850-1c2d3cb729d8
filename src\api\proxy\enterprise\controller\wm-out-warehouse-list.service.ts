import type { GetForEditInput } from '../../helper/shared/models'
import type {
    GetWmOutWarehouseListForEditorOutput,
    GetWmOutWarehouseListInput,
    WmOutWarehouseListCloneDto,
    WmOutWarehouseListCreateDto,
    WmOutWarehouseListDetailDto,
    WmOutWarehouseListEditDto,
    WmOutWarehouseListInProgressDto,
    WmOutWarehouseListIsCancelDto,
    WmOutWarehouseListIsOutDto,
    WmOutWarehouseListListDto,
    WmOutWarehouseListProcessPartialDto,
    WmOutWarehouseListSubmitDto,
    WmOutWarehouseListUpdateStatusDto,
} from '../wms/dtos/models'
import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class WmOutWarehouseListService {
    apiName = 'EnterpriseService'

    clone = (dto: WmOutWarehouseListCloneDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-out-warehouse-list/CloneOutWarehouseList',
                data: dto,
            },
            options,
        )

    create = (input: WmOutWarehouseListCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-out-warehouse-list',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-out-warehouse-list/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmOutWarehouseListDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-out-warehouse-list/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmOutWarehouseListDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-out-warehouse-list/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmOutWarehouseListForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-out-warehouse-list/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getPaged = (input: GetWmOutWarehouseListInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmOutWarehouseListListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-out-warehouse-list',
                params: {
                    status: input.status,
                    outWarehouseBillId: input.outWarehouseBillId,
                    materialId: input.materialId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    processPartialOutWarehouseListByDto = (dto: WmOutWarehouseListProcessPartialDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-out-warehouse-list/ProcessPartial',
                data: dto,
            },
            options,
        )

    update = (input: WmOutWarehouseListEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-out-warehouse-list',
                data: input,
            },
            options,
        )

    updateStatus = (updateStatusDao: WmOutWarehouseListUpdateStatusDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-out-warehouse-list/updateStatus',
                data: updateStatusDao,
            },
            options,
        )

    /**
     * 提交出库
     * @param input 提交出库DTO
     * @param options 请求选项
     * @returns
     */
    submit = (input: WmOutWarehouseListSubmitDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-out-warehouse-list/submit',
                data: input,
            },
            options,
        )

    /**
     * 检验
     * @param input 检验DTO
     * @param options 请求选项
     * @returns
     */
    inProgress = (input: WmOutWarehouseListInProgressDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-out-warehouse-list/in-progress',
                data: input,
            },
            options,
        )

    /**
     * 出库
     * @param input 出库DTO
     * @param options 请求选项
     * @returns
     */
    isOut = (input: WmOutWarehouseListIsOutDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-out-warehouse-list/is-out',
                data: input,
            },
            options,
        )

    /**
     * 退回
     * @param input 退回DTO
     * @param options 请求选项
     * @returns
     */
    isCancel = (input: WmOutWarehouseListIsCancelDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-out-warehouse-list/is-cancel',
                data: input,
            },
            options,
        )
}
