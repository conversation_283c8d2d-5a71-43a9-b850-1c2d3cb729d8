import type { GetForEditInput, NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetProductAttributeForEditorOutput,
    GetProductAttributeInput,
    ProductAttributeCreateDto,
    ProductAttributeDetailDto,
    ProductAttributeEditDto,
    ProductAttributeListDto,
} from '../pms/dtos/models'
import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class ProductAttributeService {
    apiName = 'EnterpriseService'

    create = (input: ProductAttributeCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/product-attribute',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/product-attribute/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<ProductAttributeDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/product-attribute/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<ProductAttributeDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/product-attribute/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetProductAttributeForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/product-attribute/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/product-attribute/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetProductAttributeInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<ProductAttributeListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/product-attribute',
                params: {
                    productParentId: input.parentId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/product-attribute/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    update = (input: ProductAttributeEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/product-attribute',
                data: input,
            },
            options,
        )
}
