import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetWmCategoryEditorInput,
    GetWmCategoryForEditorOutput,
    GetWmCategoryInput,
    WmCategoryCreateDto,
    WmCategoryDetailDto,
    WmCategoryEditDto,
    WmCategoryTreeListDto,
    WmCategoryTreeNodesDto,
} from '../wms/dtos/models'

export class WmCategoryService {
    apiName = 'EnterpriseService'

    create = (input: WmCategoryCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-category',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-category/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmCategoryDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-category/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmCategoryDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-category/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetWmCategoryEditorInput, options?: RequestOptions) =>
        defHttp.request<GetWmCategoryForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-category/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-category/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetWmCategoryInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmCategoryTreeListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-category',
                params: {
                    parentId: input.parentId,
                    isActivate: input.isActivate,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    getTreeNodes = (filterText: string, options?: RequestOptions) =>
        defHttp.request<WmCategoryTreeNodesDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-category/options-trees',
                params: {
                    filterText,
                },
            },
            options,
        )

    update = (input: WmCategoryEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-category',
                data: input,
            },
            options,
        )
}
