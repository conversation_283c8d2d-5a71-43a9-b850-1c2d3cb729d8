<template>
    <view class="action-menu" :class="[customClass, disabled ? 'action-menu--disabled' : '']" :style="customStyle">
        <nut-button
            :type="buttonType"
            :size="buttonSize"
            :plain="buttonPlain"
            :disabled="disabled"
            @click.stop="toggleMenu"
        >
            {{ buttonText }}
        </nut-button>
        <view
            v-if="isVisible"
            class="action-menu__dropdown"
            :class="{
                'action-menu__dropdown--left': direction === 'left',
                'action-menu__dropdown--right': direction === 'right',
                'action-menu__dropdown--top': direction === 'top',
                'action-menu__dropdown--bottom': direction === 'bottom',
            }"
        >
            <view
                v-for="option in options"
                :key="option.value"
                class="action-menu__item"
                :class="{ 'action-menu__item--disabled': option.disabled }"
                @click.stop="handleSelect(option)"
            >
                <component
                    v-if="option.icon && iconComponents[option.icon]"
                    :is="iconComponents[option.icon]"
                    class="action-menu__icon"
                />
                <text class="action-menu__text">{{ option.text }}</text>
            </view>
        </view>
        <nut-overlay
            v-model:visible="isVisible"
            @click="closeOnOverlayClick"
            :overlay-style="{ backgroundColor: 'rgba(0, 0, 0, 0)' }"
        />
    </view>
</template>

<script lang="ts" setup>
import { ref, computed, onUnmounted } from 'vue'
import { ActionMenuItem } from './models/ActionMenuDto'
import * as IconComponents from '@nutui/icons-vue-taro'
import type { ButtonType, ButtonSize } from '@nutui/nutui-taro'
import Taro from '@tarojs/taro'
import { useDidShow } from '@/hooks/component/hooks'
import { t } from '@/locale/fanyi'

defineOptions({
    name: 'ActionMenu',
})

interface Props {
    options: ActionMenuItem[]
    buttonText?: string
    buttonType?: ButtonType
    buttonSize?: ButtonSize
    buttonPlain?: boolean
    disabled?: boolean
    customStyle?: string
    customClass?: string
    closeOnClickOutside?: boolean
    overlayClosable?: boolean
    direction?: 'left' | 'right' | 'top' | 'bottom'
}

const props = withDefaults(defineProps<Props>(), {
    buttonText: t('menu.more'),
    buttonType: 'info',
    buttonSize: 'small',
    buttonPlain: true,
    disabled: false,
    customStyle: '',
    customClass: '',
    closeOnClickOutside: true,
    overlayClosable: true,
    direction: 'bottom',
})

const emit = defineEmits<{
    select: [value: string | number, option: ActionMenuItem]
}>()

const isVisible = ref(false)

// 创建图标组件映射
const iconComponents = computed(() => {
    const components: Record<string, any> = {}
    // 将所有图标组件添加到映射中
    for (const key in IconComponents) {
        // 转换组件名称格式，例如 ArrowDown 转为 arrow-down
        const iconName = key
            .replace(/([A-Z])/g, '-$1')
            .toLowerCase()
            .substring(1)
        components[iconName] = IconComponents[key]
    }
    return components
})

// 切换菜单显示状态
const toggleMenu = () => {
    if (props.disabled) return
    isVisible.value = !isVisible.value
}

// 处理选项选择
const handleSelect = (option: ActionMenuItem) => {
    if (option.disabled) return
    emit('select', option.value, option)
    isVisible.value = false
}

// 点击遮罩层关闭菜单
const closeOnOverlayClick = () => {
    if (props.overlayClosable) {
        isVisible.value = false
    }
}

// 点击外部关闭菜单
const handleClickOutside = (event: any) => {
    if (props.closeOnClickOutside && isVisible.value) {
        // 在小程序环境中，我们无法直接使用document和closest方法
        // 这里简化处理，只要点击了非菜单区域，就关闭菜单
        const target = event.target
        if (!target || !target.id || target.id !== 'action-menu') {
            isVisible.value = false
        }
    }
}

// 使用Taro的生命周期钩子
useDidShow(() => {
    if (props.closeOnClickOutside) {
        // 在Taro/小程序环境中，我们需要使用Taro提供的事件API
        if (process.env.TARO_ENV === 'h5') {
            // H5环境
            document.addEventListener('click', handleClickOutside)
        } else {
            // 小程序环境
            Taro.eventCenter.on('touchstart', handleClickOutside)
        }
    }
})

onUnmounted(() => {
    if (props.closeOnClickOutside) {
        if (process.env.TARO_ENV === 'h5') {
            // H5环境
            document.removeEventListener('click', handleClickOutside)
        } else {
            // 小程序环境
            Taro.eventCenter.off('touchstart', handleClickOutside)
        }
    }
})
</script>

<style lang="scss">
.action-menu {
    position: relative;
    display: inline-block;

    &__dropdown {
        position: absolute;
        width: 160rpx;
        background-color: #fff;
        border-radius: 8rpx;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        bottom: auto;
        left: auto;
        right: auto;
        top: 100%;
        margin-top: 5px;

        &--left {
            top: 0;
            right: 100%;
            margin-right: 5px;
            margin-top: 0;
        }

        &--right {
            top: 0;
            left: 100%;
            margin-left: 5px;
            margin-top: 0;
        }

        &--top {
            bottom: 100%;
            left: 0;
            top: auto;
            margin-bottom: 5px;
            margin-top: 0;
        }

        &--bottom {
            top: 100%;
            left: 0;
            margin-top: 5px;
        }
    }

    &__item {
        display: flex;
        align-items: center;
        padding: 20rpx 24rpx;
        font-size: 28rpx;
        color: #333;
        text-align: center;
        border-bottom: 1px solid #eee;

        &:last-child {
            border-bottom: none;
        }

        &:active {
            background-color: #f7f8fc;
        }

        &--disabled {
            color: #999;
            background-color: #f7f7f7;
            pointer-events: none;
        }
    }

    &__icon {
        margin-right: 8rpx;
    }

    &__text {
        flex: 1;
        text-align: center;
    }

    &--disabled {
        opacity: 0.6;
        pointer-events: none;
    }
}
</style>
