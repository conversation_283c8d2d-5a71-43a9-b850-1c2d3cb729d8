import { mapEnumToOptions } from '@/utils/form-utils'

/**
 * 付款方式枚举（按支付时间+支付工具分类）
 */
export enum PaymentMethod {
    /**
     * 未知
     */
    Unknow = 0,

    // ===================== 按支付时间分类 =====================

    /**
     * 预付（预付款）: 订单确认后发货前支付
     * 适用场景：新供应商、定制化产品、国际贸易
     */
    AdvancePayment = 1,

    /**
     * 货到付款（COD）: 货物验收合格后支付
     * 适用场景：本地采购、小额交易、高信任供应商
     */
    CashOnDelivery = 2,

    /**
     * 分期付款: 按合同分阶段支付（如30%+40%+30%）
     * 适用场景：大额设备采购、工程项目
     */
    InstallmentPayment = 3,

    /**
     * 账期付款（赊销）: 收货后延期支付（如月结30天）
     * 适用场景：长期合作供应商、现金流管理
     */
    CreditPayment = 4,

    // ===================== 按支付工具分类 =====================

    /**
     * 银行电汇（T/T）: 即时到账
     * 适用场景：国内/国际交易
     */
    BankTransfer = 5,

    /**
     * 信用证（L/C）: 银行担保支付
     * 适用场景：高风险国际贸易
     */
    LetterOfCredit = 6,

    /**
     * 承兑汇票: 延期支付票据（如6个月期）
     * 适用场景：缓解资金压力
     */
    AcceptanceBill = 7,

    /**
     * 电子支付: 支付宝/微信/企业网银
     * 适用场景：小额采购、电商平台
     */
    ElectronicPayment = 8,

    /**
     * 现金支付: 直接现金交易
     * 适用场景：小额零星采购（需注意合规性）
     */
    Cash = 9,

    /**
     * 支票支付: 企业支票/银行本票
     * 注意：需防范空头支票风险
     */
    Check = 10,
}

/**
 * 付款方式分组
 */
export enum PaymentMethodGroup {
    /**
     * 按支付时间分类
     */
    Time = 0,

    /**
     * 按支付工具分类
     */
    Tool = 1,
}

/**
 * 获取付款方式所属分组
 * @param method 付款方式
 * @returns 分组类型
 */
export function getPaymentMethodGroup(method: PaymentMethod): PaymentMethodGroup {
    if (method >= PaymentMethod.AdvancePayment && method <= PaymentMethod.CreditPayment) {
        return PaymentMethodGroup.Time
    }
    return PaymentMethodGroup.Tool
}

/**
 * 按支付时间分类的选项
 */
export const paymentMethodTimeOptions = [
    PaymentMethod.AdvancePayment,
    PaymentMethod.CashOnDelivery,
    PaymentMethod.InstallmentPayment,
    PaymentMethod.CreditPayment,
].map(method => ({
    label: PaymentMethod[method],
    value: method,
}))

/**
 * 按支付工具分类的选项
 */
export const paymentMethodToolOptions = [
    PaymentMethod.BankTransfer,
    PaymentMethod.LetterOfCredit,
    PaymentMethod.AcceptanceBill,
    PaymentMethod.ElectronicPayment,
    PaymentMethod.Cash,
    PaymentMethod.Check,
].map(method => ({
    label: PaymentMethod[method],
    value: method,
}))

/**
 * 付款方式选项（用于下拉选择）
 */
export const paymentMethodOptions = mapEnumToOptions(PaymentMethod, { exclude: [PaymentMethod.Unknow] })

/**
 * 付款方式分组选项（用于分组下拉选择）
 */
export const paymentMethodGroupOptions = [
    {
        label: '按支付时间分类',
        options: paymentMethodTimeOptions,
    },
    {
        label: '按支付工具分类',
        options: paymentMethodToolOptions,
    },
]
