<template>
    <view class="content">
        <!-- <Left
            color="#4A90E2"
            size="20"
            style="position: fixed; top: 15px; left: 15px; z-index: 999"
            @click="handleBack"
        /> -->
        <nut-popup :overlay="false" position="bottom" round :style="{ height: '80%' }" v-model:visible="showRound">
            <view class="login">
                <nut-row type="flex" justify="space-around">
                    <nut-col :span="12">
                        <view class="login-title">登录</view>
                    </nut-col>
                    <nut-col :span="12">
                        <view class="login-title tenant-title" @click="dialogVisible = true">
                            {{ tenantName }}
                        </view>
                    </nut-col>
                </nut-row>
                <nut-config-provider :theme-vars="themeVars">
                    <view class="g-flex-centen login-input-item">
                        <My color="#3c9fb9" size="16"></My>
                        <nut-input
                            v-model="account"
                            placeholder="请输入账号"
                            class="login-input"
                            :border="false"
                            clearable
                        />
                    </view>
                    <view class="g-flex-centen login-input-item">
                        <Success color="#3c9fb9" size="16"></Success>
                        <nut-input
                            v-model="password"
                            placeholder="请输入密码"
                            class="login-input"
                            :border="false"
                            type="password"
                            clearable
                        />
                    </view>
                    <nut-button class="login-btn" plain block :disabled="logining" @click="loginClick">登录</nut-button>
                    <view class="login-tips">--其它登录方式--</view>
                </nut-config-provider>
                <view class="login-manner" @click="wechatLogin">
                    <span style="font-size: 12px; color: #3c9fb9">手机号快捷登录</span>
                </view>
            </view>
        </nut-popup>
        <!-- 设置租户弹出框 -->
        <nut-dialog title="设置租户" content="请设置租户后再登录" @ok="setTenantName" v-model:visible="dialogVisible">
            <template #default>
                <nut-input v-model="tenantValue" placeholder="请输入租户" />
            </template>
        </nut-dialog>
    </view>
</template>

<script lang="ts" setup>
import { useAuthStore } from '@/stores/modules/auth'
import { Toast } from '@/utils/Taro-api'
import Taro, { useDidShow } from '@tarojs/taro'
import { reactive, ref } from 'vue'
import { getAbpAppConfig } from '@/api/app/appConfig'
import { initializeApp } from '@/hooks/web/initApp'

const showRound = ref<boolean>(true)
const logining = ref<boolean>(false)
const dialogVisible = ref<boolean>(false)
const tenantValue = ref<string>('')
const tenantName = ref<string>('')
const account = ref<string>('')
const password = ref<string>('')

/**
 * 自定义按钮变量
 */
const themeVars = reactive({
    buttonBorderRadius: '9px',
    buttonDefaultBorderColor: '#3c9fb9',
    buttonDefaultColor: '#3c9fb9',
})

/**
 * @description 登录事件
 */
const loginClick = async () => {
    // 检查租户是否已设置
    const authStore = useAuthStore()
    if (!authStore.getTenant) {
        Toast('请先设置租户', { icon: 'error' })
        dialogVisible.value = true
        return
    }
    
    Taro.showLoading({
        title: '登录中...',
    })
    logining.value = true
    await login()
}

/**
 * @description 设置租户
 */
const getTenantByName = (name: string) => {
    // 获取租户
    const authStore = useAuthStore()
    return authStore
        .getTenantByName(name)
        .then(res => {
            Taro.showToast({
                title: res,
            })
        })
        .catch(err => {
            Toast(err ?? '获取租户失败', { icon: 'error' })
            failInit()
        })
}

/**
 * @description 登录
 */
const login = async () => {
    const authStore = useAuthStore()

    try {
        await authStore.login({ name: account.value, password: password.value })

        await getAbpAppConfig()
        initializeApp()

        Taro.switchTab({
            url: '/pages/home/<USER>',
        })
        Toast('登录成功', { icon: 'success' })
    } catch (error) {
        Toast(error ?? '登录失败', { icon: 'error' })
        failInit()
    }
}

/**
 * @description 登录失败，恢复页面状态
 */
const failInit = () => {
    logining.value = false
    Taro.hideLoading()
}

/**
 * @description 微信登录
 */
const wechatLogin = () => {
    Taro.reLaunch({
        url: '/pages/subpackages/wechat-login/index',
    })
}

/**
 * @description 设置租户
 */
const setTenantName = () => {
    getTenantByName(tenantValue.value).then(() => {
        dialogVisible.value = false
        tenantName.value = getTenantName()
    })
}

/**
 * @description 获取租户
 */
const getTenantName = () => {
    const authStore = useAuthStore()
    return authStore.getTenant ? authStore.getTenant.name : '设置租户'
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    tenantName.value = getTenantName()
    // 检查租户是否已设置，如果未设置则自动打开设置对话框
    const authStore = useAuthStore()
    if (!authStore.getTenant) {
        // 延迟显示对话框，确保页面已完全加载
        setTimeout(() => {
            Toast('请先设置租户', { icon: 'error' })
            dialogVisible.value = true
        }, 500)
    }
})

/**
 * @description 返回上一页
 */
// const handleBack = () => {
//     Taro.navigateBack({
//         delta: 1,
//     })
// }
</script>

<style lang="scss">
// 基础变量定义
$primary-blue: #4a90e2;
$secondary-color: #67a7e5;
$text-color: #2c3e50;
$error-color: #ff6b6b;
$input-background: #ffffff;
$border-radius: 12px;

.content {
    height: 100vh;
    width: 100%;
    background: $primary-blue;
    position: relative;
    overflow: hidden;
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);

    &::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url(../../../assets/images/login-bj.png) no-repeat center center;
        background-size: cover;
        opacity: 0.1;
    }
}

.login {
    margin: 40px;
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: $border-radius;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);

    .login-title {
        color: $text-color;
        font-size: 42px;
        font-weight: 600;
        margin-bottom: 20px;
        letter-spacing: 0.5px;
    }

    .tenant-title {
        color: $error-color;
        text-align: right;
        font-size: 30px;
        text-decoration: none;
        position: relative;
        float: right;
        padding: 4px 8px;
        cursor: pointer;
        transition: opacity 0.2s ease;

        &:active {
            opacity: 0.7;
        }
    }

    .login-input-item {
        display: flex;
        border: 2px solid rgba(74, 144, 226, 0.1);
        border-radius: $border-radius;
        justify-content: flex-start;
        align-items: center;
        padding: 4px 16px;
        margin-top: 24px;
        background-color: $input-background;
        transition: all 0.3s ease;

        &:focus-within {
            border-color: $primary-blue;
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        .login-input {
            padding: 24px 12px;
            font-size: 28px;
            color: $text-color;
            flex: 1;

            &::placeholder {
                color: #a0aec0;
            }
        }
    }

    .login-btn {
        margin-top: 40px;
        height: 88px;
        font-size: 32px;
        font-weight: 600;
        border-radius: $border-radius;
        background: linear-gradient(135deg, $primary-blue 0%, $secondary-color 100%);
        border: none;
        color: white;
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:not(:disabled):active {
            transform: translateY(2px);
        }

        &:not(:disabled) {
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        &:disabled {
            opacity: 0.7;
            background: #e2e8f0;
        }
    }

    .login-tips {
        margin-top: 60px;
        text-align: center;
        color: #718096;
        font-size: 24px;
        position: relative;

        &::before,
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 60px;
            height: 1px;
            background: #cbd5e0;
        }

        &::before {
            left: 25%;
        }

        &::after {
            right: 25%;
        }
    }

    .login-manner {
        text-align: center;
        margin-top: 40px;

        span {
            font-size: 28px;
            color: $primary-blue;
            padding: 12px 24px;
            border-radius: 30px;
            background: rgba(74, 144, 226, 0.1);
            transition: all 0.3s ease;

            &:active {
                background: rgba(74, 144, 226, 0.2);
            }
        }
    }
}
</style>
