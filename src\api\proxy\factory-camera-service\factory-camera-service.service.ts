import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/model/axios'
import { PagedResultDto } from '@/shared/models/dtos'
import { FactoryCameraListDto, GetFactoryCameraInput } from './models'

export class FactoryCameraService {
    getPaged = (input: GetFactoryCameraInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<FactoryCameraListDto>>(
            {
                method: 'GET',
                url: '/api/platform/factory-camera',
                params: {
                    factoryParentId: input.factoryParentId,
                    deviceId: input.deviceId,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )
}
