import { defineStore } from 'pinia'
import { store } from '..'
import { USER_INFO_KEY } from '@/enums/cacheEnum'
import { getCache, removeCache, setCache } from '@/utils/cache'
import { CurrentUserDto } from '@/api/application-configurations/models'

interface UserState {
    userInfo: CurrentUserDto | undefined
}

export const useUserStore = defineStore({
    id: 'userInfo',
    state: (): UserState => ({
        userInfo: undefined, // 用户信息
    }),
    getters: {
        getUserInfo: state => state.userInfo,
    },
    actions: {
        initialize() {
            this.userInfo = getCache<string>(USER_INFO_KEY)
        },
        setUserInfo(userInfo: CurrentUserDto | undefined) {
            if (!userInfo) {
                removeCache(USER_INFO_KEY)
                return
            }
            setCache(USER_INFO_KEY, userInfo)
            this.userInfo = userInfo
        },
    },
})

// Need to be used outside the setup
export function useUserStoreWithOut() {
    return useUserStore(store)
}
