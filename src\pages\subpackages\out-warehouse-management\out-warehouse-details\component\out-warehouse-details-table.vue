<template>
    <view class="out-warehouse-details-table">
        <view v-if="loading" class="loading-container">
            <nut-skeleton rows="6" title animated />
        </view>
        <view v-else-if="!detailsList.id" class="empty-container">
            <nut-empty description="暂无出库单详情" image="empty" />
        </view>
        <view v-else class="details-container">
            <!-- 出库单编号 -->
            <nut-cell :title="t('text.encode')" :desc="detailsList.encode"></nut-cell>
            <!-- 出库单名称 -->
            <nut-cell :title="t('text.name')" :desc="detailsList.name"></nut-cell>
            <!-- 审核状态 -->
            <nut-cell :title="'审核状态'">
                <template #desc>
                    <nut-tag :color="getStatusColor(detailsList.status)">
                        {{ getStatusText(detailsList.status) }}
                    </nut-tag>
                </template>
            </nut-cell>
            <!-- 出库类型 -->
            <nut-cell :title="'出库类型'">
                <template #desc>
                    <nut-tag :color="getTypeColor(detailsList.outboundType)">
                        {{ getTypeText(detailsList.outboundType) }}
                    </nut-tag>
                </template>
            </nut-cell>
            <!-- 出库时间 -->
            <nut-cell :title="'出库时间'" :desc="formatTime(detailsList.outboundTime)"></nut-cell>
            <!-- 申请人 -->
            <nut-cell :title="'申请人'" :desc="detailsList.applicantUserName || '-'"></nut-cell>
            <!-- 审核人 -->
            <nut-cell :title="'审核人'" :desc="detailsList.reviewerUserName || '-'"></nut-cell>
            <!-- 执行人 -->
            <nut-cell :title="'执行人'" :desc="detailsList.executorUserName || '-'"></nut-cell>
            <!-- 描述 -->
            <nut-cell :title="t('text.describe')" :desc="detailsList.describe || '-'"></nut-cell>
            <!-- 创建时间 -->
            <nut-cell :title="t('text.creationTime')" :desc="formatTime(detailsList.creationTime)"></nut-cell>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { t } from '@/locale/fanyi'
import { WmOutWarehouseBillService } from '@/api/proxy/enterprise/controller/wm-out-warehouse-bill.service'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { Loading, HideLoading, Toast } from '@/utils/Taro-api'
import dayjs from 'dayjs'

// 初始化服务
const wmOutWarehouseBillService = new WmOutWarehouseBillService()
const identityUserService = new IdentityUserService()

// 详情数据
const detailsList = ref<Partial<WmOutWarehouseBillDetailDto>>({})
const loading = ref(false)

interface WmOutWarehouseBillDetailDto {
    id: string
    name: string
    encode: string
    describe?: string
    outboundType: string
    outboundTime?: string
    status: string
    imageUrl?: string
    attachement?: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    reviewerUserName?: string
    executorUserId: string
    executorUserName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties?: Record<string, object>
    concurrencyStamp?: string
}

interface DetailsTableProps {
    outWarehouseBillId: string
}

// 接收参数
const props = defineProps<DetailsTableProps>()

// 出库类型映射
const outboundTypeMap = {
    Ordinary: { text: '普通', color: '#909399' },
    Sales: { text: '销售', color: '#67C23A' },
    Production: { text: '生产', color: '#E6A23C' },
    Transfer: { text: '调拨', color: '#3460fa' },
    PurchaseReturn: { text: '采购退货', color: '#F56C6C' },
}

// 审核状态映射
const auditStatusMap = {
    Newly: { type: 'default', text: '新建', color: '#909399' },
    Received: { type: 'success', text: '已完成', color: '#67C23A' },
    Pending: { type: 'warning', text: '待审批', color: '#E6A23C' },
    Approved: { type: 'success', text: '已审批', color: '#67C23A' },
    Rejected: { type: 'danger', text: '已拒绝', color: '#F56C6C' },
    Cancelled: { type: 'default', text: '已取消', color: '#909399' },
    Completed: { type: 'success', text: '已出库', color: '#67C23A' },
}

// 获取状态颜色
const getStatusColor = (status?: string) => {
    if (!status) return '#909399'
    return auditStatusMap[status]?.color || '#909399'
}

// 获取状态文本
const getStatusText = (status?: string) => {
    if (!status) return '-'
    return auditStatusMap[status]?.text || status
}

// 获取类型文本
const getTypeText = (type?: string) => {
    if (!type) return '-'
    return outboundTypeMap[type]?.text || type
}

// 获取类型颜色
const getTypeColor = (type?: string) => {
    if (!type) return '#909399'
    return outboundTypeMap[type]?.color || '#909399'
}

// 格式化时间
const formatTime = (time?: string) => {
    if (!time) return '-'
    return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 获取用户信息
const fetchUserInfo = async (data: any) => {
    try {
        if (data.executorUserId) {
            const executorInfo = await identityUserService.get(data.executorUserId)
            data.executorUserName = `${executorInfo.surname || ''}${executorInfo.name || ''}`
        }
        if (data.applicantUserId) {
            const applicantInfo = await identityUserService.get(data.applicantUserId)
            data.applicantUserName = `${applicantInfo.surname || ''}${applicantInfo.name || ''}`
        }
        if (data.reviewerUserId) {
            const reviewerInfo = await identityUserService.get(data.reviewerUserId)
            data.reviewerUserName = `${reviewerInfo.surname || ''}${reviewerInfo.name || ''}`
        }
        return data
    } catch (error) {
        console.error('获取用户信息失败:', error)
        return data
    }
}

// 获取出库单详情数据
const fetchOutWarehouseDetails = async () => {
    if (!props.outWarehouseBillId) return

    try {
        loading.value = true
        Loading()

        const result = await wmOutWarehouseBillService.get(props.outWarehouseBillId)

        if (result) {
            // 获取用户信息
            const enrichedData = await fetchUserInfo(result)
            detailsList.value = enrichedData
        } else {
            detailsList.value = {}
            Toast(t('text.noData'), { icon: 'none' })
        }
    } catch (error) {
        console.error('获取出库单详情失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        loading.value = false
        HideLoading()
    }
}

// 监听ID变化刷新数据
onMounted(() => {
    fetchOutWarehouseDetails()
})

// 添加对ID变化的监听
watch(
    () => props.outWarehouseBillId,
    newVal => {
        if (newVal) {
            fetchOutWarehouseDetails()
        }
    },
    { immediate: true },
)
</script>

<style scoped>
.out-warehouse-details-table {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 2vw;
}

.loading-container {
    width: 100%;
    padding: 20px 0;
}

.empty-container {
    width: 100%;
    padding: 50px 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.details-container {
    width: 100%;
    margin-bottom: 2vh;
    border-radius: 8px;
    overflow: hidden;
    background-color: #ffffff;
}

/* 自定义单元格边框和间距 */
:deep(.nut-cell) {
    border-bottom: 1px solid #f0f0f0;
    min-height: 50px;
}

:deep(.nut-cell:last-child) {
    border-bottom: none;
}

/* 自定义标签样式 */
:deep(.nut-tag) {
    font-size: 12px;
    padding: 2px 8px;
}
</style>
