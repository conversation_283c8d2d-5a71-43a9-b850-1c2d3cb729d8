import { appConfig } from '@/appConfig'
import { t } from '@/locale/fanyi'
import Taro from '@tarojs/taro'

export function setUpTabBarI18n() {
    // 获取当前页面栈
    const pages = Taro.getCurrentPages()
    // 如果页面栈为空，则不执行后续操作
    if (!pages || pages.length === 0) {
        console.warn('setUpTabBarI18n: Unable to get current pages.')
        return
    }
    // 获取当前页面实例和路径
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage.route
    if (!currentRoute) {
        console.warn('setUpTabBarI18n: Unable to get current route.')
        return
    }

    // 获取 TabBar 配置和路径列表
    const tabBar = appConfig.tabBar
    const tabBarPaths = tabBar.list.map(item => item.pagePath)

    // 判断当前页面是否为 TabBar 页面
    const isTabBarPage = tabBarPaths.includes(currentRoute)

    // 仅当是 TabBar 页面时，才设置 TabBar item
    if (isTabBarPage) {
        tabBar.list.forEach((item, index) => {
            Taro.setTabBarItem({
                index,
                text: t(item.text),
            })
        })
    } else {
        console.log(`setUpTabBarI18n: Current page (${currentRoute}) is not a TabBar page. Skipping setTabBarItem.`)
    }
}
