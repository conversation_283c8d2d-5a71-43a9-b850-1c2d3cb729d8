# 权限控制模块

本模块提供权限验证相关功能，包括权限检查、角色检查和路由权限守卫。

## 基本权限检查

```typescript
import { hasPermission, isGranted } from './permission'

// 检查单个权限
const hasAccess = hasPermission('EnterpriseService.Wms.ProductItem')

// 检查多个权限（任一满足即可）
const hasMultipleAccess = hasPermission(['EnterpriseService.Wms.ProductItem', 'EnterpriseService.Wms.OtherPermission'])

// 检查多个权限（必须同时满足所有权限）
import { hasAllPermissions } from './permissionGuard'
const hasAllAccess = hasAllPermissions(['EnterpriseService.Wms.ProductItem', 'EnterpriseService.Wms.Edit'])
```

## 路由权限守卫

在菜单或路由配置中使用权限控制：

```typescript
// 菜单配置示例
const menuConfig = {
    text: '物料管理',
    link: '/pages/subpackages/material-management/index',
    i18n: 'menu.materialManagement',
    guard: {
        permission: ['EnterpriseService.Wms.ProductItem'],
    },
}

// 判断菜单是否有权限显示
import { hasRoutePermission } from './permissionGuard'
const canShow = hasRoutePermission(menuConfig)
```

### 权限验证模式

支持两种权限验证模式：

1. **OR 模式（默认）**：用户拥有任一指定权限即可访问

```typescript
// 用户拥有 'EnterpriseService.Wms.ProductItem' 或 'EnterpriseService.Wms.Admin' 权限之一即可访问
const menuConfig = {
    text: '物料管理',
    guard: {
        permission: ['EnterpriseService.Wms.ProductItem', 'EnterpriseService.Wms.Admin'],
        mode: 'or', // 这是默认值，可以省略
    },
}
```

2. **AND 模式**：用户必须同时拥有所有指定权限才可访问

```typescript
import { PermissionMode } from './permissionGuard'

// 用户必须同时拥有 'EnterpriseService.Wms.ProductItem' 和 'EnterpriseService.Wms.Edit' 权限才可访问
const menuConfig = {
    text: '高级物料管理',
    guard: {
        permission: ['EnterpriseService.Wms.ProductItem', 'EnterpriseService.Wms.Edit'],
        mode: PermissionMode.AND, // 也可以直接使用字符串 'and'
    },
}
```

## 组件中使用权限指令

Vue 组件中可以使用内置的`v-permission`指令：

```vue
<template>
    <!-- 只有拥有指定权限的用户才能看到此按钮 -->
    <button v-permission="'EnterpriseService.Wms.ProductItem'">添加物料</button>

    <!-- 或者使用多个权限（任一满足即可） -->
    <button v-permission="['EnterpriseService.Wms.ProductItem', 'EnterpriseService.Wms.OtherPermission']">
        编辑物料
    </button>
</template>
```

### v-all-permission 指令（必须同时满足所有权限）

```vue
<template>
    <!-- 用户必须同时拥有所有指定权限才能看到此按钮 -->
    <button v-all-permission="['EnterpriseService.Wms.ProductItem', 'EnterpriseService.Wms.Approve']">审批物料</button>
</template>
```

### v-role 指令（基于角色的权限控制）

```vue
<template>
    <!-- 只有拥有指定角色的用户才能看到此按钮 -->
    <button v-role="'admin'">管理员功能</button>

    <!-- 或者使用多个角色（任一满足即可） -->
    <button v-role="['admin', 'manager']">管理功能</button>
</template>
```
