<template>
    <view class="static-chart-card">
        <nut-empty v-if="isFetchError" description="Error" />
        <EChart v-else ref="canvas" v-show="showCanvas" style="width: 100%; height: 100%" />
    </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import Taro from '@tarojs/taro'
import EChart from './echarts/index'
import { useDidShow } from '@/hooks/component/hooks'
import { t } from '@/locale/fanyi'
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { DeviceWorkCountModelDto, GetDeviceTimeRangeInput } from '@/api/proxy/data-view/models'
import { Toast } from '@/utils/Taro-api'
import dayjs from 'dayjs'

const props = defineProps({
    isDevice: { type: Boolean, default: false },
    deviceId: { type: String, default: null },
    propFactoryModelId: { type: String },
    startTime: { type: String, default: '' },
    endTime: { type: String, default: '' },
})

const canvas = ref<any>(null)
const chartOption = ref<any>()
const datavDataFlowService = new DatavDataFlowService()
const showCanvas = ref<boolean>(true)
const isFetchError = ref<boolean>(false)
const defaultStartTime = `${dayjs().subtract(24, 'hour').format('YYYY-MM-DD HH:mm:ss').toString()}`
const defaultEndTime = `${dayjs().format('YYYY-MM-DD HH:mm:ss').toString()}`
const thisMoment = dayjs().format(' HH:mm:ss').toString()

const buildOption = (datas: DeviceWorkCountModelDto[]) => {
    const option = {
        title: {
            text: t('Platform.ProductionStatistics'),
            left: 'center',
            top: '4%',
            textStyle: {
                fontSize: 18,
                fontWeight: 600,
                color: '#1D3557',
            },
        },
        tooltip: {
            trigger: 'axis',
            alwaysShowContent: false,
            hideDelay: 5000,
            axisPointer: {
                type: 'shadow',
            },
        },
        legend: {
            bottom: '2%',
            textStyle: {
                color: '#333',
                fontWeight: 500,
            },
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '15%',
            containLabel: true,
        },
        xAxis: [
            {
                type: 'value',
                axisLabel: {
                    rotate: 45,
                    color: '#333',
                    fontWeight: 500,
                },
            },
        ],
        yAxis: [
            {
                type: 'category',
                data: datas.map(item => dayjs(item.time).format('MM-DD')),
                axisLabel: {
                    color: '#333',
                    fontWeight: 500,
                    margin: 16,
                },
                axisTick: {
                    alignWithLabel: true
                },
            },
        ],
        series: [
            {
                name: t('Helper.TotalNum'),
                type: 'bar',
                barWidth: 6,
                barGap: '100%',
                emphasis: {
                    focus: 'series',
                },
                label: {
                    show: false,
                    position: 'insideLeft',
                    formatter: params => (params.value !== 0 ? params.value : ''),
                },
                itemStyle: {
                    color: '#4361EE',
                    borderRadius: [0, 4, 4, 0],
                },
                data: datas.map(item => item.totalNum),
            },
            {
                name: t('Platform.QualifiedNum'),
                type: 'bar',
                barWidth: 6,
                stack: 'Ad',
                emphasis: {
                    focus: 'series',
                },
                label: {
                    show: false,
                    position: 'insideLeft',
                    formatter: params => (params.value !== 0 ? params.value : ''),
                },
                itemStyle: {
                    color: '#06A77D',
                    borderRadius: [0, 4, 4, 0],
                },
                data: datas.map(item => item.qualifiedNum),
            },
        ],
    }
    return option
}

/**
 * @description 获取列表
 */
const fetchData = async () => {
    try {
        Taro.showLoading({ title: t('WeChat.LoadingWithThreeDot') })
        const input: GetDeviceTimeRangeInput = {
            deviceId: props.deviceId,
            factoryModelId: props.propFactoryModelId === undefined ? '' : props.propFactoryModelId,
            startTime: props.startTime === '' ? defaultStartTime : `${props.startTime}`,
            endTime: props.endTime === '' ? defaultEndTime : `${props.endTime}${thisMoment}`,
        }
        const result = await datavDataFlowService.getWorkCountStatistics(input)
        chartOption.value = buildOption(result)
        Taro.hideLoading()
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
        isFetchError.value = true
    }
}

/**
 * @description 渲染图表
 */
const renderChart = () => {
    const echartComponentInstance: any = canvas.value
    Taro.nextTick(() => {
        if (!echartComponentInstance) return
        // 初始化图表
        echartComponentInstance.refresh(chartOption.value).then(myChart => {
            myChart.showLoading()
            /** 异步更新图表数据 */
            setInterval(() => {
                myChart.setOption(chartOption.value)
                myChart.hideLoading()
            }, 1000)
        })
    })
}

watch(
    () => [props.propFactoryModelId, props.deviceId, props.isDevice],
    async () => {
        await fetchData()
        renderChart()
    },
    { deep: true },
)

watch(
    () => [props.startTime, props.endTime],
    async () => {
        if (props.startTime && props.endTime) {
            await fetchData()
            renderChart()
        }
    },
    { deep: true },
)

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {
    await fetchData()
    renderChart()
})

defineExpose({})
</script>

<style lang="scss">
.static-chart-card {
    width: 85vw;
    height: 100vh;
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    position: relative;
    
    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 6px;
        height: 60px;
        background: #06a77d;
        border-radius: 0 3px 3px 0;
    }
}
</style>
