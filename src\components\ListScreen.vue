<template>
    <view style="margin-bottom: 20px">
        <nut-menu>
            <nut-menu-item ref="searchMenuRef" v-if="props.isShowSearch" :title="$t('WeChat.Search')">
                <nut-input
                    :placeholder="$t('Helper.SearchWithThreeDot')"
                    v-model="searchKey"
                    clearable
                    class="custom-input"
                >
                    <template #left>
                        <Search2 color="#666"></Search2>
                    </template>
                    <template #right>
                        <view class="button-group">
                            <nut-button type="danger" size="small" @click="updateSearchKey" class="search-btn">
                                {{ $t('WeChat.Search') }}
                            </nut-button>
                            <nut-button
                                type="danger"
                                plain
                                size="small"
                                @click="resetSearch"
                                class="reset-btn"
                                style="margin-left: 10px"
                            >
                                {{ $t('ui.reset') }}
                            </nut-button>
                        </view>
                    </template>
                </nut-input>
            </nut-menu-item>
            <nut-menu-item
                ref="select"
                v-if="props.isShowSelectFactory"
                :title="screenMenuTitle"
                @open="handleShowMenu(true)"
                @close="handleShowMenu(false)"
            >
                <view style="width: 100%">
                    <nut-button type="primary" shape="square" size="mini" style="float: right" @click="cleanSelect">
                        {{ $t('ui.clearSelect') }}
                    </nut-button>
                </view>
                <nut-cascader
                    v-model="cascaderValue"
                    :poppable="false"
                    text-key="name"
                    value-key="id"
                    children-key="children"
                    :title="$t('Platform.FactoryNodeSelectTips')"
                    :options="factoryTreeNode"
                    @path-change="handleCascaderChange"
                ></nut-cascader>
            </nut-menu-item>
            <nut-menu-item
                ref="selectDateTimeRef"
                :title="timeSelectTitle"
                @open="handleShowDialogSelectData()"
                @close="handleShowDialogCloseData()"
                v-if="props.isShowSelectTime"
            >
            </nut-menu-item>
        </nut-menu>
        <nut-popup
            v-model:visible="showTimeSelectPopup"
            position="bottom"
            @close="handleClosePopUp"
            @open="handlePopUpOpen"
        >
            <nut-tabs v-model="tabsValue" direction="vertical" title-scroll style="height: 50vh" name="tabName">
                <nut-tab-pane :title="$t('ui.startTime')" pane-key="1">
                    <nut-date-picker
                        :min-date="minDay"
                        :max-date="maxDay"
                        :model-value="new Date(new Date().getFullYear(), new Date().getMonth(), 1)"
                        :three-dimensional="false"
                        :cancel-text="$t('ui.cancel')"
                        :ok-text="$t('ui.confirm')"
                        @confirm="handleSelectStartTime"
                        @cancel="handleClosePopUp"
                        :safe-area-inset-bottom="true"
                    ></nut-date-picker>
                </nut-tab-pane>
                <nut-tab-pane :title="$t('ui.endTime')" pane-key="2" v-if="timeSelected[0]">
                    <nut-date-picker
                        :min-date="timeSelected[0] ? new Date(timeSelected[0]) : minDay"
                        :max-date="maxDay"
                        :three-dimensional="false"
                        :cancel-text="$t('ui.cancel')"
                        :ok-text="$t('ui.confirm')"
                        @confirm="handleSelectEndTime"
                        @cancel="handleClosePopUp"
                        :safe-area-inset-bottom="true"
                    ></nut-date-picker>
                </nut-tab-pane>
            </nut-tabs>
        </nut-popup>
    </view>
</template>

<script lang="ts" setup>
import { FactoryModelsService } from '@/api/proxy/factory-models-service/factory-models-service.service'
import { FactoryModelsTreeNodesDto } from '@/api/proxy/factory-models-service/models'
import { useDidShow } from '@/hooks/component/hooks'
import { t } from '@/locale/fanyi'
import { ref } from 'vue'
import { Search2 } from '@nutui/icons-vue-taro'
import { Toast } from '@/utils/Taro-api'

const searchKey = ref<string>('')
const screenMenuTitle = ref<string>(t('Platform.FactoryNodeSelectTips'))
const cascaderValue = ref<string[]>([])
const selectDateTimeRef = ref<any>()
const searchMenuRef = ref<any>()
const tabsValue = ref<string>('1')
const lastMonth = new Date()
lastMonth.setMonth(lastMonth.getMonth() - 1)
lastMonth.setDate(1)
const minDay = ref<Date>(lastMonth)
const maxDay = ref<Date>(new Date())
const showTimeSelectPopup = ref<boolean>(false)
const timeSelected = ref<string[]>([])
const timeSelectTitle = ref<string>(t('ui.timeSelect'))
const factoryTreeNode = ref<FactoryModelsTreeNodesDto[]>([])
const factoryModelsService = new FactoryModelsService()
const startTimeWithOutYear = ref<string>('')
const endTimeWithOutYear = ref<string>('')
const emit = defineEmits(['update:search-key', 'update:screen-factory', 'update:show-menu', 'update:selected-time'])
const props = defineProps({
    isShowSelectFactory: {
        type: Boolean,
        default: true,
    },
    isShowSearch: {
        type: Boolean,
        default: true,
    },
    isShowSelectTime: {
        type: Boolean,
        default: false,
    },
})

/**
 * @description 是否展示菜单
 */
const handleShowMenu = (isShowMenu: boolean) => {
    emit('update:show-menu', isShowMenu)
}

/**
 * @description 打开日期选择器
 */
const handleShowDialogSelectData = () => {
    emit('update:show-menu', true)
    showTimeSelectPopup.value = true
}

/**
 * @description 关闭日期选择器
 */
const handleShowDialogCloseData = () => {
    emit('update:show-menu', false)
    showTimeSelectPopup.value = false
}

/**
 * @description 更新搜索关键字
 */
const updateSearchKey = () => {
    emit('update:search-key', searchKey.value)
    // 关闭搜索菜单
    if (searchMenuRef.value) {
        searchMenuRef.value.handleClose()
        searchMenuRef.value.toggle(false)
    }
}

/**
 * @description 重置搜索
 */
const resetSearch = () => {
    searchKey.value = ''
    emit('update:search-key', '')
    // 关闭搜索菜单
    if (searchMenuRef.value) {
        searchMenuRef.value.handleClose()
        searchMenuRef.value.toggle(false)
    }
}

/**
 * @description 选择开始时间
 */
const handleSelectStartTime = (selectedValue: { selectedValue: any[] }) => {
    timeSelected.value[0] = selectedValue.selectedValue.join('-')
    tabsValue.value = '2'
}

/**
 * @description 选择结束时间
 */
const handleSelectEndTime = (selectedValue: { selectedValue: any[] }) => {
    timeSelected.value[1] = selectedValue.selectedValue.join('-')
    startTimeWithOutYear.value = timeSelected.value[0].split('-').slice(1).join('-') //开始时间去掉年份
    endTimeWithOutYear.value = timeSelected.value[1].split('-').slice(1).join('-') //结束时间去掉年份
    timeSelectTitle.value = `${startTimeWithOutYear.value} ~ ${endTimeWithOutYear.value}` //设置时间选择标题
    handleClosePopUp()
}

/**
 * @description 打开PopUp
 */
const handlePopUpOpen = () => {
    timeSelected.value = []
    tabsValue.value = '1'
    timeSelectTitle.value = t('ui.timeSelect') // 初始化标题
}

/**
 * @description 关闭PopUp
 */
const handleClosePopUp = () => {
    showTimeSelectPopup.value = false
    tabsValue.value = '1'
    selectDateTimeRef.value.handleClose() //关闭日期选择器菜单
    selectDateTimeRef.value.toggle(false) //切换菜单选择状态
    emit('update:show-menu', false) //对外通信关闭菜单
    emit('update:selected-time', timeSelected.value) //对外通信选择的时间
    timeSelectTitle.value = t('ui.timeSelect') // 恢复默认标题
}

/**
 * @description 级联选择器改变事件
 * @param pathNodes
 */
const handleCascaderChange = (pathNodes: any) => {
    let checked: any = {}
    let index = pathNodes.indexOf(null)
    if (index !== -1 && index > 0) {
        checked = pathNodes[index - 1]
    } else {
        checked = pathNodes[pathNodes.length - 1]
    }
    screenMenuTitle.value = checked.text
    emit('update:screen-factory', checked.value)
}

/**
 * @description 清除选择
 */
const cleanSelect = () => {
    cascaderValue.value = []
    screenMenuTitle.value = t('Platform.FactoryNodeSelectTips')
    emit('update:screen-factory', '')
}

/**
 * @description 获取工厂节点
 */
const fetchFactoryNode = async () => {
    try {
        const result = await factoryModelsService.getTreeNodes('')
        factoryTreeNode.value = result
        if (result.length > 0 && result[0].id) {
            // 默认选中第一个节点
            emit('update:screen-factory', result[0].id)
        }
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
    }
}

/**
 * @description 清空筛选条件
 */
const cleanScreen = () => {
    screenMenuTitle.value = t('Platform.FactoryNodeSelectTips')
    cascaderValue.value = []
}

useDidShow(() => {
    cleanScreen()
    fetchFactoryNode()
})
</script>

<style>
.button-group {
    display: flex;
    align-items: center;
}

.custom-input {
    --nutui-input-border-bottom: none;
    --nutui-input-padding: 10px;
    background-color: #f7f7f7;
    border-radius: 4px;
}

.search-btn {
    margin: 0;
    border-radius: 4px;
}

.reset-btn {
    margin: 0;
    border-radius: 4px;
    --nutui-button-plain-background-color: transparent;
}
</style>
