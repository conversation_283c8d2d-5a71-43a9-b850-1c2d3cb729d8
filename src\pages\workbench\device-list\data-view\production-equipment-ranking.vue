<template>
    <view class="chart-card" v-show="showCanvas">
        <nut-empty v-if="isFetchError" description="Error" />
        <EChart v-else ref="canvas" style="width: 100%; height: 100%" />
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import EChart from './echarts/index'
import { useDidShow } from '@/hooks/component/hooks'
import { t } from '@/locale/fanyi'
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { DeviceWorkCountRankDto, GetDataViewDeviceInput } from '@/api/proxy/data-view/models'
import { HideLoading, Loading, Toast } from '@/utils/Taro-api'

const canvas = ref<any>(null)
const chartOption = ref<any>()
const datavDataFlowService = new DatavDataFlowService()
const showCanvas = ref<boolean>(true)
const isFetchError = ref<boolean>(false)

const buildOption = (datas: DeviceWorkCountRankDto[]) => {
    const option = {
        title: {
            text: t('Platform.ProductionDeviceRanking'),
            textStyle: {
                fontSize: 18,
                fontWeight: 600,
                color: '#1D3557',
            },
            left: 16,
            top: 16,
        },
        tooltip: {
            trigger: 'axis',
        },
        grid: {
            top: 60,
            left: '2%',
            right: '15%',
            bottom: '2%',
            containLabel: true,
        },
        xAxis: {
            type: 'value',
            show: false,
        },
        yAxis: {
            type: 'category',
            inverse: true,
            axisLine: { show: false },
            axisTick: { show: false },
            data: datas.map(item => item.name),
            axisLabel: {
                color: '#333',
                fontWeight: 500,
            },
        },
        series: [
            {
                name: t('Helper.WorkCount'),
                type: 'bar',
                barWidth: 16,
                label: {
                    show: true,
                    position: 'right',
                    color: '#333',
                    fontWeight: 'bold',
                    fontSize: 14,
                },
                itemStyle: {
                    color: '#4361EE',
                    borderRadius: [0, 4, 4, 0],
                },
                data: datas.map(item => item.total),
            },
        ],
    }
    return option
}

const fetchData = async () => {
    try {
        Loading()
        const input: GetDataViewDeviceInput = {
            factoryParentId: '',
        }
        const result = await datavDataFlowService.getDeviceWorkCountByDevice(input)
        chartOption.value = buildOption(result)
        HideLoading()
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
        isFetchError.value = true
    }
}

useDidShow(async () => {
    await fetchData()
    const echartComponentInstance: any = canvas.value
    if (echartComponentInstance) {
        echartComponentInstance.refresh(chartOption.value)
    }
})

defineExpose({})
</script>

<style lang="scss">
.chart-card {
    width: 90vw;
    height: 50vh;
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 6px;
        height: 60px;
        background: #4361ee;
        border-radius: 0 3px 3px 0;
    }
}
</style>
