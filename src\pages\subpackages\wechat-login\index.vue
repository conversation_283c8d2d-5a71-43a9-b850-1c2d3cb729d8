<template>
    <view class="wechat-login">
        <!-- <view style="background-color: aquamarine; height: 10px"></view> -->
        <nut-button class="login-btn" color="#27b13e" block type="primary" @click="loginClick" :disabled="logining">
            <template #icon>
                <!-- <img :src="wechatPng" /> -->
            </template>
            手机号快捷登录</nut-button
        >
        <nut-popup position="bottom" round :style="{ height: '60%' }" v-model:visible="showRound">
            <view class="popup-content">
                <view>
                    <h3 class="popup-title">服务条款及隐私政策</h3>
                    <p class="popup-tetx">
                        亲爱的用户，感谢您一直以来的支持!为了更好地保护您的权益，同时遵守相关监管要求，特向您说明如下:
                    </p>
                    <p class="popup-tetx">
                        1.为向您提供基本的服务，我们会遵循正当、合法，必要的原则收集和使用必要的信息。
                    </p>
                    <p class="popup-tetx">2.基于您的授权我们可能会收集和使用您的相关信息，您有权拒绝或取消授权。</p>
                    <p class="popup-tetx">
                        3.未经您的授权同意，我们不会将您的信息共享给第三方或用于您未授权的其他用途。
                    </p>
                    <p class="popup-tetx">4.您可以对上述信息进行访问、更正、删除，以及注销账户。</p>
                    <p></p>
                    <p class="popup-tetx">我们竭诚为您服务!</p>
                    <p class="popup-tetx" style="color: red">点击同意表示你已阅读并同意服务条款及隐私政策</p>
                </view>
                <view class="btn">
                    <nut-button class="consent-btn" type="default" @click="cancelLogin">不同意</nut-button>
                    <nut-button
                        class="consent-btn"
                        type="primary"
                        open-type="getPhoneNumber"
                        @getphonenumber="getPhoneNumberCall"
                        >同意
                    </nut-button>
                </view>
            </view>
        </nut-popup>
        <view class="login-tips" @click="onAccountLogin">使用账号登录</view>
    </view>
</template>

<script lang="ts" setup>
import { useAuthStore } from '@/stores/modules/auth'
import { Toast } from '@/utils/Taro-api'
import Taro from '@tarojs/taro'
import { reactive, ref, toRefs } from 'vue'
import wechatPng from '../../../assets/images/wechat.png'
import { LOGIN_PAGE } from '@/enums/routerEnum'
import { getAbpAppConfig } from '@/api/app/appConfig'
import { initializeApp } from '@/hooks/web/initApp'

const showRound = ref<boolean>(false)
const logining = ref<boolean>(false)
let loginCode = ''

/**
 * @description 微信登录按钮点击事件
 */
const loginClick = async () => {
    // Taro.showLoading({ title: '登录中...' });
    // logining.value = true;
    await getLoginCode()
    // logining.value = false;
}

/**
 * @description 获取登录code
 */
const getLoginCode = () => {
    Taro.login({
        success: function (res) {
            // 发送 res.code 到后台换取 openId, sessionKey, unionId
            if (res.code) {
                loginCode = res.code
                showRound.value = true
            } else {
                Toast('获取code失败！', { icon: 'error' })
                failInit()
            }
        },
        fail: () => {
            failInit()
        },
    })
}

/**
 * @description 取消登录
 */
const cancelLogin = () => {
    showRound.value = false
}

/**
 * @description 点击同意按钮获取手机号code回调
 */
const getPhoneNumberCall = (e: any) => {
    if (!e.detail.code) {
        return
    }
    Taro.showLoading({
        title: '获取登录信息中...',
    })
    Taro.getUserInfo({
        success: infoRes => {
            let userInfo = {
                encryptedData: infoRes.encryptedData, //包括敏感数据在内的完整用户信息的加密数据，详细见加密数据解密算法。
                iv: infoRes.iv, //加密算法的初始向量，详细见加密数据解密算法。
                rawData: infoRes.rawData, //不包括敏感信息的原始数据字符串，用于计算签名。
                signature: infoRes.signature, //使用 sha1( rawData + sessionkey ) 得到字符串，用于校验用户信息。
                code: loginCode, //微信登录的code
                mobileCode: e.detail.code, //获取手机号的code
            }
            let user_info = JSON.stringify(userInfo)
            Taro.showLoading({ title: '登录中...' })
            const authStore = useAuthStore()
            authStore
                .wechatlogin(user_info)
                .then(async () => {
                    await getAbpAppConfig()
                    initializeApp()
                    Toast('登录成功')
                    Taro.switchTab({
                        url: '/pages/home/<USER>',
                    })
                })
                .catch(() => {
                    Toast('登录失败！', { icon: 'error' })
                })
                .finally(() => {
                    failInit()
                })
        },
        fail: () => {
            Toast('获取用户信息失败', { icon: 'error' })
            failInit()
        },
    })
}

/**
 * @description 登录失败，恢复页面状态
 */
const failInit = () => {
    showRound.value = false
    logining.value = false
    Taro.hideLoading()
}

/**
 * @description 账号登录
 */
const onAccountLogin = () => {
    Taro.reLaunch({ url: LOGIN_PAGE })
}
</script>

<style lang="scss">
.wechat-login {
    width: 100%;
    margin-top: 40%;
    .login-btn {
        margin: auto;
        width: 70%;
        img {
            height: 38px;
            width: 38px;
        }
    }
    .login-tips {
        margin-top: 5%;
        text-align: center;
        bottom: 130px;
    }
}

.popup-content {
    padding: 28px;
    .popup-title {
        color: black;
        font-size: 32px;
        font-weight: bold;
    }

    .popup-tetx {
        font-size: 24px;
        margin: 20px 0px;
    }

    .btn {
        margin-top: 200px;
        display: flex;
        justify-content: space-between;
        .consent-btn {
            width: 45%;
        }
    }
}
</style>
