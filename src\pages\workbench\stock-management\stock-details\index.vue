<template>
    <view class="stock-detail-container">
        <nut-tabs v-model="tabActive" title-active-color="#0066ff" title-inactive-color="#616161">
            <nut-tab-pane :title="t('text.stockDetails')">
                <StockDetailsTable :detailsData="stockDetail" />
            </nut-tab-pane>
            <nut-tab-pane :title="t('text.statisticInfo')">
                <StatisticInfoTable :materialId="materialId" />
            </nut-tab-pane>
            <nut-tab-pane :title="t('text.supplierInfo')">
                <SupplierInfoTable :materialId="materialId" />
            </nut-tab-pane>
            <nut-tab-pane :title="t('text.materialFlowLog')">
                <MaterialFlowLogTable :materialId="materialId" />
            </nut-tab-pane>
        </nut-tabs>
    </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { t } from '@/locale/fanyi'
import { WmStockQuantityService } from '@/api/proxy/enterprise/controller/wm-stock-quantity.service'
import { WmMaterialService } from '@/api/proxy/enterprise/controller'
import { WmWarehouseLocationService } from '@/api/proxy/enterprise/controller'
import { WmMeasureUnitService } from '@/api/proxy/enterprise/controller'
import { WmCategoryService } from '@/api/proxy/enterprise/controller'
import { Toast, Loading, HideLoading } from '@/utils/Taro-api'
import Taro, { useDidShow } from '@tarojs/taro'
import StockDetailsTable from './component/stock-details-table.vue'
import StatisticInfoTable from './component/statistic-info-table.vue'
import SupplierInfoTable from './component/supplier-info-table.vue'
import MaterialFlowLogTable from './component/material-flow-log-table.vue'

defineOptions({
    name: 'StockDetails',
})

interface StockDetailItem {
    id?: string
    name?: string
    encode?: string
    model?: string
    specification?: string
    describe?: string
    type?: string
    isActivate?: boolean
    safeStock?: string
    minStock?: number
    maxStock?: number
    highValue?: string
    materialsAuditStatus?: string
    images?: string
    measureUnitId?: string
    categoryId?: string
    supplierId?: string
    quantity?: number
    costPrice?: number
    stockStatus?: string
    batchCode?: string
    expireDate?: string
    isFrozen?: boolean
    locationId?: string
    materialId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties?: Record<string, object>
    concurrencyStamp?: string
    locationName?: string
    categoryName?: string
    measureUnitName?: string
}
const tabActive = ref(0)
const stockQuantityService = new WmStockQuantityService()
const materialService = new WmMaterialService()
const locationService = new WmWarehouseLocationService()
const measureUnitService = new WmMeasureUnitService()
const categoryService = new WmCategoryService()
const stockDetail = ref<StockDetailItem>({})
const enrichedData = ref<StockDetailItem>({})
const materialId = ref<string>('')

const fetchRelatedData = async (data: StockDetailItem): Promise<StockDetailItem> => {
    try {
        // 获取位置名称
        if (data.locationId) {
            const locationInfo = await locationService.get(data.locationId)
            data.locationName = locationInfo?.name || ''
        }
        // 获取分类名称
        if (data.categoryId) {
            const categoryInfo = await categoryService.get(data.categoryId)
            data.categoryName = categoryInfo?.name || ''
        }
        // 获取计量单位名称
        if (data.measureUnitId) {
            const unitInfo = await measureUnitService.get(data.measureUnitId)
            data.measureUnitName = unitInfo?.name || ''
        }
        if ((!data.name || data.name.trim() === '') && data.materialId) {
            try {
                const materialInfo = await materialService.get(data.materialId)
                if (materialInfo && materialInfo.name) {
                    data.name = materialInfo.name
                }
            } catch (materialError) {
                console.error('获取物料名称失败:', materialError)
            }
        }

        return data
    } catch (error) {
        console.error('获取关联数据失败:', error)
        return data
    }
}

// 获取库存详情
const getStockDetail = async (id: string) => {
    try {
        Loading()
        const result = await stockQuantityService.get(id)
        if (result) {
            enrichedData.value = await fetchRelatedData(result)
            stockDetail.value = enrichedData.value
            if (result.materialId) {
                materialId.value = result.materialId
            }
        }
    } catch (error) {
        console.error('获取库存详情失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

useDidShow(() => {
    const params = Taro.getCurrentInstance().router?.params
    const id = params?.id
    if (id) {
        getStockDetail(id)
    } else {
        Toast(t('text.paramError'), { icon: 'none' })
    }
})
</script>

<style lang="scss">
.stock-detail-container {
    padding: 16px;
    background-color: #f5f5f5;
    min-height: 100vh;
}
</style>
