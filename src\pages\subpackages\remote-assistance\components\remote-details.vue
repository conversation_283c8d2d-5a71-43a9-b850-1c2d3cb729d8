<template>
    <view class="content-wrapper">
        <nut-cell :title="t('text.name')" :desc="remoteData?.name || remoteAssistanceDetail?.name"></nut-cell>
        <nut-cell
            :title="t('text.creatorName')"
            :desc="remoteData?.creatorName || remoteAssistanceDetail?.creatorName"
        ></nut-cell>
        <nut-cell :title="t('text.factoryModel')" :desc="factoryDevice?.factoryParentName"></nut-cell>
        <nut-cell :title="t('text.produceDutyUserName')" :desc="factoryDevice?.produceDutyUserName"></nut-cell>
        <nut-cell :title="t('text.maintainDutyUserName')" :desc="factoryDevice?.maintainDutyUserName"></nut-cell>
        <nut-cell :title="t('text.iotDevice')" :desc="factoryDevice?.iotDeviceName"></nut-cell>
        <nut-cell :title="t('text.deviceForm')">
            <template #desc>
                <nut-tag type="primary" size="small">{{ t(`tag.${props.remoteData?.deviceForm}`) }}</nut-tag>
            </template>
        </nut-cell>
        <nut-cell :title="t('text.deviceType')">
            <template #desc>
                <nut-tag type="primary" size="small">{{ t(`tag.${props.remoteData?.deviceType}`) }}</nut-tag>
            </template>
        </nut-cell>
        <nut-cell :title="t('text.isActivate')">
            <template #desc>
                <nut-tag type="primary" size="small">{{ t(`tag.${factoryDevice?.isActivate}`) }}</nut-tag>
            </template>
        </nut-cell>
        <nut-cell :title="t('text.creationTime')">
            <template #desc>
                {{
                    dayjs(remoteData?.creationTime || remoteAssistanceDetail?.creationTime).format(
                        'YYYY-MM-DD HH:mm:ss',
                    )
                }}
            </template>
        </nut-cell>
    </view>
</template>

<script setup lang="ts">
import { RemoteAssistanceService } from '@/api/proxy/platform/controller/remote-assistance.service'
import { RemoteAssistanceDetailDto } from '@/api/proxy/platform/dtos'
import { t } from '@/locale/fanyi'
import { Toast } from '@/utils/Taro-api'
import Taro from '@tarojs/taro'
import { useDidShow } from '@tarojs/taro'
import { ref } from 'vue'
import dayjs from 'dayjs'
import { FactoryDeviceService } from '@/api/proxy/factory-device-service/factory-device-service.servicec'
import { FactoryDeviceDetailDto } from '@/api/proxy/factory-device-service/models'
import { RemoteAssistanceListDto } from '@/api/proxy/platform/dtos/models'

defineOptions({
    name: 'RemoteDetails',
})

const props = defineProps<{
    remoteData?: RemoteAssistanceListDto
}>()

const remoteAssistanceService = new RemoteAssistanceService()
const remoteAssistanceDetail = ref<RemoteAssistanceDetailDto>()
const factoryDeviceService = new FactoryDeviceService()
const factoryDevice = ref<FactoryDeviceDetailDto>()

const fetchData = async (id: string) => {
    try {
        const result = await remoteAssistanceService.getById(id)
        remoteAssistanceDetail.value = result
    } catch (error) {
        console.error('获取远程协助详情失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    }
}

const fetchFactoryDevice = async () => {
    try {
        if (remoteAssistanceDetail.value?.factoryDeviceId) {
            const result = await factoryDeviceService.getById(remoteAssistanceDetail.value?.factoryDeviceId)
            factoryDevice.value = result
        }
    } catch (error) {
        console.error('获取工厂设备失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    }
}

useDidShow(async () => {
    const id = Taro.getCurrentInstance().router?.params.id
    if (id) {
        await fetchData(id)
        await fetchFactoryDevice()
    }
})
</script>

<style scoped></style>
