// ESLint 检查 .vue 文件需要单独配置编辑器：
// https://eslint.vuejs.org/user-guide/#editor-integrations
module.exports = {
    root: true,

    env: {
        node: true,
        'vue/setup-compiler-macros': true,
    },

    extends: ['plugin:vue/vue3-essential', 'eslint:recommended', '@vue/prettier', '@vue/typescript'],

    parserOptions: {
        parser: '@typescript-eslint/parser',
    },

    rules: {
        'prettier/prettier': [
            'error',
            {
                singleQuote: true,
                semi: false,
                trailingComma: 'none',
                arrowParens: 'avoid',
                printWidth: 120,
            },
        ],
        'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
        'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
        '@typescript-eslint/no-unused-vars': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    },
    overrides: [
        //给指定的文件添加规则
        {
            files: ['src/pages/index.vue', 'src/pages/**/*.vue', 'src/components/**/*.vue'], // 匹配pages和二级目录中的所以.vue文件
            //给上面匹配的文件指定规则
            rules: {
                'vue/multi-word-component-names': 'off', // 关闭自定义组件名称应该由多单词组成规则
            },
        },
    ],
}
