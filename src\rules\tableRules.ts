// 定义只能是数字的验证器
const numberOnlyValidator = (val: any) => {
    const pattern = /^\d+$/
    if (pattern.test(val)) {
        return Promise.resolve()
    } else {
        return Promise.reject('输入只能包含数字')
    }
}

// 定义不能包含空格的验证器
const noSpaceValidator = (val: any) => {
    const pattern = /^\S*$/
    if (pattern.test(val)) {
        return Promise.resolve()
    } else {
        return Promise.reject('输入不能包含空格')
    }
}

const twoLengthValidator = (val: any) => {
    if (val.length >= 2) {
        return Promise.resolve()
    } else {
        return Promise.reject('长度不能少于2个字符')
    }
}

const threeLengthValidator = (val: any) => {
    if (val.length >= 3) {
        return Promise.resolve()
    } else {
        return Promise.reject('长度不能少于3个字符')
    }
}

// 定义只能包含大小写字母、数字、下划线和连字符的验证器
const encodeValidator = (val: any) => {
    const pattern = /^[A-Za-z0-9_-]+$/
    if (pattern.test(val)) {
        return Promise.resolve()
    } else {
        return Promise.reject('物料编码只能包含字母、数字、下划线或连字符')
    }
}

// 长度验证器
const maxLength16Validator = (val: any) => {
    if (val.length <= 16) {
        return Promise.resolve()
    } else {
        return Promise.reject('长度不能超过16个字符')
    }
}

const maxLength32Validator = (val: any) => {
    if (val.length <= 32) {
        return Promise.resolve()
    } else {
        return Promise.reject('长度不能超过32个字符')
    }
}

export const tableRules = {
    name: [
        { required: true, message: '请输入物料名称' },
        { validator: twoLengthValidator },
        { validator: noSpaceValidator },
        { validator: maxLength32Validator }
    ],
    encode: [
        { required: true, message: '请输入物料编码' },
        { validator: threeLengthValidator },
        { validator: noSpaceValidator },
        { validator: encodeValidator },
        { validator: maxLength16Validator }
    ],
    model: [
        { required: true, message: '请输入物料型号' },
        { validator: threeLengthValidator },
        { validator: noSpaceValidator },
        { validator: maxLength32Validator }
    ],
    specification: [
        { required: true, message: '请输入物料规格' },
        { validator: threeLengthValidator },
        { validator: noSpaceValidator },
        { validator: maxLength32Validator }
    ],
    safeStock: [
        { required: true, message: '请输入安全库存' },
        { validator: threeLengthValidator },
        { validator: noSpaceValidator },
        { validator: numberOnlyValidator },
        { validator: maxLength32Validator }
    ],
    minStock: [
        { required: true, message: '请输入最小库存' },
        { validator: threeLengthValidator },
        { validator: noSpaceValidator },
        { validator: numberOnlyValidator },
        { validator: maxLength32Validator }
    ],
    maxStock: [
        { required: true, message: '请输入最大库存' },
        { validator: threeLengthValidator },
        { validator: noSpaceValidator },
        { validator: numberOnlyValidator },
        { validator: maxLength32Validator }
    ],
    highValue: [
        { required: true, message: '请输入最高值' },
        { validator: threeLengthValidator },
        { validator: noSpaceValidator },
        { validator: numberOnlyValidator },
        { validator: maxLength32Validator }
    ],
}
