import { externalAuthenticate, login } from '@/api/app/auth'
import { AbpTenantService, FindTenantResultDto } from '@/api/proxy/multi-tenancy'
import { TENANT_KEY, TOKEN_KEY } from '@/enums/cacheEnum'
import { getCache, removeCache, setCache } from '@/utils/cache'
import { defineStore } from 'pinia'
import { usePermissionStoreWithOut } from './permission'
import { useUserStoreWithOut } from './user'

interface AuthState {
    tenant: FindTenantResultDto | undefined
    token: string | undefined
}

const useAuthStore = defineStore({
    id: 'authInfo',
    state: (): AuthState => ({
        tenant: undefined,
        token: undefined,
    }),
    getters: {
        getTenant() {
            return this.tenant
        },
        getToken() {
            return this.token
        },
        getIsLogin() {
            return this.token != '' && this.token != undefined
        },
    },
    actions: {
        initialize() {
            this.token = getCache(TOKEN_KEY)
            this.tenant = getCache(TENANT_KEY)
        },

        /**
         * 设置token缓存
         * @param token
         */
        setToken(token: string | undefined) {
            if (token) {
                setCache(TOKEN_KEY, token)
            } else {
                removeCache(TOKEN_KEY)
            }
            this.token = token
            // setCache(TOKEN_KEY, token);
        },

        /**
         * 设置租户缓存
         * @param tenant
         */
        setTenant(tenant: FindTenantResultDto | undefined) {
            if (tenant) {
                setCache(TENANT_KEY, tenant)
            } else {
                removeCache(TENANT_KEY)
            }
            this.tenant = tenant
        },

        /**
         * 根据租户名称获取租户ID
         * @param name 租户名称
         */
        getTenantByName(name?: string): Promise<string> {
            if (!name) {
                this.setTenant(undefined)
                return Promise.reject('租户名称不能为空')
            }
            const abpTenantService = new AbpTenantService()
            return abpTenantService
                .findTenantByName(name)
                .then(res => {
                    if (res.tenantId) {
                        this.setTenant(res)
                        return Promise.resolve('设置租户成功')
                    } else {
                        return Promise.reject('设置的租户不存在')
                    }
                })
                .catch(error => {
                    this.setTenant(undefined)
                    return Promise.reject(error ?? '设置租户失败')
                })
        },

        /**
         * @description 登录
         */
        async login(params: LoginParams): Promise<string> {
            try {
                const res = await login(params)
                this.setToken(res.access_token)
                return '登录成功'
            } catch (error) {
                return await Promise.reject('登录失败')
            }
        },

        /**
         * @description 微信登录
         */
        async wechatlogin(params: string): Promise<string> {
            return externalAuthenticate(params)
                .then(res => {
                    this.setToken(res.access_token)
                    return '登录成功'
                })
                .catch(error => {
                    return Promise.reject('登录失败')
                })
        },

        /**
         * 退出登录
         */
        async logout(): Promise<boolean> {
            // 清除token
            this.setToken(undefined)
            // 清除权限及角色
            const permissionStore = usePermissionStoreWithOut()
            permissionStore.setRoles([])
            permissionStore.setGrantedPolicies([])
            // 清除用户信息
            const userStore = useUserStoreWithOut()
            userStore.setUserInfo([])
            return true
        },
    },
})

export { useAuthStore }
