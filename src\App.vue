<template>
    <div id="app">
        <img src="./assets/logo.png" />
        <div>
            <p>
                If ViewUI is successfully added to this project, you'll see an
                <code v-text="'<Button>'"></code>
                below
            </p>
            <Button type="primary">Button</Button>
        </div>
        <HelloWorld msg="Welcome to Your Vue.js App" />
    </div>
</template>

<script>
import HelloWorld from './components/HelloWorld.vue'

export default {
    name: 'app',
    components: {
        HelloWorld,
    },
}
</script>

<style>
#app {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
    margin-top: 60px;
}
</style>
