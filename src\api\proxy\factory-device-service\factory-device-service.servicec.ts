import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/model/axios'
import { PagedResultDto } from '@/shared/models/dtos'
import {
    BindingIotDeviceInput,
    FactoryDeviceCreateDto,
    FactoryDeviceDetailDto,
    FactoryDeviceEditDto,
    FactoryDeviceListDto,
    GetFactoryDeviceForEditorOutput,
    GetFactoryDeviceInput,
    GetForEditInput,
    NodeSelectDataDto,
} from './models'

export class FactoryDeviceService {
    bindingIot = (input: BindingIotDeviceInput, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/platform/factory-device/binding',
                data: input,
            },
            options,
        )

    create = (input: FactoryDeviceCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/platform/factory-device',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/platform/factory-device/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<FactoryDeviceDetailDto>(
            {
                method: 'GET',
                url: `/api/platform/factory-device/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<FactoryDeviceDetailDto>(
            {
                method: 'GET',
                url: '/api/platform/factory-device/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetFactoryDeviceForEditorOutput>(
            {
                method: 'GET',
                url: '/api/platform/factory-device/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/platform/factory-device/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetFactoryDeviceInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<FactoryDeviceListDto>>(
            {
                method: 'GET',
                url: '/api/platform/factory-device',
                params: {
                    factoryParentId: input.factoryParentId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/platform/factory-device/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    unBindIot = (input: BindingIotDeviceInput, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/platform/factory-device/unbind',
                data: input,
            },
            options,
        )

    update = (input: FactoryDeviceEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/platform/factory-device',
                data: input,
            },
            options,
        )
}
