import { useI18n } from '@/hooks/web/i18n/i18nBaseService'
import { useLocaleStoreWithOut } from '@/stores/modules/locale'
import type { App } from 'vue'
import { setLocaleMessage } from './useLocale'
// import type { I18nOptions } from 'vue-i18n';
// import { createI18n } from 'vue-i18n';

const LocaleStore = useLocaleStoreWithOut()

// export let i18n: ReturnType<typeof createI18n>;

export let l: any

// function createI18nOptions(): I18nOptions {
//   const i18nConfig = {
//     locale: LocaleStore.getLocale,
//     legacy: false,
//     messages: {
//       [LocaleStore.getLocale]: LocaleStore.messages
//     }
//   };
//   return i18nConfig;
//   // setHtmlPageLang(locale);
// }

// setup i18n instance with glob
export async function setupI18n(app: App) {
    //   const options = createI18nOptions();
    // vuei18n在微信小程序无法使用，暂时屏蔽vuei18n的使用
    //   i18n = createI18n(options);
    //   t = i18n.global.t;
    //   app.use(i18n);
    // 使用自定义的本地发翻译
    const { fanyi } = useI18n()
    setLocaleMessage(LocaleStore.getLocale)
    l = fanyi
}
