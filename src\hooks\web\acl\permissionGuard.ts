import { hasPermission } from './permission'
import { isArray } from '@/utils/other/is'
import { ACLCanType } from './acl.type'

// 权限验证模式
export enum PermissionMode {
    // 满足任一权限即可
    OR = 'or',
    // 必须满足所有权限
    AND = 'and',
}

/**
 * 检查路由是否有权限访问
 * @param route 路由对象
 * @returns 是否有权限访问
 */
export function hasRoutePermission(route: any): boolean {
    // 如果没有guard或者guard中没有permission，则默认允许访问
    if (!route || !route.guard || !route.guard.permission) {
        return true
    }
    // 获取权限配置
    const permissions = route.guard.permission as ACLCanType
    // 获取验证模式
    const mode = route.guard.mode || PermissionMode.OR
    // 根据不同模式验证权限
    if (mode === PermissionMode.AND) {
        return hasAllPermissions(permissions)
    } else {
        // 默认为OR模式，使用已有的hasPermission函数检查权限
        return hasPermission(permissions)
    }
}

/**
 * 检查是否同时拥有所有指定权限
 * @param permissions 权限列表
 * @returns 是否同时拥有所有权限
 */
export function hasAllPermissions(permissions?: ACLCanType): boolean {
    if (!permissions || (isArray(permissions) && permissions.length === 0)) {
        return false
    }
    if (!isArray(permissions)) {
        return hasPermission(permissions)
    } else {
        // 必须同时拥有所有指定权限
        return permissions.every(item => hasPermission(item))
    }
}
