export enum FactoryLevels {
    None = 'None',
    Enterprise = 'Enterprise',
    Workshop = 'Workshop',
    ProdLine = 'ProdLine',
    McingUnit = 'McingUnit',
    Equipment = 'Equipment',
    Teams = 'Teams',
    Group = 'Group',
    Undefined = 'Undefined',
}

export interface FactoryModelsTreeNodesDto {
    id?: string
    name?: string
    serialNo: number
    encode?: string
    describe?: string
    level: FactoryLevels
    isActivate: boolean
    factoryParentId?: string
    children: FactoryModelsTreeNodesDto[]
}
