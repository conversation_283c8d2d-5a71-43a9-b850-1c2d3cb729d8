import type { FileDto, GetForEditInput, NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetWmMaterialForEditorOutput,
    GetWmMaterialInput,
    WmMaterialCreateDto,
    WmMaterialDetailDto,
    WmMaterialEditDto,
    WmMaterialListDto,
} from '../wms/dtos/models'
import type { WmMaterialsAuditStatus } from '../wms/wm-materials-audit-status.enum'
import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class WmMaterialService {
    apiName = 'EnterpriseService'

    create = (input: WmMaterialCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-material',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-material/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmMaterialDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-material/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmMaterialDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-material/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmMaterialForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-material/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getList = (options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmMaterialListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-material/all',
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-material/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getSuppliersOptionItems = (id: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-material/supplier-options',
                params: {
                    id,
                },
            },
            options,
        )

    getPaged = (input: GetWmMaterialInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmMaterialListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-material',
                params: {
                    categoryId: input.categoryId,
                    supplierId: input.supplierId,
                    isActivate: input.isActivate,
                    materialsType: input.materialsType,
                    materialsAuditStatus: input.materialsAuditStatus,
                    isDeleted: input.isDeleted,
                    name: input.name,
                    encode: input.encode,
                    model: input.model,
                    specification: input.specification,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    getToExcelFile = (options?: RequestOptions) =>
        defHttp.request<FileDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-material/export-excel',
            },
            options,
        )

    purchaseMaterialIds = (ids: string[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-material/purchaseMaterials',
                data: ids,
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-material/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    update = (input: WmMaterialEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-material',
                data: input,
            },
            options,
        )

    updateAuditStatus = (id: string, auditStatus: WmMaterialsAuditStatus, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-material/AuditStatus',
                params: {
                    id,
                    auditStatus,
                },
            },
            options,
        )
}
