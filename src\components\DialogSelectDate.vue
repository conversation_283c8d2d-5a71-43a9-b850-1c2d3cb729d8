<template>
    <nut-button v-bind="$attrs" @click="showDialog" size="small" type="primary" plain style="margin-bottom: 10px">
        {{ dayjs(datePickerValue).format('YYYY-MM-DD') ?? $t('Platform.DateSelectTips') }}
    </nut-button>
    <nut-dialog
        @closed="handleCloseDialog"
        no-cancel-btn
        no-ok-btn
        :title="$t('Platform.DateSelectTips')"
        v-model:visible="show"
        position="bottom"
    >
        <template #default>
            <nut-date-picker
                v-model="datePickerValue"
                :min-date="props.minDate"
                :max-date="props.maxDate"
                :three-dimensional="false"
                @cancel="handleCloseDialog"
                @confirm="handleConfirm"
            ></nut-date-picker>
        </template>
    </nut-dialog>
</template>

<script setup lang="ts">
import { useDidShow } from '@/hooks/component/hooks'
import dayjs from 'dayjs'
import { ref } from 'vue'

const props = defineProps({
    minDate: { type: Date, default: dayjs().subtract(1, 'months').add(1, 'day').toDate() },
    maxDate: { type: Date, default: dayjs().toDate() },
})

const emit = defineEmits(['update:selectDate', 'show:visible'])
const show = ref<boolean>(false)
const datePickerValue = ref<Date>(dayjs().toDate())

/**
 * @description 显示弹窗
 */
const showDialog = () => {
    show.value = true
    emit('show:visible', true)
}

/**
 * @description 关闭弹窗
 */
const handleCloseDialog = () => {
    show.value = false
    emit('show:visible', false)
}

/**
 * @description 确认选择日期
 */
const handleConfirm = () => {
    emit('update:selectDate', dayjs(datePickerValue.value).format('YYYY-MM-DD'))
    show.value = false
    emit('show:visible', false)
}

/**
 * @description 页面显示时的钩子
 */
useDidShow(() => {
    emit('update:selectDate', dayjs().format('YYYY-MM-DD'))
})
</script>

<style></style>
