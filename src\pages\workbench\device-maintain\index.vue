<template>
    <view class="main-viewer">
        <nut-space direction="vertical" fill>
            <ListScreen
                @update:screen-factory="handleUpdateScreenFactory"
                @update:search-key="handleUpdateSearchKey"
                :is-pull-down="isPullDown"
            />
            <InformationCard
                v-for="(item, index) in deviceList"
                :key="index"
                :item-image-url="item.imagesUrl"
                @click:content="toDeviceDetails(item.iotDeviceId!)"
                :isActivate="item.isActivate"
                :imageStyle="{ height: '100%' }"
            >
                <template #front-title>{{ `[${item.serialNo}]` }}</template>
                <template #title>{{ item.name }}</template>
                <template #line-first>
                    <div>{{ `${$t('Platform.Encode')}:${item.encode}` }}</div>
                </template>
                <template #line-second>
                    <div>{{ `${$t('AbpIdentity.Type')}:${$t(`Platform.${item.deviceType}`)}` }}</div>
                </template>
                <template #line-third>
                    <div>{{ `${$t('ui.factoryName')}:${$t(`${item.factoryParentName}`)}` }}</div>
                </template>
                <template #space-one>
                    <nut-tag :color="item.isActivate ? '#30D479' : '#FF0000'">
                        {{ $t(item.isActivate ? 'Helper.Enable' : 'Helper.Disable') }}
                    </nut-tag>
                </template>
            </InformationCard>
            <nut-empty v-if="deviceList.length === 0" image="empty" :description="$t('Platform.NoData')" />
        </nut-space>
        <view class="no-more" v-if="reachedEnd && searchParams.skipCount > 0">{{ $t('ui.noMore') }}</view>
        <view class="ios-safe-distance" />
    </view>
</template>

<script lang="ts" setup>
import Taro from '@tarojs/taro'
import { reactive, ref } from 'vue'
import { useDidShow, usePullDownRefresh, useReachBottom } from '@/hooks/component/hooks'
import { FactoryDeviceService } from '@/api/proxy/factory-device-service/factory-device-service.servicec'
import { FactoryDeviceListDto } from '@/api/proxy/factory-device-service/models'
import { handleImages } from '@/utils/image'
import { HideLoading, Loading, NavigateTo, RequestFail, Toast } from '@/utils/Taro-api'
import { t } from '@/locale/fanyi'

/**
 * @description 修改导航栏标题
 */
Taro.setNavigationBarTitle({ title: t('Platform.DeviceMaintenance') })

const factoryDeviceService = new FactoryDeviceService()
const isPullDown = ref<boolean>(false)
const deviceList = ref<FactoryDeviceListDto[]>([])
const searchParams = reactive({
    factoryParentId: '',
    sorting: '',
    filterText: '',
    skipCount: 0,
    maxResultCount: 10,
})
const reachedEnd = ref<boolean>(false)
/**
 * @description 更新搜索关键字
 */
const handleUpdateSearchKey = async (key: string) => {
    searchParams.filterText = key
    await fetchFactoryDevice()
}

/**
 * @description 更新筛选工厂
 */
const handleUpdateScreenFactory = async (factoryId: string) => {
    searchParams.factoryParentId = factoryId
    await fetchFactoryDevice()
}

/**
 * @description 获取工厂节点设备
 */
const fetchFactoryDevice = async () => {
    try {
        Loading()
        searchParams.skipCount = 0
        reachedEnd.value = false
        const result = await factoryDeviceService.getPaged(searchParams)
        deviceList.value = result.items
        // 处理图片
        deviceList.value.forEach(item => {
            if (item.images !== undefined) {
                item.imagesUrl = handleImages(item.images)
            }
        })
        HideLoading()

        // 更新 reachedEnd 状态
        reachedEnd.value = result.items.length === 0
    } catch {
        RequestFail()
    }
}

/**
 * @description 清除搜索条件
 */
const clearSearch = () => {
    searchParams.filterText = ''
    searchParams.skipCount = 0
    searchParams.sorting = ''
}

/**
 * @description 跳转设备详情
 */
const toDeviceDetails = (id: string) => {
    NavigateTo(`/pages/workbench/device-maintain/component/device-maintain-details?id=${id}`)
}

/**
 * @description 下拉刷新
 */
usePullDownRefresh(async () => {
    Loading()
    clearSearch()
    await fetchFactoryDevice() //获取工厂设备数据
    Taro.stopPullDownRefresh()
    HideLoading()
})

/**
 * @description 页面触底钩子
 */
useReachBottom(async () => {
    try {
        if (!reachedEnd.value) {
            // 判断是否到达末尾
            Loading()
            searchParams.skipCount += searchParams.maxResultCount
            const result = await factoryDeviceService.getPaged(searchParams)
            deviceList.value = [...deviceList.value, ...result.items]
            // 处理图片
            deviceList.value.forEach(item => {
                if (item.images !== undefined) {
                    item.imagesUrl = handleImages(item.images)
                }
            })
            // 更新 reachedEnd 状态
            reachedEnd.value = result.items.length === 0
            HideLoading()
        }
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
    }
})

/**
 * @description 页面加载时钩子
 */
useDidShow(async () => {
    clearSearch()
})
</script>

<style lang="scss">
.main-viewer {
    padding: 20px;

    .grid-box {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
    }

    .device-card {
        border-radius: 8px;
    }

    .no-more {
        text-align: center;
        padding: 10px;
        color: #999;
        font-size: 32px;
    }

    .ios-safe-distance {
        bottom: 0;
        width: 100%;
        height: 10rpx;
        display: flex;
        z-index: 99;
    }

    .card-row {
        border-radius: 20px;

        .card-image {
            width: 100%;
            height: 210px;
            border-radius: 16px;
        }

        .card-content {
            font-size: 28px;
            color: #999999;
            padding-left: 20px;

            span {
                margin-top: 4px;
            }
        }
    }
}
</style>
