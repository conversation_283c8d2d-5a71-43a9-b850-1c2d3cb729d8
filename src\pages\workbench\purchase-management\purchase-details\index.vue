<template>
    <view class="purchase-details">
        <nut-tabs v-model="tabActive" title-active-color="#0066ff" title-inactive-color="#616161">
            <nut-tab-pane title="基本信息">
                <DetailsTable :detailsList="detailsList" />
            </nut-tab-pane>
            <nut-tab-pane title="采购子项">
                <view v-if="!showItemDetails">
                    <PurchaseItemsTable
                        :purchaseOrderId="purchaseOrderId"
                        ref="itemsTableRef"
                        @showDetails="handleShowDetails"
                    />
                </view>
                <view v-else class="item-details-container">
                    <view class="details-header">
                        <nut-button size="small" type="primary" @click="showItemDetails = false"> 返回列表 </nut-button>
                        <text class="header-title">采购子项详情</text>
                    </view>
                    <PurchaseItemDetails ref="itemDetailsRef" />
                </view>
            </nut-tab-pane>
        </nut-tabs>
    </view>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { ref, nextTick } from 'vue'
import { t } from '@/locale/fanyi'
import { PurchaseOrderService } from '@/api/proxy/enterprise/controller/purchase-order.service'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { Loading, HideLoading, Toast } from '@/utils/Taro-api'
import * as DetailsTableModule from './component/purchase-details-table.vue'
import * as PurchaseItemsTableModule from './component/purchase-items-table.vue'
import * as PurchaseItemDetailsModule from './component/purchase-item-details.vue'
import { useDidShow } from '@/hooks/component/hooks'

// 使用通用方式导入组件
const DetailsTable = DetailsTableModule.default || DetailsTableModule
const PurchaseItemsTable = PurchaseItemsTableModule.default || PurchaseItemsTableModule
const PurchaseItemDetails = PurchaseItemDetailsModule.default || PurchaseItemDetailsModule

// 当前激活的选项卡
const tabActive = ref(0)

// 初始化服务
const purchaseOrderService = new PurchaseOrderService()
const identityUserService = new IdentityUserService()

// 详情数据
const detailsList = ref({})
const purchaseOrderId = ref('')
const itemsTableRef = ref(null)
const itemDetailsRef = ref(null)

// 子项详情显示控制
const showItemDetails = ref(false)

/**
 * @description 获取用户信息
 */
const fetchUserInfo = async (data: any) => {
    try {
        if (data.executorUserId) {
            const executorInfo = await identityUserService.get(data.executorUserId)
            data.executorUserName = `${executorInfo.surname || ''}${executorInfo.name || ''}`
        }
        if (data.applicantUserId) {
            const applicantInfo = await identityUserService.get(data.applicantUserId)
            data.applicantUserName = `${applicantInfo.surname || ''}${applicantInfo.name || ''}`
        }
        return data
    } catch (error) {
        console.error('获取用户信息失败:', error)
        return data
    }
}

/**
 * @description 获取数据
 */
const fetchData = async () => {
    try {
        Loading()

        // 获取路由参数
        const id = Taro.getCurrentInstance()?.router?.params?.id

        if (!id) {
            console.error('ID不存在')
            Toast('参数错误', { icon: 'error' })
            return
        }

        purchaseOrderId.value = id

        // 获取采购单详情
        const result = await purchaseOrderService.get(id)
        if (result) {
            // 获取用户信息并更新数据
            const updatedResult = await fetchUserInfo(result)
            detailsList.value = updatedResult
        } else {
            detailsList.value = {}
        }
    } catch (error) {
        console.error('获取数据错误:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

/**
 * 页面加载时获取数据
 */
useDidShow(() => {
    fetchData()
})

/**
 * 处理显示采购子项详情
 */
const handleShowDetails = async (id: string) => {
    showItemDetails.value = true

    // 等待下一个渲染循环，确保组件已经渲染
    await nextTick()

    // 调用子组件的方法加载详情数据
    if (itemDetailsRef.value && typeof itemDetailsRef.value.fetchData === 'function') {
        itemDetailsRef.value.fetchData(id)
    }
}
</script>

<style lang="scss" scoped>
.purchase-details {
    background-color: #f5f5f5;
    min-height: 100vh;

    :deep(.nut-tabs) {
        .nut-tabs__titles {
            background-color: #fff;
            padding: 8px 0;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .nut-tabs__content {
            padding-top: 10px;
        }

        .nut-tab-pane {
            padding: 0;
        }

        .nut-tabs__titles-item {
            font-size: 15px;

            &.active {
                font-weight: 500;
            }
        }
    }
}

.item-details-container {
    background-color: #fff;

    .details-header {
        display: flex;
        align-items: center;
        padding: 10px 16px;
        border-bottom: 1px solid #f0f0f0;

        .header-title {
            margin-left: 20px;
            font-size: 16px;
            font-weight: 500;
        }
    }
}
</style>
