import type { WmMaterialFlowLogDetailDto, WmMaterialFlowLogListDto } from '../wms/dtos/models'
import type { GetWmMaterialFlowLogInput } from '../wms/dtos/wm-material-flow-log'
import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class WmMaterialFlowLogService {
    apiName = 'EnterpriseService'

    /**
     * @param input 查询参数
     * @param options 请求选项
     * @returns 分页结果
     */
    getPaged = (input: GetWmMaterialFlowLogInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmMaterialFlowLogListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-material-flow-log',
                params: {
                    materialId: input.materialId,
                    isDeleted: input.isDeleted,
                    type: input.type,
                    storageType: input.storageType,
                    outboundType: input.outboundType,
                    categoryId: input.categoryId,
                    measureUnitId: input.measureUnitId,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    /**
     * @param id 记录ID
     * @param options 请求选项
     * @returns 详情DTO
     */
    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmMaterialFlowLogDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-material-flow-log/${id}`,
            },
            options,
        )

    /**

     * @param id 记录ID
     * @param options 请求选项
     * @returns 详情DTO
     */
    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmMaterialFlowLogDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-material-flow-log/id',
                params: {
                    id,
                },
            },
            options,
        )
}
