<template>
    <view class="supplier-info-container">
        <nut-empty v-if="hasError || (suppliers.length === 0 && !loading)" :description="t('text.noSupplierData')" />
        <view v-else>
            <nut-table :columns="columns" :data="suppliers" />
        </view>
    </view>
</template>

<script setup lang="ts">
import { WmMaterialService } from '@/api/proxy/enterprise/controller'
import { ref, watch } from 'vue'
import { useDidShow } from '@tarojs/taro'
import { Loading, HideLoading, Toast } from '@/utils/Taro-api'
import { t } from '@/locale/fanyi'
import { NodeSelectDataDto } from '@/api/proxy/helper/shared/models'

defineOptions({
    name: 'SupplierInfoTable',
})

const props = defineProps<{
    materialId: string
}>()

const wmMaterialService = new WmMaterialService()
const suppliers = ref<NodeSelectDataDto[]>([])
const loading = ref(false)
const hasError = ref(false)
const columns = [
    { title: t('text.supplierName'), key: 'label' },
    { title: t('text.encode'), key: 'encode' },
]

const fetchData = async () => {
    if (!props.materialId) {
        console.warn('materialId为空，无法获取供应商数据')
        return
    }
    try {
        loading.value = true
        hasError.value = false
        Loading()
        const result = await wmMaterialService.getSuppliersOptionItems(props.materialId)
        suppliers.value = result || []
    } catch (error) {
        console.error('获取供应商数据失败:', error)
        hasError.value = true
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        loading.value = false
        HideLoading()
    }
}

watch(
    () => props.materialId,
    newVal => {
        if (newVal) {
            fetchData()
        }
    },
    { immediate: true },
)

useDidShow(() => {
    if (props.materialId) {
        fetchData()
    }
})
</script>

<style scoped>
.supplier-info-container {
    padding: 16px;
}
</style>
