import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { ReturnResult } from '../../helper/shared/models'
import type {
    WmOutWarehouseListInProgressDto,
    WmOutWarehouseListIsCancelDto,
    WmOutWarehouseListIsOutDto,
    WmOutWarehouseListSubmitDto,
} from '../wms/dtos/models'

export class WorkflowsOutWarehouseListService {
    apiName = 'EnterpriseService'

    inProgressByInput = (input: WmOutWarehouseListInProgressDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-out-warehouse-list/in-progress',
                data: input,
            },
            options,
        )

    isCancelByInput = (input: WmOutWarehouseListIsCancelDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-out-warehouse-list/is-cancel',
                data: input,
            },
            options,
        )

    isOutByInput = (input: WmOutWarehouseListIsOutDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-out-warehouse-list/is-out',
                data: input,
            },
            options,
        )

    submitByInput = (input: WmOutWarehouseListSubmitDto, options?: RequestOptions) =>
        defHttp.request<ReturnResult>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-out-warehouse-list/submit',
                data: input,
            },
            options,
        )
}
