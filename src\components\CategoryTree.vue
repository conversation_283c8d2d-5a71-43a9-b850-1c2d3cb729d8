<template>
    <view class="category-tree">
        <view class="search-box">
            <nut-searchbar v-model="filterText" :clearable="true" :placeholder="t('text.pleaseEnterName')">
                <template #leftin>
                    <Search2 />
                </template>
                <template #rightout>
                    <nut-button type="primary" size="small" @click="fetchData">
                        {{ t('ui.search') }}
                    </nut-button>
                </template>
            </nut-searchbar>
        </view>

        <view class="tree-container">
            <!-- 根节点 -->
            <view class="tree-node root-node" @click="onClickNode({ id: 'root', name: '全部分类' })">
                <view class="node-content" :class="{ 'node-selected': selectedNodeId === 'root' }">
                    <text class="node-label">全部分类</text>
                </view>
            </view>

            <!-- 递归渲染树节点 -->
            <view v-for="(node, index) in filteredNodes" :key="index" class="tree-node">
                <view
                    class="node-content"
                    :class="{ 'node-selected': selectedNodeId === node.id }"
                    @click="onClickNode(node)"
                >
                    <text class="node-label">{{ node.name }}</text>
                    <nut-tag v-if="node.count" size="small">{{ node.count }}</nut-tag>
                </view>

                <!-- 子节点 -->
                <view
                    v-if="node.children && node.children.length > 0 && node.id && expandedNodes.includes(node.id)"
                    class="child-nodes"
                >
                    <view v-for="(child, childIndex) in node.children" :key="childIndex" class="tree-node child-node">
                        <view
                            class="node-content"
                            :class="{ 'node-selected': selectedNodeId === child.id }"
                            @click.stop="onClickNode(child)"
                        >
                            <text class="node-label">{{ child.name }}</text>
                            <nut-tag v-if="child.count" size="small">{{ child.count }}</nut-tag>
                        </view>

                        <!-- 二级子节点 -->
                        <view
                            v-if="
                                child.children &&
                                child.children.length > 0 &&
                                child.id &&
                                expandedNodes.includes(child.id)
                            "
                            class="child-nodes"
                        >
                            <view
                                v-for="(grandChild, grandChildIndex) in child.children"
                                :key="grandChildIndex"
                                class="tree-node grandchild-node"
                            >
                                <view
                                    class="node-content"
                                    :class="{ 'node-selected': selectedNodeId === grandChild.id }"
                                    @click.stop="onClickNode(grandChild)"
                                >
                                    <text class="node-label">{{ grandChild.name }}</text>
                                    <nut-tag v-if="grandChild.count" size="small">{{ grandChild.count }}</nut-tag>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import { t } from '@/locale/fanyi'
import { Search2 } from '@nutui/icons-vue-taro'
import { WmCategoryService } from '@/api/proxy/enterprise/controller'
import { Toast, Loading, HideLoading } from '@/utils/Taro-api'

// 添加默认导出
defineOptions({
    name: 'CategoryTree',
})

// 定义物料分类树节点接口
interface CategoryTreeNode {
    id?: string
    name?: string
    serialNo?: number
    encode?: string
    describe?: string
    isActivate?: boolean
    categoryId?: string
    children?: CategoryTreeNode[]
    count?: number
}

const props = defineProps({
    isRefresh: { type: Boolean, default: false },
    categoryId: { type: String, default: '' },
})

const emit = defineEmits(['click-node'])

const wmCategoryService = new WmCategoryService()
const filterText = ref('')
const dataTrees = ref<CategoryTreeNode[]>([])
const loading = ref(false)
const selectedNodeId = ref<string>('')
const expandedNodes = ref<string[]>([])

// 过滤后的节点
const filteredNodes = computed(() => {
    if (!filterText.value) return dataTrees.value

    const filterNode = (node: CategoryTreeNode): boolean => {
        if (node.name?.toLowerCase().includes(filterText.value.toLowerCase())) return true

        if (node.children && node.children.length > 0) {
            node.children = node.children.filter(filterNode)
            return node.children.length > 0
        }

        return false
    }

    return dataTrees.value.filter(filterNode)
})

// 获取分类树数据
const fetchData = async () => {
    try {
        loading.value = true
        Loading()

        const result = await wmCategoryService.getTreeNodes(filterText.value)
        if (result) {
            dataTrees.value = result

            // 如果有指定的分类ID，则选中该节点
            if (props.categoryId) {
                selectedNodeId.value = props.categoryId

                // 展开包含该节点的父节点
                const findAndExpandParent = (
                    nodes: CategoryTreeNode[],
                    targetId: string,
                    parentIds: string[] = [],
                ): string[] | null => {
                    for (const node of nodes) {
                        if (node.id === targetId) {
                            return parentIds
                        }

                        if (node.children && node.children.length > 0) {
                            const result = findAndExpandParent(node.children, targetId, [...parentIds, node.id || ''])
                            if (result) return result
                        }
                    }

                    return null
                }

                const parentIds = findAndExpandParent(result, props.categoryId)
                if (parentIds) {
                    expandedNodes.value = [...expandedNodes.value, ...parentIds]
                }
            }
        }
    } catch (error) {
        console.error('获取分类树失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        loading.value = false
        HideLoading()
    }
}

// 点击节点
const onClickNode = (node: CategoryTreeNode) => {
    // 如果点击的是已选中的节点，则取消选中
    if (selectedNodeId.value === node.id) {
        selectedNodeId.value = ''
        emit('click-node', { id: '', name: '' })
        return
    }

    selectedNodeId.value = node.id || ''
    emit('click-node', node)

    // 切换展开/折叠状态
    if (node.children && node.children.length > 0) {
        if (expandedNodes.value.includes(node.id || '')) {
            expandedNodes.value = expandedNodes.value.filter(id => id !== node.id)
        } else {
            expandedNodes.value.push(node.id || '')
        }
    }
}

// 清除选择
const clearSelection = () => {
    selectedNodeId.value = ''
    emit('click-node', { id: '', name: '' })
}

// 监听刷新信号
watch(
    () => props.isRefresh,
    newVal => {
        if (newVal) {
            fetchData()
        }
    },
)

// 组件挂载时获取数据
fetchData()

// 暴露方法给父组件
defineExpose({
    fetchData,
    clearSelection,
})
</script>

<style lang="scss">
.category-tree {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 12px;

    .search-box {
        padding: 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    .tree-container {
        padding: 8px 0;

        .tree-node {
            position: relative;

            .node-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;

                &.node-selected {
                    background-color: #f0f5ff;
                    color: #4970f2;
                }

                .node-label {
                    flex: 1;
                }
            }

            &.root-node {
                .node-content {
                    font-weight: 500;
                }
            }

            .child-nodes {
                margin-left: 16px;

                .child-node {
                    .node-content {
                        padding: 10px 16px;
                    }
                }

                .grandchild-node {
                    .node-content {
                        padding: 8px 16px;
                    }
                }
            }
        }
    }
}
</style>
