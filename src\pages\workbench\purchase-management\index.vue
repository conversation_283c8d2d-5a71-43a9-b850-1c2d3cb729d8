<template>
    <view>
        <view>
            <nut-menu>
                <nut-menu-item ref="searchBox" :title="t('ui.search')">
                    <nut-searchbar v-model="editSearch" :clearable="true" :placeholder="t('text.pleaseEnterName')">
                        <template #leftin>
                            <Search2 />
                        </template>
                        <template #rightout>
                            <view style="display: flex; gap: 10px">
                                <nut-button type="primary" size="small" @click="search">
                                    {{ t('ui.search') }}
                                </nut-button>
                                <nut-button type="primary" plain size="small" @click="resetSearch">
                                    {{ t('ui.reset') }}
                                </nut-button>
                            </view>
                        </template>
                    </nut-searchbar>
                </nut-menu-item>
            </nut-menu>
        </view>
        <view style="padding: 8px">
            <nut-row>
                <nut-col :span="24" style="text-align: right">
                    <nut-button type="info" plain size="small" @click="loading">
                        {{ t('ui.refresh') }}
                    </nut-button>
                </nut-col>
            </nut-row>
        </view>
        <view class="content-wrapper">
            <view v-if="purchaseList.length > 0">
                <view v-for="(item, index) in purchaseList" :key="index" @click="handleInfo(item)">
                    <InformationCard :show-empty-image="false">
                        <template #front-title> [{{ (currentPage - 1) * pageSize + index + 1 }}] </template>
                        <template #title>
                            {{ item.name }}
                        </template>
                        <template #line-first> {{ t('text.encode') }} ： {{ item.encode }} </template>
                        <template #line-second> {{ t('text.describe') }} ： {{ item.description }} </template>
                        <template #line-third>
                            <span>{{ t('text.creationTime') }} ：{{ formatTime(item.creationTime) }}</span>
                        </template>
                        <template #space-one>
                            <nut-tag :type="getStatusType(item.status)" style="margin-bottom: 0.5vh">
                                {{ getStatusText(item.status) }}
                            </nut-tag>
                        </template>
                        <template #space-two>
                            <view @click.stop>
                                <action-menu
                                    :options="actionOptions"
                                    direction="left"
                                    @select="(action, _) => handleActionSelect(action, item)"
                                />
                            </view>
                        </template>
                    </InformationCard>
                </view>
            </view>
            <view v-else class="empty-state">
                <nut-empty description="暂无数据" image="empty"> </nut-empty>
            </view>
        </view>
        <nut-pagination
            v-model="currentPage"
            :total-items="totalCount"
            :items-per-page="pageSize"
            @change="handlePageChange"
        />
        <nut-popup v-model:visible="editVisible" round :style="{ padding: '16px', height: '85%', width: '92%' }">
            <purchase-edit-form />
        </nut-popup>
        <nut-dialog
            :title="t('text.warmReminder')"
            :content="t('text.isDelete')"
            v-model:visible="isDelete"
            @cancel="isDelete = false"
            @ok="confirmDel"
            :ok-text="t('ui.confirm')"
            :cancel-text="t('ui.cancel')"
        />
    </view>
</template>

<script lang="ts" setup>
import { useDidShow } from '@/hooks/component/hooks'
import { ref } from 'vue'
import { t } from '@/locale/fanyi'
import { Search2 } from '@nutui/icons-vue-taro'
import dayjs from 'dayjs'
import { PurchaseOrderService } from '@/api/proxy/enterprise/controller/purchase-order.service'
import { NavigateTo, Toast, Loading, HideLoading } from '@/utils/Taro-api'
import InformationCard from '@/components/InformationCard.vue'
import { ActionMenuItem } from '@/components/ActionMenu/models/ActionMenuDto'
import PurchaseEditForm from './purchase-details/component/purchase-edit-form.vue'

interface PurchaseOrderItem {
    id: string
    name?: string
    encode?: string
    description?: string
    orderDate?: string
    eta?: string
    ata?: string
    status: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    totalPrice: number
    creatorId?: string
    creationTime?: string
}

const purchaseOrderService = new PurchaseOrderService()

// 定义采购单状态映射
const purchaseStatusMap = {
    Newly: { type: 'default', text: '新建' },
    PendingApproval: { type: 'warning', text: '待审批' },
    Approved: { type: 'success', text: '已审批' },
    Purchasing: { type: 'warning', text: '采购中' },
    Ordered: { type: 'warning', text: '已下单' },
    Rejected: { type: 'danger', text: '已拒绝' },
    Cancelled: { type: 'default', text: '已取消' },
    Completed: { type: 'success', text: '已完成' },
    Returned: { type: 'danger', text: '有退回' },
}

const searchBox = ref()
const editSearch = ref('')
const purchaseList = ref<PurchaseOrderItem[]>([])
const originalList = ref<PurchaseOrderItem[]>([])
const isLoading = ref(false)
const isDelete = ref<boolean>(false)
const currentItem = ref<PurchaseOrderItem | null>(null)
const actionOptions = ref<ActionMenuItem[]>([
    {
        text: t('menu.edit'),
        value: 'edit',
    },
    {
        text: t('menu.delete'),
        value: 'delete',
    },
])
const editVisible = ref(false)
const currentPage = ref<number>(1)
const pageSize = ref<number>(5)
const totalCount = ref<number>(0)

// 获取状态类型
const getStatusType = (status: string) => {
    return purchaseStatusMap[status]?.type || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
    return purchaseStatusMap[status]?.text || status
}

// 格式化时间
const formatTime = (time: string | undefined) => {
    if (!time) return '-'
    return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 确认删除
const confirmDel = async () => {
    if (!currentItem.value?.id) return

    try {
        Loading()
        await purchaseOrderService.deleteById(currentItem.value.id)
        Toast(t('text.deleteSuccess'), { icon: 'success' })
        await fetchData()
    } catch (error) {
        console.error('删除失败:', error)
        Toast(t('text.deleteFail'), { icon: 'none' })
    } finally {
        HideLoading()
        isDelete.value = false
    }
}

// 搜索
const search = async () => {
    try {
        Loading()
        currentPage.value = 1
        // 如果搜索框为空，直接重新加载所有数据
        if (editSearch.value.trim() === '') {
            await fetchData()
        } else {
            // 构建搜索参数，支持按名称、编码和描述搜索
            const searchParams = {
                filterText: editSearch.value.trim(),
                maxResultCount: pageSize.value,
                skipCount: (currentPage.value - 1) * pageSize.value,
            }
            // 使用 API 进行搜索
            const result = await purchaseOrderService.getPaged(searchParams)
            if (result && result.items) {
                purchaseList.value = result.items
                totalCount.value = result.totalCount
            } else {
                purchaseList.value = []
                totalCount.value = 0
            }
        }
        if (searchBox.value && typeof searchBox.value.toggle === 'function') {
            searchBox.value.toggle()
        }
    } catch (error) {
        console.error('搜索失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

// 重置搜索
const resetSearch = async () => {
    editSearch.value = ''
    currentPage.value = 1
    await fetchData()
    if (searchBox.value && typeof searchBox.value.toggle === 'function') {
        searchBox.value.toggle()
    }
}

// 加载数据
const fetchData = async () => {
    const input = {
        maxResultCount: pageSize.value,
        skipCount: (currentPage.value - 1) * pageSize.value,
        filterText: editSearch.value.trim(),
    }
    try {
        Loading()
        isLoading.value = true
        const result = await purchaseOrderService.getPaged(input)
        if (result && result.items) {
            purchaseList.value = result.items
            totalCount.value = result.totalCount || 0
        } else {
            purchaseList.value = []
            totalCount.value = 0
        }
    } catch (error) {
        console.error('获取数据失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
        // 设置为空数组，避免undefined
        purchaseList.value = []
        totalCount.value = 0
    } finally {
        HideLoading()
        isLoading.value = false
    }
}

// 刷新数据
const loading = async () => {
    Loading()
    await fetchData()
    HideLoading()
}

// 查看详情
const handleInfo = (item: PurchaseOrderItem) => {
    NavigateTo(`/pages/workbench/purchase-management/purchase-details/index?id=${item.id}`)
}

// 处理删除
const handleDelete = (item: PurchaseOrderItem) => {
    currentItem.value = item
    isDelete.value = true
}

const handleActionSelect = (action: string | number, item: PurchaseOrderItem) => {
    switch (action) {
        case 'edit':
            editVisible.value = true
            break
        case 'delete':
            handleDelete(item)
    }
}

// 处理页码变化
const handlePageChange = async (page: number) => {
    currentPage.value = page
    await fetchData()
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    // 确保页面加载数据
    currentPage.value = 1
    fetchData()
})
</script>

<style lang="scss">
.content-wrapper {
    padding: 12px;
    background: #f5f5f5;
    min-height: calc(100vh - 100px);
}

.empty-state {
    padding: 40px 0;
    text-align: center;
}

.custom-btn {
    &.primary-btn {
        background-color: #4970f2;
    }

    &.outline-btn {
        color: #4970f2;
        border: 1px solid #4970f2;
    }
}

.popup-actions {
    padding: 8px 0;

    .popup-item {
        padding: 8px 16px;
        font-size: 28rpx;
        color: #333;

        &.delete {
            color: #fa2c19;
        }

        &:active {
            background-color: #f5f5f5;
        }
    }
}
</style>
