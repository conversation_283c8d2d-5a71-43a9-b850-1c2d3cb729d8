{"miniprogramRoot": "dist/", "projectname": "taro", "description": "", "appid": "wx1e71402e089a5193", "setting": {"urlCheck": true, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "minified": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "libVersion": "3.5.0", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}