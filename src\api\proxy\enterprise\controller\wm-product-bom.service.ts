import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetWmProductBomEditorInput,
    GetWmProductBomForEditorOutput,
    GetWmProductBomInput,
    WmProductBomCreateDto,
    WmProductBomDetailDto,
    WmProductBomEditDto,
    WmProductBomTreeListDto,
    WmProductBomTreeNodesDto,
} from '../wms/dtos/models'

export class WmProductBomService {
    apiName = 'EnterpriseService'

    create = (input: WmProductBomCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-product-bom',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-product-bom/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmProductBomDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-product-bom/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmProductBomDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-product-bom/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetWmProductBomEditorInput, options?: RequestOptions) =>
        defHttp.request<GetWmProductBomForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-product-bom/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-product-bom/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetWmProductBomInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmProductBomTreeListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-product-bom',
                params: {
                    type: input.type,
                    isActivate: input.isActivate,
                    bomId: input.bomId,
                    materialId: input.materialId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    getTreeNodes = (filterText: string, options?: RequestOptions) =>
        defHttp.request<WmProductBomTreeNodesDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-product-bom/options-trees',
                params: {
                    filterText,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-product-bom/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    update = (input: WmProductBomEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-product-bom',
                data: input,
            },
            options,
        )
}
