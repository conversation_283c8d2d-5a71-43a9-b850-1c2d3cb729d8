import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { GetForEditInput } from '../../helper/shared/models'
import type {
    GetPurchaseOrderForEditorOutput,
    GetPurchaseOrderInput,
    PurchaseOrderCloneDto,
    PurchaseOrderCreateDto,
    PurchaseOrderDetailDto,
    PurchaseOrderEditDto,
    PurchaseOrderListDto,
    PurchaseOrderUpdateStatusDto,
} from '../wms/dtos/models'

export class PurchaseOrderService {
    apiName = 'EnterpriseService'

    clone = (dto: PurchaseOrderCloneDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/purchase-order/ClonePurchaseOrder',
                data: dto,
            },
            options,
        )

    create = (input: PurchaseOrderCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/purchase-order',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/purchase-order/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<PurchaseOrderDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/purchase-order/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<PurchaseOrderDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/purchase-order/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetPurchaseOrderForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/purchase-order/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getPaged = (input: GetPurchaseOrderInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<PurchaseOrderListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/purchase-order',
                params: {
                    status: input.status,
                    applicantUserId: input.applicantUserId,
                    reviewerUserId: input.reviewerUserId,
                    executorUserId: input.executorUserId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    mergePurchaseOrders = (ids: string[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/purchase-order/MergePurchaseOrders',
                params: {
                    ids,
                },
            },
            options,
        )

    update = (input: PurchaseOrderEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/purchase-order',
                data: input,
            },
            options,
        )

    updateStatus = (updateStatusDao: PurchaseOrderUpdateStatusDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/purchase-order/updateStatus',
                data: updateStatusDao,
            },
            options,
        )
}
