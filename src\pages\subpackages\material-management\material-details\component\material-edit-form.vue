<template>
    <view class="material-edit">
        <nut-popup
            v-model:visible="props.isShow"
            :style="{ padding: '16px', height: '85%', width: '92%' }"
            @close="onClosed"
            round
            class="edit-popup"
        >
            <view class="edit-container">
                <view class="edit-header">
                    <text class="header-title">{{ props.name === 'add' ? t('text.add') : t('menu.edit') }}</text>
                </view>

                <nut-form
                    ref="tableRef"
                    :model-value="editObj"
                    :rules="tableRules"
                    class="edit-form"
                    star-position="right"
                >
                    <nut-form-item :label="t('text.materialName')" prop="name">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.name"
                            type="text"
                            @blur="customBlurValidate('name')"
                            :placeholder="t('text.pleaseEnterMaterialName')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.materialCode')" prop="encode">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.encode"
                            type="text"
                            @blur="customBlurValidate('encode')"
                            :placeholder="t('text.pleaseEnterCode')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.materialmodel')" prop="model">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.model"
                            type="text"
                            @blur="customBlurValidate('model')"
                            :placeholder="t('text.pleaseEnterMaterialModel')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.materialSpecification')" prop="specification">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.specification"
                            type="text"
                            @blur="customBlurValidate('specification')"
                            :placeholder="t('text.pleaseEnterMaterialSpecification')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.materialtype')">
                        <view class="manager-selector" @click="showType = true">
                            <text class="selector-text">
                                {{ currentType.text }}
                            </text>
                        </view>
                    </nut-form-item>

                    <nut-form-item :label="t('text.safetyStock')" prop="safeStock">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.safeStock"
                            type="text"
                            @blur="customBlurValidate('safeStock')"
                            :placeholder="t('text.pleaseEnterSafetyStock')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.minStock')" prop="minStock">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.minStock"
                            type="text"
                            @blur="customBlurValidate('minStock')"
                            :placeholder="t('text.pleaseEnterMinStock')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.maxStock')" prop="maxStock">
                        <nut-input
                            class="custom-input"
                            v-model="editObj.maxStock"
                            type="text"
                            @blur="customBlurValidate('maxStock')"
                            :placeholder="t('text.pleaseEnterMaxStock')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.measureUnitName')">
                        <view class="manager-selector" @click="showUnit = true">
                            <text class="selector-text">
                                {{ currentUnit.text }}
                            </text>
                        </view>
                    </nut-form-item>

                    <nut-form-item :label="t('text.materialSort')">
                        <view class="manager-selector" @click="showCategory = true">
                            <text class="selector-text">
                                {{ currentCategory.text }}
                            </text>
                        </view>
                    </nut-form-item>

                    <nut-form-item :label="t('text.supplierName')">
                        <view class="manager-selector" @click="showSupplier = true">
                            <text class="selector-text">
                                {{ currentSupplier.text }}
                            </text>
                        </view>
                        <view v-if="currentSupplier.selected.length > 0" class="selected-suppliers">
                            <nut-tag
                                v-for="(supplier, index) in currentSupplier.selected"
                                :key="supplier.value"
                                type="primary"
                                closeable
                                @close="removeSupplier(index)"
                            >
                                {{ supplier.text }}
                            </nut-tag>
                        </view>
                    </nut-form-item>

                    <nut-form-item :label="t('text.describe')" prop="describe">
                        <nut-textarea
                            v-model="editObj.describe"
                            class="custom-textarea"
                            :autosize="{ maxHeight: 80, minHeight: 60 }"
                            limit-show
                            :max-length="128"
                            :placeholder="t('text.pleaseEnterDescription')"
                        />
                    </nut-form-item>

                    <nut-form-item :label="t('text.isEnable')" class="switch-item">
                        <nut-switch v-model="editObj.isActivate" />
                    </nut-form-item>

                    <nut-form-item :label="t('text.picture')" class="uploader-item">
                        <template v-if="hasPhotoPermission && hasCameraPermission">
                            <nut-uploader url="" multiple maximum="3" />
                        </template>
                        <template v-else>
                            <nut-button size="small" type="primary" @click="requestImageUploadPermission">
                                请求授权
                            </nut-button>
                        </template>
                    </nut-form-item>
                </nut-form>

                <view class="edit-footer">
                    <nut-button class="footer-btn cancel" size="large" @click="onClosed">
                        {{ t('ui.cancel') }}
                    </nut-button>
                    <nut-button
                        class="footer-btn submit"
                        size="large"
                        type="primary"
                        v-if="props.name === 'edit'"
                        @click="confirmEdit"
                    >
                        {{ t('ui.submit') }}
                    </nut-button>
                    <nut-button
                        class="footer-btn submit"
                        size="large"
                        type="primary"
                        v-if="props.name === 'add'"
                        @click="confirmEdit"
                    >
                        {{ t('ui.submit') }}
                    </nut-button>
                </view>
            </view>
        </nut-popup>

        <nut-popup v-model:visible="showType" position="bottom" round class="picker-popup">
            <nut-picker
                :columns="[
                    [
                        { text: '未定义', value: 'None' },
                        { text: '物料', value: 'Material' },
                        { text: '产品', value: 'Product' },
                    ],
                ]"
                :title="t('text.pleaseSelectMaterialType')"
                @confirm="confirmType"
                @cancel="showType = false"
            />
        </nut-popup>

        <nut-popup v-model:visible="showUnit" position="bottom" round class="picker-popup">
            <nut-picker
                :columns="unitColumns"
                :title="t('text.pleaseSelectMaterialUnit')"
                @confirm="confirmUnit"
                @cancel="showUnit = false"
            />
        </nut-popup>

        <nut-popup v-model:visible="showCategory" position="bottom" round class="picker-popup">
            <nut-picker
                :columns="[categoryList]"
                :title="t('text.pleaseSelectMaterialSort')"
                @confirm="confirmCategory"
                @cancel="showCategory = false"
            />
        </nut-popup>

        <nut-popup v-model:visible="showSupplier" position="bottom" round class="picker-popup">
            <nut-picker
                :columns="supplierList"
                :title="t('text.pleaseSelectSupplierName')"
                @confirm="confirmSupplier"
                @cancel="showSupplier = false"
            />
        </nut-popup>
    </view>
</template>

<script lang="ts" setup>
import { WmMaterialService } from '@/api/proxy/enterprise/controller/wm-material.service'
import { WmMeasureUnitService } from '@/api/proxy/enterprise/controller/wm-measure-unit.service'
import { WmSupplierService } from '@/api/proxy/enterprise/controller/wm-supplier.service'
import { WmMaterialCreateDto } from '@/api/proxy/enterprise/wms/dtos'
import { ref, watch, onMounted, computed } from 'vue'
import { tableRules } from '@/rules/tableRules'
import { t } from '@/locale/fanyi'
import Taro, { useDidShow } from '@tarojs/taro'
import { useAuth, AuthScope } from '@/hooks/auth/useAuth'

interface ReceiveData {
    isShow: boolean
    categoryList: any[]
    editList: any
    name: string
}

interface EditData {
    id?: string
    name: string
    encode: string
    model: string
    specification: string
    describe?: string
    isActivate: boolean
    safeStock?: string
    minStock: number
    maxStock: number
    images?: string
    measureUnitId?: string
    categoryId?: string
    supplierIds?: string[]
    type: string
    materialsAuditStatus: any
}

interface OptionItem {
    label: string
    value: string
}

interface UnitColumn {
    text: string
    value: string
}

interface CategoryItem {
    id: string
    text: string
    value: string
}

interface CategoryRef {
    text: string
    value: string
    id?: string
}

// 添加供应商选项接口定义
interface SupplierOption {
    text: string
    value: string
}

// 添加当前供应商引用接口定义
interface CurrentSupplier {
    text: string
    value: string
    selected: SupplierOption[]
}

const props = defineProps<ReceiveData>()

const emit = defineEmits(['update:close', 'update:closeAdd', 'update:closeEdit', 'update:fetch'])

// 使用授权钩子
const { authState, requestMediaAuth, checkAuth } = useAuth()

const hasPhotoPermission = computed(() => authState.value[AuthScope.PhotoAlbum])

const hasCameraPermission = computed(() => authState.value[AuthScope.Camera])

const showType = ref<boolean>(false)

const showUnit = ref<boolean>(false)

const showCategory = ref<boolean>(false)

const showSupplier = ref<boolean>(false)

const unitColumns = ref<UnitColumn[][]>([])

const categoryList = ref<CategoryItem[]>([])

const supplierList = ref<UnitColumn[][]>([])

const currentType = ref({
    text: '未定义',
    value: 'None',
})

const currentUnit = ref({
    text: '请选择物料单位',
    value: '',
})

const currentCategory = ref<CategoryRef>({
    text: '请选择物料分类',
    value: '',
    id: '',
})

const currentSupplier = ref<CurrentSupplier>({
    text: '请选择供应商',
    value: '',
    selected: [],
})

const editObj = ref<EditData>({
    name: '',
    encode: '',
    model: '',
    specification: '',
    isActivate: false,
    minStock: 0,
    maxStock: 0,
    type: 'None',
    materialsAuditStatus: 'None',
})

const tableRef = ref<any>(null)

const wmMaterialService = new WmMaterialService()

const wmMeasureUnitService = new WmMeasureUnitService()

const wmSupplierService = new WmSupplierService()

/**
 * @description 获取数据
 */
const fetchType = () => {
    // 直接使用父组件传递的类型数据，不再请求empty-editor
    if (props.name === 'edit' && props.editList && props.editList.type) {
        // 编辑模式下，直接使用编辑对象的类型
        currentType.value = {
            text: getTypeText(props.editList.type) || '未定义',
            value: props.editList.type || 'None',
        }
    } else {
        // 添加模式下，使用默认类型
        currentType.value = {
            text: '未定义',
            value: 'None',
        }
    }
}

/**
 * @description 根据类型值获取类型文本
 */
const getTypeText = (typeValue: string): string => {
    // Material=1, Product=2, None=0 对应的文本
    switch (typeValue) {
        case 'Material':
        case '1':
            return '物料'
        case 'Product':
        case '2':
            return '产品'
        default:
            return '未定义'
    }
}

/**
 * @description 获取单位
 */
const fetchUnit = async () => {
    try {
        const result = (await wmMeasureUnitService.getOptionItems('')) as OptionItem[]
        unitColumns.value = [
            result.map(
                (item: OptionItem): UnitColumn => ({
                    text: item.label,
                    value: item.value,
                }),
            ),
        ]
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

/**
 * @description 获取分类
 */
const fetchSupplier = async () => {
    try {
        // 获取供应商选项列表
        const result = (await wmSupplierService.getOptionItems('')) as OptionItem[]
        supplierList.value = [
            result.map(
                (item: OptionItem): UnitColumn => ({
                    text: item.label,
                    value: item.value,
                }),
            ),
        ]

        // 如果是编辑模式，尝试获取当前物料的关联供应商
        if (props.name === 'edit' && props.editList && props.editList.id) {
            try {
                const supplierOptions = await wmMaterialService.getSuppliersOptionItems(props.editList.id)
                if (supplierOptions && Array.isArray(supplierOptions) && supplierOptions.length > 0) {
                    // 保存当前物料的供应商ID列表，用于初始化选择
                    const selectedSuppliers: SupplierOption[] = supplierOptions
                        .filter(item => item.label && item.value) // 过滤掉没有必要字段的项
                        .map(item => ({
                            text: item.label as string, // 类型断言
                            value: item.value as string, // 类型断言
                        }))

                    currentSupplier.value.selected = selectedSuppliers

                    // 如果有供应商，更新显示文本
                    if (selectedSuppliers.length > 0) {
                        currentSupplier.value.text =
                            selectedSuppliers.length > 1
                                ? `已选择${selectedSuppliers.length}个供应商`
                                : selectedSuppliers[0].text
                    }
                }
            } catch (error) {
                console.error('获取物料供应商数据错误:', error.response ? error.response.data : error.message)
            }
        }
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

/**
 * @description 确认类型
 */
const confirmType = ({ selectedOptions }) => {
    currentType.value = selectedOptions[0]
    showType.value = false
}

/**
 * @description 确认单位
 */
const confirmUnit = ({ selectedOptions }) => {
    currentUnit.value = selectedOptions[0]
    showUnit.value = false
}

/**
 * @description 确认分类
 */
const confirmCategory = ({ selectedOptions }) => {
    currentCategory.value = selectedOptions[0]
    showCategory.value = false
}

/**
 * @description 确认供应商
 */
const confirmSupplier = ({ selectedOptions }) => {
    if (!selectedOptions || !selectedOptions[0]) return
    // 保存选中的供应商信息
    const selected = selectedOptions[0]
    // 类型安全检查
    if (!selected.text || !selected.value) {
        console.warn('供应商选项缺少必要字段', selected)
        return
    }
    // 更新当前显示的供应商文本
    currentSupplier.value.text = selected.text
    currentSupplier.value.value = selected.value
    // 检查是否已经在选中列表中
    const existingIndex = currentSupplier.value.selected.findIndex(item => item.value === selected.value)
    // 如果不在选中列表中，添加到选中列表
    if (existingIndex === -1) {
        const newSupplier: SupplierOption = {
            text: selected.text,
            value: selected.value,
        }
        currentSupplier.value.selected.push(newSupplier)
        // 如果有多个供应商被选中，更新文本显示
        if (currentSupplier.value.selected.length > 1) {
            currentSupplier.value.text = `已选择${currentSupplier.value.selected.length}个供应商`
        }
    }

    showSupplier.value = false
}

// 弹窗关闭时的处理
const onClosed = () => {
    emit('update:closeAdd', false)
    emit('update:closeEdit', false)
    editObj.value = {
        name: '',
        encode: '',
        model: '',
        specification: '',
        isActivate: false,
        minStock: 0,
        maxStock: 0,
        type: 'None',
        materialsAuditStatus: 'None',
    }
    currentType.value = {
        text: '未定义',
        value: 'None',
    }
    currentUnit.value = {
        text: '请选择物料单位',
        value: '',
    }
    currentCategory.value = {
        text: '请选择物料分类',
        value: '',
        id: '',
    }
    // 重置供应商状态为初始值
    const emptySupplierState: CurrentSupplier = {
        text: '请选择供应商',
        value: '',
        selected: [], // 空数组，类型已定义为SupplierOption[]
    }
    currentSupplier.value = emptySupplierState
}

/**
 * @description 请求图片上传授权
 */
const requestImageUploadPermission = async () => {
    try {
        await requestMediaAuth()
    } catch (error) {
        console.error('授权请求失败', error)
    }
}

/**
 * @description 自定义校验
 */
const customBlurValidate = prop => {
    tableRef.value?.validate(prop)
}

/**
 * @description 确认编辑
 */
const confirmEdit = async () => {
    editObj.value.type = currentType.value.value
    editObj.value.measureUnitId = currentUnit.value.value
    editObj.value.categoryId = currentCategory.value.id || ''

    // 从已选择的供应商列表中提取ID数组
    editObj.value.supplierIds = currentSupplier.value.selected.map(item => item.value)

    tableRef.value?.validate().then(async ({ valid }) => {
        if (valid && props.name === 'add') {
            try {
                const input: WmMaterialCreateDto = {
                    ...editObj.value,
                }
                await wmMaterialService.create(input)
                emit('update:closeAdd', false)
                emit('update:fetch')
            } catch (error) {
                console.error('获取数据错误:', error.response ? error.response.data : error.message)
            }
        } else if (valid && props.name === 'edit') {
            try {
                const input: WmMaterialCreateDto = {
                    ...editObj.value,
                    id: props.editList.id,
                }
                await wmMaterialService.update(input)
                emit('update:closeEdit', false)
                emit('update:fetch')
            } catch (error) {
                console.error('获取数据错误:', error.response ? error.response.data : error.message)
            }
        }
    })
}

// 监听 isShow 的变化，当它变为 true 时执行异步操作
watch(
    () => props.isShow,
    async newVal => {
        if (newVal) {
            checkAuth([AuthScope.Camera, AuthScope.PhotoAlbum]) // 检查授权状态

            // fetchType是同步方法，不需要await
            fetchType()

            await fetchUnit()
            await fetchSupplier()

            if (props.categoryList && Array.isArray(props.categoryList)) {
                categoryList.value = props.categoryList.map(
                    (item: any): CategoryItem => ({
                        id: item.id || '',
                        text: item.text || '',
                        value: item.value || '',
                    }),
                )
            }

            if (props.name === 'edit') {
                editObj.value = { ...props.editList }
                currentUnit.value = unitColumns.value[0]?.find(item => item.value === props.editList.measureUnitId) || {
                    text: '请选择物料单位',
                    value: '',
                }
                currentCategory.value = categoryList.value.find(item => item.id === props.editList.categoryId) || {
                    text: '请选择物料分类',
                    value: '',
                    id: '',
                }
                // 如果 fetchSupplier 没有成功加载供应商数据，则尝试从 props.editList.suppliers 中获取
                if (
                    props.editList.suppliers &&
                    Array.isArray(props.editList.suppliers) &&
                    props.editList.suppliers.length > 0 &&
                    currentSupplier.value.selected.length === 0
                ) {
                    // 转换供应商数据结构
                    const selectedSuppliers: SupplierOption[] = props.editList.suppliers
                        .filter(supplier => supplier && supplier.name && (supplier.id || supplier.encode))
                        .map(supplier => ({
                            text: supplier.name,
                            value: supplier.id || supplier.encode,
                        }))
                    if (selectedSuppliers.length > 0) {
                        // 更新已选供应商
                        currentSupplier.value.selected = selectedSuppliers
                        // 更新显示文本
                        currentSupplier.value.text =
                            selectedSuppliers.length > 1
                                ? `已选择${selectedSuppliers.length}个供应商`
                                : selectedSuppliers[0].text
                    }
                }
            }
        }
    },
)

/**
 * @description 移除已选供应商
 */
const removeSupplier = (index: number) => {
    // 从已选列表中移除指定索引的供应商
    currentSupplier.value.selected.splice(index, 1)
    // 更新显示文本
    if (currentSupplier.value.selected.length === 0) {
        currentSupplier.value.text = '请选择供应商'
    } else if (currentSupplier.value.selected.length === 1) {
        currentSupplier.value.text = currentSupplier.value.selected[0].text
    } else {
        currentSupplier.value.text = `已选择${currentSupplier.value.selected.length}个供应商`
    }
}

// 组件挂载时检查授权
useDidShow(() => {
    checkAuth([AuthScope.Camera, AuthScope.PhotoAlbum])
})
</script>

<style lang="scss" scoped>
.material-edit {
    .edit-popup {
        border-radius: 24px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    }

    .edit-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        background: #ffffff;
    }

    .edit-header {
        padding: 20px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        position: relative;

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            position: relative;
            display: inline-block;
            padding: 0 16px;

            &::before,
            &::after {
                content: '';
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 20px;
                background: #0066ff;
                border-radius: 2px;
            }

            &::before {
                left: 0;
            }

            &::after {
                right: 0;
            }
        }
    }

    .edit-form {
        height: calc(100% - 180px);
        overflow-y: auto;
        padding: 0 16px;

        :deep(.nut-form-item) {
            margin-bottom: 16px;

            .nut-form-item__label {
                font-size: 14px;
                color: #333;
                font-weight: 500;
            }
        }

        .custom-input {
            border-bottom: 1px solid #eee;
            transition: all 0.3s;

            &:focus {
                border-color: #0066ff;
            }
        }

        .custom-textarea {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 8px;
            transition: all 0.3s;

            &:focus {
                border-color: #0066ff;
            }
        }

        .manager-selector {
            padding: 8px 0;
            border-bottom: 1px solid #eee;

            .selector-text {
                color: #666;
                font-size: 14px;
            }
        }

        .switch-item {
            :deep(.nut-switch) {
                transform: scale(0.8);
            }
        }

        .uploader-item {
            :deep(.nut-uploader) {
                border-radius: 8px;
                overflow: hidden;
            }

            .permission-tip {
                display: block;
                font-size: 12px;
                color: #999;
                margin-top: 4px;
            }
        }
    }

    .edit-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 16px 20px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        justify-content: space-between;
        z-index: 1;

        .footer-btn {
            flex: 1;
            height: 44px;
            border-radius: 22px;
            font-size: 15px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

            &.cancel {
                background: #f8fafc;
                color: #64748b;
                border: 2px solid #e2e8f0;

                &:active {
                    background: #f1f5f9;
                    transform: scale(0.98);
                }
            }

            &.submit {
                background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%);
                color: #fff;
                border: none;

                &:active {
                    transform: scale(0.98);
                    background: linear-gradient(135deg, #0052cc 0%, #004099 100%);
                }
            }
        }
    }

    .selected-suppliers {
        margin-top: 8px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        :deep(.nut-tag) {
            margin-right: 8px;
            margin-bottom: 8px;
        }
    }
}

.picker-popup {
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.08);
}
</style>
