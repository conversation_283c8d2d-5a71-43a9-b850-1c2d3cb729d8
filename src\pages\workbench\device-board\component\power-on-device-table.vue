<template>
    <InformationCard
        v-for="(v, k) in deviceInfoList"
        :key="k"
        :title="v.name"
        @click:content="toDeviceDetails(v.id)"
        :item-image-url="v.images"
        :iotStatus="v.iotStatus"
        :imageStyle="{ height: '128px' }"
    >
        <template #line-first> {{ $t('Platform.Encode') }}: {{ v.encode }}</template>
        <template #line-second> {{ $t('IotService.DeviceForm') }}: {{ $t(`Platform.${v.deviceForm}`) }} </template>
        <template #line-third> {{ $t('IotService.DeviceType') }}: {{ $t(`Platform.${v.deviceType}`) }} </template>
        <template #content-default>
            <view>
                <text>{{ $t('IotService.IotStatus') }}:</text>
                <nut-tag v-if="v.iotStatus === 'OFFLINE'" type="danger">{{ $t(`IotService.${v.iotStatus}`) }}</nut-tag>
                <nut-tag v-else type="primary">{{ $t(`IotService.${v.iotStatus}`) }}</nut-tag>
            </view>
            <view>
                <text>{{ $t('IotService.WorkMode') }}: </text>
                <nut-tag type="success">{{ $t(`IotService.Mode${v.mode}`) }}</nut-tag>
            </view>
        </template>
    </InformationCard>
    <nut-empty v-if="isFetchError" description="Error" />
</template>
<script lang="ts" setup>
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { FactoryDeviceIotFlowListDto } from '@/api/proxy/data-view/models'
import { useDidShow } from '@/hooks/component/hooks'
import { reactive, ref } from 'vue'
import { HideLoading, Loading, NavigateTo, Toast } from '@/utils/Taro-api'
import { t } from '@/locale/fanyi'
import InformationCard from '@/components/InformationCard.vue'
import { handleImages } from '@/utils/image'

const datavDataFlowService = new DatavDataFlowService()
const deviceInfoList = ref<FactoryDeviceIotFlowListDto[]>([])
const isFetchError = ref<boolean>(false)
const params = reactive<any>({
    skipCount: 0,
    maxResultCount: 10,
})

/**
 * @description 获取设备列表
 */
const fetchData = async () => {
    try {
        Loading()
        const result = await datavDataFlowService.getPaged(params)
        deviceInfoList.value = result.items
        deviceInfoList.value.forEach(item => {
            if (item.images !== undefined) {
                item.images = handleImages(item.images)
            }
        })
        HideLoading()
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
        isFetchError.value = true
    }
}

/**
 * @description 跳转到设备详情
 */
const toDeviceDetails = (id: string) => {
    NavigateTo(`/pages/workbench/device-list/device-details/index?id=${id}`)
}

/**
 * @description 页面显示时钩子
 */
useDidShow(() => {
    fetchData()
})
</script>
<style lang="scss" scoped></style>
