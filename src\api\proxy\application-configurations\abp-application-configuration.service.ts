import { RequestOptions } from '@/utils/http/axios/model/axios'
import type { ApplicationConfigurationDto } from './models'
import { defHttp } from '@/utils/http/axios'

export class AbpApplicationConfigurationService {
    apiName = 'abp'

    get = (options?: RequestOptions) =>
        defHttp.request<ApplicationConfigurationDto>(
            {
                method: 'GET',
                url: '/api/abp/application-configuration',
            },
            options,
        )
}
