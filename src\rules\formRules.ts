// 定义不能包含空格的验证器
const noSpaceValidator = (val: any) => {
    const pattern = /^\S*$/
    if (pattern.test(val)) {
        return Promise.resolve()
    } else {
        return Promise.reject('输入不能包含空格')
    }
}

const twoLengthValidator = (val: any) => {
    if (val.length >= 2) {
        return Promise.resolve()
    } else {
        return Promise.reject('长度不能少于2个字符')
    }
}

const threeLengthValidator = (val: any) => {
    if (val.length >= 3) {
        return Promise.resolve()
    } else {
        return Promise.reject('长度不能少于3个字符')
    }
}

// 定义只能包含大小写字母、数字、下划线和连字符的验证器
const encodeValidator = (val: any) => {
    const pattern = /^[A-Za-z0-9_-]+$/
    if (pattern.test(val)) {
        return Promise.resolve()
    } else {
        return Promise.reject('编码只能包含字母、数字、下划线或连字符')
    }
}

const areaSizeValidator = (val: any) => {
    const pattern = /^\d+(\.\d+)?$/
    if (pattern.test(val)) {
        return Promise.resolve()
    } else {
        return Promise.reject('面积必须是正数')
    }
}

const maxLength16Validator = (val: any) => {
    if (val.length <= 16) {
        return Promise.resolve()
    } else {
        return Promise.reject('长度不能超过16个字符')
    }
}

const maxLength32Validator = (val: any) => {
    if (val.length <= 32) {
        return Promise.resolve()
    } else {
        return Promise.reject('长度不能超过32个字符')
    }
}

// 更新 formRules
export const formRules = {
    name: [
        { required: true, message: '请输入名称' },
        { validator: twoLengthValidator },
        { validator: noSpaceValidator },
        { validator: maxLength32Validator },
    ],
    encode: [
        { required: true, message: '请输入编码' },
        { validator: threeLengthValidator },
        { validator: noSpaceValidator },
        { validator: encodeValidator },
        { validator: maxLength16Validator },
    ],
    describe: [
        { message: '请输入描述' },
        { validator: maxLength32Validator },
    ],
    location: [
        { required: true, message: '请输入位置' },
        { validator: twoLengthValidator },
        { validator: noSpaceValidator },
        { validator: maxLength32Validator },
    ],
    areaSize: [
        { required: true, message: '请输入面积' },
        { validator: areaSizeValidator },
        { validator: noSpaceValidator },
        { validator: maxLength32Validator },
    ],
}
