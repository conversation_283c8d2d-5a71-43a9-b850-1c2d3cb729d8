import { isObject } from './other/is'

/**
 * 深度合并
 * @param src
 * @param target
 */
export function deepMerge<T = any>(src: any = {}, target: any = {}): T {
    let key: string
    for (key in target) {
        src[key] = isObject(src[key]) ? deepMerge(src[key], target[key]) : (src[key] = target[key])
    }
    return src
}

/**
 * Add the object as a parameter to the URL
 * @param baseUrl url
 * @param obj
 * @returns {string}
 * eg:
 *  let obj = {a: '3', b: '4'}
 *  setObjToUrlParams('www.baidu.com', obj)
 *  ==>www.baidu.com?a=3&b=4
 */
export function setObjToUrlParams(baseUrl: string, obj: any): string {
    let parameters = ''
    for (const key in obj) {
        parameters += key + '=' + encodeURIComponent(obj[key]) + '&'
    }
    parameters = parameters.replace(/&$/, '')
    return /\?$/.test(baseUrl) ? baseUrl + parameters : baseUrl.replace(/\/?$/, '?') + parameters
}

/**
 * 防抖
 * @param fn
 * @param delayTime
 * @returns
 */
export const debounce = (function () {
    // 防抖临时变量
    let timer: any
    return (fn: any, delayTime: number) => {
        clearTimeout(timer)
        timer = setTimeout(() => {
            fn()
        }, delayTime)
    }
})()

/**
 * 格式化字符串
 * @param formatted 需要处理的字符串
 * @param args 参数列表，可以是数组，也可以是对象
 * @returns 返回格式化的字符串
 * @example format('Hello, {0}!', ['World'])
 * @example format('Hello, {name}!', {name: 'World'})
 */
export function format(formatted: string, args: Record<string, any> | any[]) {
    if (Array.isArray(args)) {
        for (let i = 0; i < args.length; i++) {
            const regexp = new RegExp(`\\{${i}\\}`, 'gi')
            formatted = formatted.replace(regexp, String(args[i]))
        }
    } else if (typeof args === 'object') {
        Object.keys(args).forEach(key => {
            const regexp = new RegExp(`\\{${key}\\}`, 'gi')
            formatted = formatted.replace(regexp, String(args[key]))
        })
    }
    return formatted
}
