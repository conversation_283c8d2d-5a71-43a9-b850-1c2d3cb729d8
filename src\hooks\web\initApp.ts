import { useAuthStore } from '@/stores/modules/auth'
import { useLocaleStore } from '@/stores/modules/locale'
import { usePermissionStore } from '@/stores/modules/permission'
import { useUserStore } from '@/stores/modules/user'

// app初始化
export function initializeApp() {
    const permissionStore = usePermissionStore()
    const userStore = useUserStore()
    const localeStore = useLocaleStore()
    const authStore = useAuthStore()
    // 初始化store
    permissionStore.initialize()
    userStore.initialize()
    localeStore.initialize()
    authStore.initialize()
}
