import type { GetForEditInput, NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetWmSupplierForEditorOutput,
    GetWmSupplierInput,
    WmSupplierCreateDto,
    WmSupplierDetailDto,
    WmSupplierEditDto,
    WmSupplierListDto,
} from '../wms/dtos/models'
import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class WmSupplierService {
    apiName = 'EnterpriseService'

    create = (input: WmSupplierCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-supplier',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-supplier/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmSupplierDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-supplier/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmSupplierDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-supplier/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmSupplierForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-supplier/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-supplier/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetWmSupplierInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmSupplierListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-supplier',
                params: {
                    isDeleted: input.isDeleted,
                    isActivate: input.isActivate,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-supplier/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    update = (input: WmSupplierEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-supplier',
                data: input,
            },
            options,
        )
}
