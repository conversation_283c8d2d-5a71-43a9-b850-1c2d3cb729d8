<template>
    <view class="home-container">
        <nut-swiper :init-page="page" :pagination-visible="true" pagination-color="#426543" auto-play="3000">
            <nut-swiper-item v-for="item in list" :key="item">
                <img :src="item" alt="" />
            </nut-swiper-item>
        </nut-swiper>

        <view class="content-wrapper">
            <nut-noticebar :text="notice" close-mode class="notice-bar" />

            <template v-if="menuList.length > 0">
                <BaseCard v-for="menus in menuList" :key="menus.text" :title="menus.text" class="menu-card">
                    <nut-grid :column-num="4">
                        <nut-grid-item
                            v-for="item in getVisibleChildren(menus)"
                            :key="item.text"
                            :text="item.text"
                            @click="handleMenuClick(item)"
                            class="grid-item"
                        >
                            <IconFont
                                :name="typeof item.icon === 'string' ? item.icon : item.icon?.value"
                                :size="32"
                                class="grid-icon"
                            />
                        </nut-grid-item>
                    </nut-grid>
                </BaseCard>
            </template>
            <template v-else>
                <view class="empty-state">
                    <nut-empty :description="t('text.noMenu')" image="empty"></nut-empty>
                </view>
            </template>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { getAbpAppConfig } from '@/api/app/appConfig'
import { useDidShow, usePullDownRefresh } from '@/hooks/component/hooks'
import { Menu } from '@/hooks/web/menu/interface'
import { useMenuService } from '@/hooks/web/menu/useMenuService'
import { processMenuVisibility } from '@/hooks/web/menu/menuFilter'
import Taro from '@tarojs/taro'
import { computed, ref, watch } from 'vue'
import menus from '@/hooks/web/menu/home/<USER>'
import '@/components/BaseCard.vue'
import { IconFont } from '@nutui/icons-vue-taro'
import { NavigateTo, scanCode } from '@/utils/Taro-api'
import { t } from '@/locale/fanyi'
import { setUpTabBarI18n } from '@/hooks/tabBar'
import { AuthScope, useAuth } from '@/hooks/auth/useAuth'
import { usePermissionStoreWithOut } from '@/stores/modules/permission'

Taro.setNavigationBarTitle({ title: t('ui.industrialCloud') })

const menuList = ref<Menu[]>([])
const page = ref<number>(2)
const list = ref<string[]>([
    'https://img.picui.cn/free/2025/05/10/681ef391d0289.png',
    'https://img.picui.cn/free/2025/05/10/681ef391d24e6.png',
    'https://img.picui.cn/free/2025/05/10/681ef391f0138.png',
])
const notice = ref<string>('欢迎使用工业云小程序，络特拉科技为您服务！此处可作为公告栏，用于发布公告、通知等内容。')
const { authState, requestCameraAuth, checkAuth } = useAuth()
const hasCameraPermission = computed(() => authState.value[AuthScope.Camera])
const permissionStore = usePermissionStoreWithOut()

/**
 * 获取可见的子菜单
 * @param menu 父菜单
 * @returns 可见的子菜单列表
 */
const getVisibleChildren = (menu: Menu) => {
    if (!menu.children) return []
    return menu.children.filter(item => item.visible !== false)
}

/**
 * @description 下拉刷新事件
 */
usePullDownRefresh(() => {
    getAppConfig()
})

/**
 * @description 获取菜单
 */
const getMenu = () => {
    try {
        const menuService = useMenuService()
        menuService.add(menus.menu)
        // 获取经过菜单服务处理后的菜单数据
        const originalMenus = (menuService.menus[0]?.children as Menu[]) || []
        // 处理菜单可见性而不是过滤掉不可见的菜单
        menuList.value = processMenuVisibility(originalMenus)
        // 检查是否有可见的子菜单项
        const hasVisibleItems = menuList.value.some(
            menu => menu.children && menu.children.some(item => item.visible !== false),
        )
        if (!hasVisibleItems) {
            menuList.value = [] // 如果没有可见子项，清空菜单列表触发empty状态
        }
    } catch (error) {
        console.log('获取菜单失败', error)
        menuList.value = [] // 确保菜单列表是数组
    }
}

/**
 * @description 监听权限变更，自动重新获取菜单
 */
watch(
    () => permissionStore.getGrantedPolicies,
    () => {
        // 如果menuList已经存在，先强制重新计算所有菜单的可见性
        if (menuList.value.length > 0) {
            processMenuVisibility(menuList.value)
            // 手动触发视图更新 (Vue的技巧 - 通过替换引用迫使Vue重新渲染)
            menuList.value = [...menuList.value]
            // 检查是否有可见的子菜单项
            const hasVisibleItems = menuList.value.some(
                menu => menu.children && menu.children.some(item => item.visible !== false),
            )
            if (!hasVisibleItems) {
                menuList.value = [] // 如果没有可见子项，清空菜单列表触发empty状态
            }
        } else {
            // 如果menuList为空，则重新获取菜单
            getMenu()
        }
    },
    { deep: true },
)

/**
 * @description 获取app配置
 */
const getAppConfig = async () => {
    try {
        Taro.showLoading({
            title: '初始化信息中...',
        })
        await getAbpAppConfig()
    } catch (error) {
        console.log('获取App配置信息失败')
        Taro.showToast({
            title: '获取App配置信息失败',
            icon: 'error',
            duration: 2000,
        })
    } finally {
        Taro.hideLoading()
        Taro.stopPullDownRefresh()
    }
}

const requestImageUploadPermission = async () => {
    try {
        const result = await requestCameraAuth()
        if (result) {
            handleScan()
        }
    } catch (error) {
        console.error('授权请求失败', error)
    }
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    // 获取菜单
    getMenu()
    setUpTabBarI18n()
    checkAuth([AuthScope.Camera])
    Taro.showShareMenu({
        withShareTicket: true,
    })
})

/**
 * @description 处理菜单点击
 */
const handleMenuClick = (item: Menu) => {
    // 如果是扫一扫菜单
    if (item.i18n === 'menu.scanCode' || item.text === '扫一扫' || item.text === 'Scan Code') {
        // 实时检查相机权限状态
        checkAuth([AuthScope.Camera])
        if (hasCameraPermission.value) {
            handleScan()
        } else {
            requestImageUploadPermission()
        }
    } else if (item.link && item.link !== '') {
        NavigateTo(item.link)
    } else {
        console.log('菜单项没有链接或不是扫一扫:', item)
    }
}

/**
 * @description 处理扫码功能
 */
const handleScan = async () => {
    try {
        const res = await scanCode()
        let path: string | null = null
        let id: string | null = null

        // 定义一个辅助函数来解析查询字符串
        const parseQueryParams = (queryString: string): Record<string, string> => {
            const params: Record<string, string> = {}
            if (!queryString) return params
            queryString.split('&').forEach(item => {
                const [key, value] = item.split('=')
                if (key && value) {
                    try {
                        params[key] = decodeURIComponent(value.replace(/\+/g, ' ')) // 处理 '+' 转空格和解码
                    } catch (e) {
                        console.error(`Error decoding param ${key}=${value}:`, e)
                        params[key] = value // 解码失败则使用原始值
                    }
                }
            })
            return params
        }

        // 优先处理小程序码路径
        if (res.path) {
            try {
                const pathParts = res.path.split('?')
                if (pathParts.length > 1) {
                    const queryParams = parseQueryParams(pathParts[1])
                    path = queryParams.path || null
                    id = queryParams.id || null
                    console.log('Parsed from res.path:', { path, id })
                } else {
                    console.log('res.path has no query parameters:', res.path)
                }
            } catch (e) {
                console.error('Error parsing res.path:', e)
            }
        }
        // 处理普通二维码内容 (尝试作为URL解析)
        else if (res.result) {
            try {
                const resultStr = res.result.toString()
                let queryParams: Record<string, string> = {}
                // 尝试将结果视为完整URL解析
                if (resultStr.includes('?')) {
                    // 兼容性写法：直接提取问号后的部分
                    const queryString = resultStr.substring(resultStr.indexOf('?') + 1)
                    queryParams = parseQueryParams(queryString)
                }
                // 如果不包含 '?'，尝试直接解析key=value&key=value格式
                else if ((resultStr.includes('=') && resultStr.includes('&')) || resultStr.includes('=')) {
                    queryParams = parseQueryParams(resultStr)
                }
                path = queryParams.path || null
                id = queryParams.id || null
                console.log('Parsed from res.result:', { path, id })
            } catch (e) {
                console.error('Error parsing res.result:', e)
            }
        }

        // 根据解析结果进行导航或提示
        if (path && id) {
            // 成功解析出 path 和 id，导航到通用路由页面
            const targetUrl = `/pages/route/index?path=${encodeURIComponent(path)}&id=${encodeURIComponent(id)}`
            console.log('Navigating to route page:', targetUrl)
            NavigateTo(targetUrl)
        } else {
            Taro.showModal({
                title: '扫码结果', // 修改标题为更中性的"扫码结果"
                content: `内容：${res.result || res.path || '无有效内容'}`, // 显示更详细的内容
                showCancel: false,
            })
        }
    } catch (error) {
        // 判断是否是用户取消操作，取消操作不需显示错误信息
        if (
            error &&
            error.errMsg &&
            error.errMsg.indexOf('cancel') === -1 &&
            error.errMsg.indexOf('scanCode:fail') === -1
        ) {
            // 增加对 fail 错误的过滤，避免权限拒绝等情况也弹窗
            console.error('扫码出错：', error)
            Taro.showToast({
                title: '扫码失败',
                icon: 'error',
                duration: 2000,
            })
        } else {
            console.log('扫码操作取消或正常失败:', error) // 记录取消或普通失败日志
        }
    }
}
</script>

<style lang="scss">
.home-container {
    background: linear-gradient(135deg, #f0f5f9 0%, #e8f1f8 100%); // 微妙的渐变背景
    min-height: 100vh;

    .nut-swiper-item {
        line-height: 300px;
        img {
            width: 100%;
            height: 100%;
        }
    }
}

.content-wrapper {
    padding: 12px;

    .notice-bar {
        margin-bottom: 12px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(8px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    .menu-card {
        margin-bottom: 12px;
        border-radius: 12px;
        background: #fff;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

        .grid-item {
            padding: 16px 8px;
            transition: background-color 0.2s;

            .grid-icon {
                color: #426543;
                margin-bottom: 6px;
            }

            .nut-grid-item__text {
                font-size: 26px;
                color: #333;
            }

            &:active {
                background-color: rgba(0, 0, 0, 0.02);
            }
        }
    }
}
</style>
