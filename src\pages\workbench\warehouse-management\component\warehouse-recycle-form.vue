<template>
    <view style="margin: 0; padding: 0">
        <nut-cell
            :title="t('menu.recycle')"
            round-radius="0"
            style="color: #f2f7ff; background-color: #0066ff; margin: 0; font-size: 15px"
        ></nut-cell>
        <nut-table :columns="columns" :data="props.recycleList" striped class="custom-table"></nut-table>
        <nut-dialog
            :title="t('text.warmReminder')"
            :content="t('text.isRecover')"
            v-model:visible="showDialog"
            @cancel="onCancel"
            @ok="onConfirm"
        />
    </view>
</template>
<script setup lang="ts">
import { h, ref } from 'vue'
import { t } from '@/locale/fanyi'
import { Tag, Button, Dialog } from '@nutui/nutui-taro'

// 定义表单数据接口
interface FormData {
    recycleList: any[]
}

// 定义事件
const emit = defineEmits<{
    (event: 'restore', id: string): void
}>()

const props = defineProps<FormData>()

const showDialog = ref(false)
const currentId = ref('')

const handleRestore = (id: string) => {
    currentId.value = id
    showDialog.value = true
}

const onCancel = () => {
    showDialog.value = false
    currentId.value = ''
}

const onConfirm = () => {
    emit('restore', currentId.value)
    showDialog.value = false
    currentId.value = ''
}

const columns = ref([
    {
        title: t('text.name'),
        key: 'name',
        align: 'center',
    },
    {
        title: t('text.encode'),
        key: 'encode',
        align: 'center',
    },
    {
        title: t('text.isEnable'),
        key: 'isActive',
        align: 'center',
        render(row: any) {
            return h(
                Tag,
                {
                    style: {
                        backgroundColor: row.isActivate ? '#30D479' : '#FF0000',
                        verticalAlign: 'middle',
                        lineHeight: '24px',
                        padding: '0 8px',
                        borderRadius: '2px',
                    },
                },
                row.isActivate ? t('Helper.Enable') : t('Helper.Disable'),
            )
        },
    },
    {
        title: t('text.operate'),
        key: 'action',
        align: 'center',
        render(row: any) {
            return h(
                Button,
                {
                    type: 'primary',
                    size: 'small',
                    onClick: () => handleRestore(row.id),
                    style: {
                        backgroundColor: '#4970F2',
                        border: 'none',
                        borderRadius: '4px',
                        verticalAlign: 'middle',
                        lineHeight: '24px',
                        padding: '4px 12px',
                    },
                },
                t('text.recover'),
            )
        },
    },
])
</script>
<style lang="scss" scoped>
:deep(.custom-table) {
    .nut-table-content {
        td {
            height: 44px;
            line-height: 44px;
            vertical-align: middle;
        }
    }
}

:deep(.nut-button) {
    &:active {
        opacity: 0.8;
    }
}
</style>
