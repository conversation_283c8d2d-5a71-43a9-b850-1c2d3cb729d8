import { defineStore } from 'pinia'
import { store } from '..'
import { USER_PERMISSION, USER_ROLES } from '@/enums/cacheEnum'
import { getCache, setCache } from '@/utils/cache'

interface permissionStore {
    roles: string[]
    grantedPolicies: string[]
}

export const usePermissionStore = defineStore({
    id: 'permissionStore',
    state: (): permissionStore => ({
        roles: [],
        grantedPolicies: [],
    }),
    getters: {
        getRoles(state) {
            return state.roles
        },
        getGrantedPolicies(state) {
            return state.grantedPolicies
        },
    },
    actions: {
        initialize() {
            this.roles = getCache<string[]>(USER_ROLES) || []
            this.grantedPolicies = getCache<string[]>(USER_PERMISSION) || []
        },
        setRoles(value: string[]) {
            setCache(USER_ROLES, value)
            this.roles = value
        },
        setGrantedPolicies(values: string[]) {
            setCache(USER_PERMISSION, values)
            this.grantedPolicies = values
        },
    },
})

// Need to be used outside the setup
export function usePermissionStoreWithOut() {
    return usePermissionStore(store)
}
