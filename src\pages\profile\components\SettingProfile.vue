<template>
    <nut-dialog v-model:visible="show" v-bind="attrs">
        <nut-form :rules="formRules" ref="formRef" :model-value="formData" error-message-align="right" :show-error-line="false" :show-error-message="true">
            <nut-form-item :label="$t('ui.username')" prop="userName">
                <nut-input
                    v-model="formData.userName"
                    class="nut-input-text"
                    :placeholder="$t('ui.inputUsername')"
                    type="text"
                    maxlength="32"
                    @blur="customBlurValidate('userName')"
                />
            </nut-form-item>
            <nut-form-item :label="$t('ui.surname')" prop="surname">
                <nut-input
                    v-model="formData.surname"
                    class="nut-input-text"
                    :placeholder="$t('ui.inputSurname')"
                    type="text"
                    maxlength="32"
                    @blur="customBlurValidate('surname')"
                />
            </nut-form-item>
            <nut-form-item :label="$t('ui.name')" prop="name">
                <nut-input
                    v-model="formData.name"
                    class="nut-input-text"
                    :placeholder="$t('ui.inputName')"
                    type="text"
                    maxlength="32"
                    @blur="customBlurValidate('name')"
                />
            </nut-form-item>
            <nut-form-item :label="$t('ui.contactPhone')" prop="phoneNumber">
                <nut-input
                    @blur="customBlurValidate('phoneNumber')"
                    v-model="formData.phoneNumber"
                    class="nut-input-text"
                    :placeholder="$t('ui.inputPhone')"
                    type="number"
                    maxlength="11"
                />
            </nut-form-item>
            <nut-form-item :label="$t('ui.email')" prop="email">
                <nut-input
                    v-model="formData.email"
                    class="nut-input-text"
                    :placeholder="$t('ui.inputEmail')"
                    type="text"
                    maxlength="128"
                    @blur="customBlurValidate('email')"
                />
            </nut-form-item>
        </nut-form>
        <template #footer>
            <nut-space>
                <nut-button size="small" @click="handleCancel">{{ $t('ui.cancel') }}</nut-button>
                <nut-button size="small" type="primary" @click="onOk">{{ $t('ui.confirm') }}</nut-button>
            </nut-space>
        </template>
    </nut-dialog>
</template>

<script lang="ts" setup>
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { IdentityUserUpdateDto } from '@/api/proxy/identity-user-service/models'
import { t } from '@/locale/fanyi'
import { useUserStore } from '@/stores/modules/user'
import { HideLoading, Loading } from '@/utils/Taro-api'
import Taro from '@tarojs/taro'
import { reactive, ref, useAttrs } from 'vue'

const attrs = useAttrs()
const emit = defineEmits(['submit'])
const identityUserService = new IdentityUserService()
const userStore = useUserStore()
const userId = userStore.getUserInfo?.id
const show = ref<boolean>(false)
const formRef = ref<any>(null)
const formData = reactive<IdentityUserUpdateDto>({
    userName: '',
    email: '',
    surname: '',
    name: '',
    phoneNumber: '',
    lockoutEnabled: false,
    roleNames: [],
    organizationUnitIds: [],
    extraProperties: {},
})
const formRules = {
    userName: [
        { required: true, message: t('ui.inputUsername'), trigger: 'blur' },
        { validator: (val: string|any[]) => {
            if (val.length > 32) {
                return Promise.reject(t('ui.usernameTooLong'))
            }
            return Promise.resolve()
        }, trigger: 'blur' }
    ],
    surname: [
        { required: true, message: t('ui.inputSurname'), trigger: 'blur' },
        { validator: (val: string|any[]) => {
            if (val.length > 32) {
                return Promise.reject(t('ui.surnameTooLong'))
            }
            return Promise.resolve()
        }, trigger: 'blur' }
    ],
    name: [
        { required: true, message: t('ui.inputName'), trigger: 'blur' },
        { validator: (val: string|any[]) => {
            if (val.length > 32) {
                return Promise.reject(t('ui.nameTooLong'))
            }
            return Promise.resolve()
        }, trigger: 'blur' }
    ],
    phoneNumber: [
        { required: true, message: t('ui.inputPhone'), trigger: 'blur' },
        { regex: /^\d+$/, message: t('ui.inputCorrectPhone') },
        { validator: (val: string|any[]) => {
            if (val.length > 11) {
                return Promise.reject(t('ui.phoneNumberTooLong'))
            }
            return Promise.resolve()
        }, trigger: 'blur' }
    ],
    email: [
        { required: true, message: t('ui.inputEmail'), trigger: 'blur' },
        { regex: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: t('ui.inputCorrectEmail') },
        { validator: (val: { includes: (arg0: string) => any; startsWith: (arg0: string) => any; endsWith: (arg0: string) => any; split: (arg0: string) => { (): any; new(): any; length: number }; length: number }) => {
            // 检查特殊字符的位置
            if (val.includes('..') || val.startsWith('.') || val.endsWith('.') || 
                val.includes('@.') || val.includes('.@') || val.split('@').length > 2) {
                return Promise.reject(t('ui.inputCorrectEmail'))
            }
            // 检查长度
            if (val.length > 128) {
                return Promise.reject(t('ui.emailTooLong'))
            }
            return Promise.resolve()
        }, trigger: 'blur' }
    ],
}

/**
 * @description 请求数据
 */
const fetchData = async () => {
    try {
        Loading()
        const result = await identityUserService.get(userId)
        Object.assign(formData, result)
        HideLoading()
    } catch {
        Taro.showToast({
            title: t('Helper.Fail'),
            icon: 'error',
        })
    }
}

// 失去焦点校验
const customBlurValidate = async (prop: any) => {
    if (formRef.value) {
        try {
            await formRef.value.validate(prop)
        } catch (error) {
            console.error(error)
        }
    } else {
        console.error('formRef is undefined or null')
    }
}

/**
 * @description 确认
 */
const onOk = () => {
    formRef.value?.validate().then(async ({ valid, errors }) => {
        if (valid) {
            try {
                await identityUserService.update(userId, formData)
                Taro.showToast({
                    title: t('Helper.SuccessfullySaved'),
                    icon: 'success',
                })
                show.value = false
            } catch {
                Taro.showToast({
                    title: t('Helper.Fail'),
                    icon: 'error',
                })
            }
        } else {
            console.warn('error:', errors)
        }
    })
}

/**
 * @description 取消
 */
const handleCancel = () => {
    show.value = false
}

/**
 * @description 显示弹窗
 */
const showDialog = () => {
    fetchData()
    show.value = true
}

defineExpose({
    showDialog,
})
</script>
