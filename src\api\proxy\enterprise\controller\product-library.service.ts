import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetProductLibraryEditorInput,
    GetProductLibraryForEditorOutput,
    GetProductLibraryInput,
    ProductLibraryCreateDto,
    ProductLibraryDetailDto,
    ProductLibraryEditDto,
    ProductLibraryTreeListDto,
    ProductLibraryTreeNodesDto,
} from '../pms/dtos/models'

export class ProductLibraryService {
    apiName = 'EnterpriseService'

    create = (input: ProductLibraryCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/product-library',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/product-library/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<ProductLibraryDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/product-library/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<ProductLibraryDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/product-library/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetProductLibraryEditorInput, options?: RequestOptions) =>
        defHttp.request<GetProductLibraryForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/product-library/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/product-library/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetProductLibraryInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<ProductLibraryTreeListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/product-library',
                params: {
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    getTreeNodes = (filterText: string, options?: RequestOptions) =>
        defHttp.request<ProductLibraryTreeNodesDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/product-library/options-trees',
                params: {
                    filterText,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/product-library/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    update = (input: ProductLibraryEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/product-library',
                data: input,
            },
            options,
        )
}
