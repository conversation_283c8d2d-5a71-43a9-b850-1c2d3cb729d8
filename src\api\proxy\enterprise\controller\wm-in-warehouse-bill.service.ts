import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { GetForEditInput } from '../../helper/shared/models'
import type {
    GetWmInWarehouseBillForEditorOutput,
    GetWmInWarehouseBillInput,
    WmInWarehouseBillCloneDto,
    WmInWarehouseBillCreateDto,
    WmInWarehouseBillDetailDto,
    WmInWarehouseBillEditDto,
    WmInWarehouseBillListDto,
    WmInWarehouseBilllUpdateStatusDto,
} from '../wms/dtos/models'

export class WmInWarehouseBillService {
    apiName = 'EnterpriseService'

    clone = (dto: WmInWarehouseBillCloneDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-in-warehouse-bill/CloneInWarehouseBill',
                data: dto,
            },
            options,
        )

    create = (input: WmInWarehouseBillCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-in-warehouse-bill',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-in-warehouse-bill/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmInWarehouseBillDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-in-warehouse-bill/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmInWarehouseBillDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-in-warehouse-bill/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmInWarehouseBillForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-in-warehouse-bill/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getPaged = (input: GetWmInWarehouseBillInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmInWarehouseBillListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-in-warehouse-bill',
                params: {
                    storageType: input.storageType,
                    auditStatus: input.auditStatus,
                    applicantUserId: input.applicantUserId,
                    reviewerUserId: input.reviewerUserId,
                    executorUserId: input.executorUserId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    update = (input: WmInWarehouseBillEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-in-warehouse-bill',
                data: input,
            },
            options,
        )

    updateStatus = (dto: WmInWarehouseBilllUpdateStatusDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-in-warehouse-bill/updateStatus',
                data: dto,
            },
            options,
        )
}
