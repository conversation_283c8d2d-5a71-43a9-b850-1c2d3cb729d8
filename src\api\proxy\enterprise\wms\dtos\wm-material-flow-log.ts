import type { PagedSortedAndFilteredInputDto } from '../../../helper/shared/models'
import type { WmInWarehouseType } from '../wm-in-warehouse-type.enum'
import type { WmOutWarehouseType } from '../wm-out-warehouse-type.enum'
import type { WmMaterialFlowLogType } from '../wm-material-flow-log-type.enum'

/**
 * 物料流水日志输入参数
 */
export interface GetWmMaterialFlowLogInput extends PagedSortedAndFilteredInputDto {
    /**
     * 物料ID
     */
    materialId?: string

    /**
     * 日志类型
     */
    type?: WmMaterialFlowLogType

    /**
     * 入库类型
     */
    storageType?: WmInWarehouseType

    /**
     * 出库类型
     */
    outboundType?: WmOutWarehouseType

    /**
     * 分类ID
     */
    categoryId?: string

    /**
     * 计量单位ID
     */
    measureUnitId?: string

    /**
     * 是否已删除
     */
    isDeleted?: boolean

    /**
     * 排序参数
     */
    sorting?: string

    /**
     * 过滤文本
     */
    filterText?: string

    /**
     * 跳过数量
     */
    skipCount?: number

    /**
     * 最大结果数
     */
    maxResultCount?: number
}
