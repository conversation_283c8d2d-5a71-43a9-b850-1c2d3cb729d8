<template>
    <view style="width: 90vw" :style="{ height: `${rectHeight}vh` }" v-show="showCanvas">
        <nut-empty v-if="isFetchError" description="Error" />
        <EChart v-else ref="canvas" style="width: 100%; height: 100%" />
    </view>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import Taro from '@tarojs/taro'
import EChart from './echarts/index'
import { useDidShow } from '@/hooks/component/hooks'
import { t } from '@/locale/fanyi'
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { DeviceStatusHistoryDatasDto, GetDeviceDataInput } from '@/api/proxy/data-view/models'
import { HideLoading, Loading, Toast } from '@/utils/Taro-api'
import dayjs from 'dayjs'

export interface DeviceUseRate {
    deviceName?: string
    powerOnRate: number
    utilizationRate: number
    spareRate: number
    failureRate: number
    time?: string
}

const props = defineProps({
    datePicker: { type: String, required: true },
    isDevice: { type: Boolean, default: false },
    deviceId: { type: String, default: null },
    factoryModelId: { type: String, default: null },
})

const { graphic } = require('./echarts/echarts.min')
const canvas = ref<any>(null)
const chartOption = ref<any>()
const datavDataFlowService = new DatavDataFlowService()
const showCanvas = ref<boolean>(true)
const isFetchError = ref<boolean>(false)
const rectHeight = computed(() => {
    if (props.deviceId !== null) {
        return 30
    }
    return 50
})

/**
 * 自定义的图形元素渲染逻辑
 *
 * @param params 包含了当前数据信息（如 seriesIndex、dataIndex 等等）和坐标系的信息（如坐标系包围盒的位置和尺寸）
 * @param api 是一些开发者可调用的方法集合（如 api.value()、api.coord()）
 */
const renderItem = (
    params: { coordSys: { x: any; y: any; width: any; height: any } },
    api: {
        value: (arg0: number) => any
        coord: (arg0: any[]) => any
        size: (arg0: number[]) => number[]
        style: () => any
    },
) => {
    // 对于 data 中的每个 dataItem，都会调用这个 renderItem 函数。
    // （但是注意，并不一定是按照 data 的顺序调用）

    // 这里进行一些处理，例如，坐标转换。
    // 这里使用 api.value(0) 取出当前 dataItem 中第一个维度的数值。
    const categoryIndex = api.value(0)
    // 这里使用 api.coord(...) 将数值在当前坐标系中转换成为屏幕上的点的像素值。
    const start = api.coord([api.value(1), categoryIndex])
    const end = api.coord([api.value(2), categoryIndex])
    // 这里使用 api.size(...) 获得 Y 轴上数值范围为 1 的一段所对应的像素长度。
    const height = api.size([0, 1])[1] * 0.6

    // shape 属性描述了这个矩形的像素位置和大小。
    // 其中特殊得用到了 echarts.graphic.clipRectByRect，意思是，
    // 如果矩形超出了当前坐标系的包围盒，则剪裁这个矩形。
    // 如果矩形完全被剪掉，会返回 undefined.
    const rectShape = graphic.clipRectByRect(
        {
            // 矩形的位置和大小。
            x: start[0],
            y: start[1] - height / 2,
            width: Math.min((end[0] - start[0]) * 1.2, params.coordSys.width - start[0]), // 修正宽度，确保不超出边界
            height,
        },
        {
            // 当前坐标系的包围盒。
            x: params.coordSys.x,
            y: params.coordSys.y,
            width: params.coordSys.width,
            height: params.coordSys.height,
        },
    )
    // 这里返回为这个 dataItem 构建的图形元素定义。
    return (
        rectShape && {
            // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
            type: 'rect',
            shape: rectShape,
            // 用 api.style(...) 得到默认的样式设置。这个样式设置包含了
            // option 中 itemStyle 的配置和视觉映射得到的颜色。
            style: api.style(),
        }
    )
}

const buildOption = (value: DeviceStatusHistoryDatasDto) => {
    let datas = value.items
    let itemNames = value.names

    //   if (canvas.value) {
    //     // 清除图表缓存数据
    //     canvas.value.clear()
    //   }

    const option = {
        // 标题
        title: {
            text: t('IotService.WorkMode'),
            left: 'center',
            textStyle: {
                color: '#333',
                fontStyle: 'normal',
                fontWeight: 'bolder',
                fontFamily: 'sans-serif',
                fontSize: 24,
                lineHeight: 30,
            },
        },
        // 图例组件
        legend: {
            top: 'auto',
            left: 0,
            right: 0,
            bottom: 'auto',
            width: 'auto',
            height: value.items.length * 100,
            padding: 5,
        },
        // 鼠标提示信息
        tooltip: {
            position: function (point) {
                // 将提示框放在图表上方，避开触摸区域
                return [point[0], point[1] - 120];
            },
            alwaysShowContent: false,
            hideDelay: 5000,
            confine: true,  // 限制在图表区域内
            enterable: false, // 不允许鼠标进入提示框
            axisPointer: {
                type: 'cross',
            },
            formatter(params: { marker: any; data: { status: any; timeSpan: any; value: any } }) {
                const tip = `${params.marker}${t(`IotService.Mode${params.data.status}`)}: ${params.data.timeSpan}
        ${params.marker}${t('Helper.StartTime')}: ${dayjs(params.data.value[1]).format('YYYY-MM-DD HH:mm:ss')}
        ${params.marker}${t('Helper.EndTime')}: ${dayjs(params.data.value[2]).format('YYYY-MM-DD HH:mm:ss')}`
                return tip
            },
            trigger: 'item',
            triggerOn: 'mousemove|click',  // 同时支持鼠标和触摸事件
        },
        axisPointer: {
            link: { yAxisIndex: 'all' },
            label: {
                backgroundColor: '#777',
            },
        },

        grid: {
            // height: 300
            left: 70, //相当padding值
            right: 30, //相当padding值
            width: 'auto',
            height: 'auto',
        },
        xAxis: {
            min: value.startTime, //'dataMin',
            max: value.endTime, //'dataMax',
            scale: true,
            axisLabel: {
                rotate: 25,
                formatter: (val: number) => {
                    const time = Math.max(0, val)
                    const res = dayjs(time).format(`MM${t('Helper.Month')}DD${t('Helper.Date')} HH:mm:ss`)
                    return res
                },
            },
            // 鼠标X方向提示
            axisPointer: {
                label: {
                    formatter: (params: { value: any }) => {
                        return `${t('Helper.Time')}：${dayjs(params.value).format('YYYY-MM-DD HH:mm:ss')}`
                    },
                },
            },
        },
        // 鼠标Y方向提示
        yAxis: {
            data: itemNames,
            axisLabel: {
                formatter: function (value) {
                    // 这里我们手动控制换行，可以每8个字符换一行
                    if (value.length > 16) {
                        return value.slice(0, 8) + '\n' + value.slice(8, 16) + '\n' + value.slice(16)
                    } else if (value.length > 8) {
                        return value.slice(0, 8) + '\n' + value.slice(8)
                    } else {
                        return value
                    }
                },
                lineHeight: 12
            },
            axisPointer: {
                label: {
                    formatter: (params: { value: any }) => {
                        return `${t('Helper.Device')}：${params.value}`
                    },
                },
            },
        },
        series: [
            {
                type: 'custom',
                renderItem,
                itemStyle: {
                    opacity: 0.8,
                },
                encode: {
                    x: [1, 2], // data 中『维度1』和『维度2』对应到 X 轴
                    y: 0, // data 中『维度0』对应到 Y 轴
                    tooltip: [2, 3], // 表示『维度2』和『维度3』要显示到 tooltip 中。
                },
                data: datas,
            },
        ],
    }
    return option
}

/**
 * @description 获取列表
 */
const fetchData = async () => {
    try {
        Loading()
        const input: GetDeviceDataInput = {
            deviceId: props.deviceId,
            factoryModelId: props.factoryModelId,
            dateTime: props.datePicker,
        }
        const result = await datavDataFlowService.getDeviceHistoryModeByInput(input)
        chartOption.value = buildOption(result)
        HideLoading()
    } catch (e) {
        console.error(e)
        isFetchError.value = true
        Toast(t('ui.requestFailed'), { icon: 'error' })
    }
}

/**
 * @description 渲染图表
 */
const renderChart = () => {
    const echartComponentInstance: any = canvas.value // 组件实例
    Taro.nextTick(() => {
        if (!echartComponentInstance) return
        // 初始化图表
        echartComponentInstance.refresh(chartOption.value).then(myChart => {
            myChart.showLoading()
            /** 异步更新图表数据 */
            setInterval(() => {
                myChart.setOption(chartOption.value) // myChart 即为 echarts 实例，可使用的实例方法，具体可参考 echarts 官网
                myChart.hideLoading()
            }, 1000)
        })
    })
}

watch(
    () => props.datePicker,
    async () => {
        if (!props.datePicker) {
            return
        }
        await fetchData()
        renderChart()
    },
    { deep: true, immediate: true },
)

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {})
</script>
