import { defineStore } from 'pinia'

export interface MaterialState {
    currentMaterialId: string | null
    currentMeasureUnitId: string | null
    // 如果有更多参数，可以在这里添加
}

// 定义一个名为 'material' 的 store
export const useMaterialStore = defineStore('material', {
    state: (): MaterialState => ({
        currentMaterialId: null,
        currentMeasureUnitId: null,
    }),

    actions: {
        // 设置当前物料ID
        setCurrentMaterialId(id: string) {
            this.currentMaterialId = id
        },

        // 设置当前单位ID
        setCurrentMeasureUnitId(id: string) {
            this.currentMeasureUnitId = id
        },

        // 同时设置多个参数
        setMaterialDetails(materialId: string, measureUnitId?: string) {
            this.currentMaterialId = materialId
            if (measureUnitId) {
                this.currentMeasureUnitId = measureUnitId
            }
        },

        // 清除当前物料详情数据
        clearMaterialDetails() {
            this.currentMaterialId = null
            this.currentMeasureUnitId = null
        },
    },

    getters: {
        // 获取当前物料ID
        getMaterialId: state => state.currentMaterialId,

        // 获取当前单位ID
        getMeasureUnitId: state => state.currentMeasureUnitId,
    },
})
