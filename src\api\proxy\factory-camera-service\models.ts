import { EntityDto } from '@/shared/models/dtos'
import { PagedSortedAndFilteredInputDto } from '../factory-device-service/models'

export interface GetFactoryCameraInput extends PagedSortedAndFilteredInputDto {
    factoryParentId?: string
    deviceId?: string
    sorting?: string
}

export interface FactoryCameraListDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    videoCoding: CameraVideoCoding
    liveUrl?: string
    liveHls?: string
    liveRtmp?: string
    liveRtsp?: string
    factoryParentId?: string
    factoryParentName?: string
    factoryDeviceId?: string
    factoryDeviceName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export enum CameraVideoCoding {
    Http = 'Http',
    Hls = 'Hls',
    Rtmp = 'Rtmp',
    Rtsp = 'Rtsp',
}
