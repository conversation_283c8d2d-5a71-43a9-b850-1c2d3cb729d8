<template>
    <view class="workbench-container">
        <view class="content-wrapper">
            <template v-if="menuList.length > 0">
                <BaseCard v-for="menus in menuList" :key="menus.text" :title="menus.text" class="menu-card">
                    <nut-grid :column-num="4">
                        <nut-grid-item
                            v-for="item in getVisibleChildren(menus)"
                            :key="item.text"
                            :text="item.text"
                            @click="NavigateTo(item.link ?? '')"
                            class="grid-item"
                        >
                            <IconFont
                                :name="typeof item.icon === 'string' ? item.icon : item.icon?.value"
                                :size="32"
                                class="grid-icon"
                            />
                        </nut-grid-item>
                    </nut-grid>
                </BaseCard>
            </template>
            <template v-else>
                <view class="empty-state">
                    <nut-empty :description="t('text.noMenu')" image="empty"></nut-empty>
                </view>
            </template>
        </view>
    </view>
</template>

<script lang="ts" setup>
import '@/components/BaseCard.vue'
import { useDidShow, usePullDownRefresh } from '@/hooks/component/hooks'
import { Menu } from '@/hooks/web/menu/interface'
import { useMenuService } from '@/hooks/web/menu/useMenuService'
import { processMenuVisibility } from '@/hooks/web/menu/menuFilter'
import { IconFont } from '@nutui/icons-vue-taro'
import Taro from '@tarojs/taro'
import { ref, watch } from 'vue'
import menus from '@/hooks/web/menu/workbench/menus.workbench'
import { NavigateTo } from '@/utils/Taro-api'
import { t } from '@/locale/fanyi'
import { usePermissionStoreWithOut } from '@/stores/modules/permission'

/**
 * @description 设置页面标题
 */
Taro.setNavigationBarTitle({ title: t('menu.dashboard.workplace') })

const menuList = ref<Menu[]>([])
const permissionStore = usePermissionStoreWithOut()

/**
 * 获取可见的子菜单
 * @param menu 父菜单
 * @returns 可见的子菜单列表
 */
const getVisibleChildren = (menu: Menu) => {
    if (!menu.children) return []
    return menu.children.filter(item => item.visible !== false)
}

/**
 * @description 获取菜单
 */
const getMenu = () => {
    try {
        const menuService = useMenuService()
        menuService.add(menus.menu)
        // 获取经过菜单服务处理后的菜单数据
        const originalMenus = (menuService.menus[0]?.children as Menu[]) || []
        // 处理菜单可见性而不是过滤掉不可见的菜单
        menuList.value = processMenuVisibility(originalMenus)
        // 检查是否有可见的子菜单项
        const hasVisibleItems = menuList.value.some(
            menu => menu.children && menu.children.some(item => item.visible !== false),
        )
        if (!hasVisibleItems) {
            menuList.value = [] // 如果没有可见子项，清空菜单列表触发empty状态
        }
    } catch (error) {
        menuList.value = [] // 确保菜单列表是数组
    }
}

/**
 * @description 监听权限变更，自动重新获取菜单
 */
watch(
    () => permissionStore.getGrantedPolicies,
    () => {
        // 如果menuList已经存在，先强制重新计算所有菜单的可见性
        if (menuList.value.length > 0) {
            processMenuVisibility(menuList.value)
            // 手动触发视图更新 (Vue的技巧 - 通过替换引用迫使Vue重新渲染)
            menuList.value = [...menuList.value]
            // 检查是否有可见的子菜单项
            const hasVisibleItems = menuList.value.some(
                menu => menu.children && menu.children.some(item => item.visible !== false),
            )
            if (!hasVisibleItems) {
                menuList.value = [] // 如果没有可见子项，清空菜单列表触发empty状态
            }
        } else {
            // 如果menuList为空，则重新获取菜单
            getMenu()
        }
    },
    { deep: true },
)

/**
 * @description 下拉刷新事件
 */
usePullDownRefresh(() => {})

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    // 获取菜单
    getMenu()
})
</script>

<style lang="scss">
.workbench-container {
    min-height: 100vh;
    background: #f7f8fa;
    padding: 12px 0;

    .content-wrapper {
        padding: 0 12px;

        .menu-card {
            margin-bottom: 12px;
            border-radius: 12px;
            background: #fff;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

            .grid-item {
                padding: 16px 8px;
                transition: background-color 0.2s;

                .grid-icon {
                    color: #426543;
                    margin-bottom: 6px;
                }

                :deep(.nut-grid-item__text) {
                    font-size: 13px;
                    color: #333;
                }

                &:active {
                    background-color: rgba(0, 0, 0, 0.02);
                }
            }
        }
    }
}

.icon {
    width: 32px;
    height: 32px;
}
</style>
