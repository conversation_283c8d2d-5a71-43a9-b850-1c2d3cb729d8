/**
 * Global authority directive
 * Used for fine-grained control of component permissions
 * @Example v-auth="RoleEnum.TEST"
 */
// import { usePermission } from '@/hooks/web/acl/usePermission';
import { hasPermission, hasRole } from '@/hooks/web/acl/permission'
import { hasAllPermissions } from '@/hooks/web/acl/permissionGuard'
import type { App, DirectiveBinding } from 'vue'

/**
 * 移出没有权限的元素
 * @param el
 * @param result
 * @returns
 */
function removeChild(el: Element, result: boolean) {
    if (!result) {
        const parentNode = el.parentNode
        if (parentNode) {
            el.remove()
        }
    }
}

/**
 * 根据角色判断元素是否移除
 * @param el
 * @param binding
 * @returns
 */
function isRoleAuth(el: Element, binding: any) {
    const value = binding.value
    const result = hasRole(value)
    removeChild(el, result)
}

/**
 * 根据权限判断元素是否移除
 * @param el
 * @param binding
 * @returns
 */
function isPermissionAuth(el: Element, binding: any) {
    const value = binding.value
    const result = hasPermission(value)
    removeChild(el, result)
}

/**
 * 必须同时拥有所有指定权限才显示元素
 * @param el
 * @param binding
 * @returns
 */
function isAllPermissionsAuth(el: Element, binding: any) {
    const value = binding.value
    const result = hasAllPermissions(value)
    removeChild(el, result)
}

export function setupPermissionDirective(app: App) {
    // 判断角色
    app.directive('role', {
        mounted: (el: Element, binding: DirectiveBinding<any>) => {
            isRoleAuth(el, binding)
        },
    })

    // 判断权限（任一满足即可）
    app.directive('permission', {
        mounted: (el: Element, binding: DirectiveBinding<any>) => {
            isPermissionAuth(el, binding)
        },
    })

    // 判断权限（必须同时满足所有权限）
    app.directive('all-permission', {
        mounted: (el: Element, binding: DirectiveBinding<any>) => {
            isAllPermissionsAuth(el, binding)
        },
    })
}
