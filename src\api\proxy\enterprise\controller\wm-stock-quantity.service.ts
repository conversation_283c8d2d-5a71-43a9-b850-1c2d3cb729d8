import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { GetForEditInput } from '../../helper/shared/models'
import type {
    GetWmStockQuantityForEditorOutput,
    GetWmStockQuantityInput,
    WmStockQuantityCreateDto,
    WmStockQuantityDetailDto,
    WmStockQuantityEditDto,
    WmStockQuantityListDto,
} from '../wms/dtos/models'
import type { Statistics } from '../wms/models'

export class WmStockQuantityService {
    apiName = 'EnterpriseService'

    create = (input: WmStockQuantityCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-stock-quantity',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-stock-quantity/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmStockQuantityDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-stock-quantity/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmStockQuantityDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-stock-quantity/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmStockQuantityForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-stock-quantity/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getPaged = (input: GetWmStockQuantityInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmStockQuantityListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-stock-quantity',
                params: {
                    materialId: input.materialId,
                    categoryId: input.categoryId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-stock-quantity/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    statistics = (materialId: string, options?: RequestOptions) =>
        defHttp.request<Statistics>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-stock-quantity/statistics',
                params: {
                    materialId,
                },
            },
            options,
        )

    update = (input: WmStockQuantityEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-stock-quantity',
                data: input,
            },
            options,
        )
}
