import type { GetForEditInput } from '../../helper/shared/models'
import type {
    GetWmOutWarehouseBillForEditorOutput,
    GetWmOutWarehouseBillInput,
    WmOutWarehouseBillCloneDto,
    WmOutWarehouseBillCreateDto,
    WmOutWarehouseBillDetailDto,
    WmOutWarehouseBillEditDto,
    WmOutWarehouseBillListDto,
    WmOutWarehouseBillUpdateStatusDto,
} from '../wms/dtos/models'
import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class WmOutWarehouseBillService {
    apiName = 'EnterpriseService'

    clone = (dto: WmOutWarehouseBillCloneDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-out-warehouse-bill/CloneOutWarehouseBill',
                data: dto,
            },
            options,
        )

    create = (input: WmOutWarehouseBillCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-out-warehouse-bill',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-out-warehouse-bill/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmOutWarehouseBillDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-out-warehouse-bill/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmOutWarehouseBillDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-out-warehouse-bill/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmOutWarehouseBillForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-out-warehouse-bill/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getPaged = (input: GetWmOutWarehouseBillInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmOutWarehouseBillListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-out-warehouse-bill',
                params: {
                    outboundType: input.outboundType,
                    auditStatus: input.auditStatus,
                    applicantUserId: input.applicantUserId,
                    reviewerUserId: input.reviewerUserId,
                    executorUserId: input.executorUserId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    update = (input: WmOutWarehouseBillEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-out-warehouse-bill',
                data: input,
            },
            options,
        )

    updateStatus = (updateStatusDao: WmOutWarehouseBillUpdateStatusDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-out-warehouse-bill/updateStatus',
                data: updateStatusDao,
            },
            options,
        )
}
