{"home": "Home", "menu.main": "Main Navigation", "menu.dashboard": "Dashboard", "menu.dashboard.analysis": "Analysis", "menu.dashboard.monitor": "Monitor", "menu.dashboard.workplace": "Workplace", "menu.dashboard.dd": "Custom", "menu.shortcut": "Shortcut Menu", "menu.home": "Home", "menu.workbench": "Workbench", "menu.my": "My", "menu.deviceList": "Device List", "menu.gateway": "IoT Gateway", "menu.productionKanban": "Production Board", "menu.deviceKanban": "Device Board", "menu.deviceMaintenance": "Device Maintenance", "menu.productionDaily": "Production Daily", "menu.shortcutMenu": "Shortcut Menu", "menu.equipmentIoT": "Equipment IoT", "menu.productionReport": "Production Report", "menu.warehouseManagement": "Warehouse Management", "menu.materialManagement": "Material Management", "menu.purchaseManagement": "Purchase Management", "menu.stockManagement": "Stock Management", "menu.inWarehouseManagement": "In-Warehouse Management", "menu.outWarehouseManagement": "Out-Warehouse Management", "menu.bomTable": "BOM Table", "menu.materialDetail": "Material Detail", "menu.warehouseSetup": "Warehouse Setup", "menu.more": "More", "menu.edit": "Edit", "menu.recycle": "Recycle", "menu.delete": "Delete", "menu.toolMenu": "<PERSON><PERSON>", "menu.scanCode": "Scan Code", "menu.routeJump": "Route Jump", "menu.warehouseDetails": "Warehouse Details", "menu.myDevice": "My Device", "menu.iotDevice": "IoT Device", "menu.remoteAssistance": "Remote Assistance", "menu.deviceInfo": "Device Info", "menu.liveStatus": "Live Status", "menu.alarmList": "Alarm List", "menu.diagnosticInfo": "Diagnostic Info", "menu.deviceParameters": "Device Parameters", "menu.deviceMonitoring": "Device Monitoring", "menu.deviceImage": "Device Image", "menu.warehouseArea": "Warehouse Area", "menu.warehouseAreaDetails": "Warehouse Area Details", "menu.warehouseLocation": "Warehouse Location", "menu.warehouseLocationDetails": "Warehouse Location Details", "menu.addWarehouseLocation": "Add Warehouse Location", "ui.startup": "Startup", "ui.personal": "Personal Information", "ui.language": "Language", "ui.aboutUs": "About Us", "ui.setting": "Settings", "ui.loading": "Loading...", "ui.submit": "Submit", "ui.confirm": "Confirm", "ui.cancel": "Cancel", "ui.username": "Username", "ui.usernameTooLong": "Username cannot exceed 32 characters", "ui.surname": "Surname", "ui.surnameTooLong": "Surname cannot exceed 32 characters", "ui.name": "Name", "ui.nameTooLong": "Name cannot exceed 32 characters", "ui.email": "Email", "ui.emailTooLong": "Email cannot exceed 128 characters", "ui.deviceDetails": "<PERSON>ce Det<PERSON>", "ui.gatewayDetails": "Gateway Details", "ui.noMore": "No More...", "ui.factory": "Factory", "ui.totalDevice": "Total Devices", "ui.runDevice": "Running Devices", "ui.stopDevice": "Stopped Devices", "ui.alarmDevice": "Alarmed Devices", "ui.factoryName": "Factory Name", "ui.dataView": "Data View", "ui.productionBarChart": "Production Bar Chart", "ui.qualifiedRate": "Qualification Rate", "ui.dataDeadline": "Data Deadline", "ui.connectGateway": "Connect Gateway", "ui.connectFailed": "Connection Failed", "ui.requestFailed": "Request Failed", "ui.contactPhone": "Contact Phone", "ui.phoneNumberTooLong": "Phone number cannot exceed 11 digits", "ui.inputUsername": "Please Enter Username", "ui.inputSurname": "Please Enter Surname", "ui.inputName": "Please Enter Name", "ui.inputEmail": "Please Enter Email Address", "ui.inputPhone": "Please Enter Contact Phone", "ui.inputCorrectEmail": "Please Enter Correct Email", "ui.inputCorrectPhone": "Please enter a valid phone number", "ui.selectLanguage": "Select Language", "ui.reloadApp": "System language changed, reload the mini program?", "ui.notLogin": "Not Logged In", "ui.logout": "Logout", "ui.logoutConfirm": "Confirm to logout?", "ui.industrialCloud": "Industrial Cloud Service Platform", "ui.viewImage": "View Image", "ui.ioTSubDevices": "IoT Sub-Devices", "ui.system": "System Settings", "ui.startTime": "Start Time", "ui.endTime": "End Time", "ui.timeSelect": "Time Select", "ui.clearSelect": "Clear Select", "ui.refresh": "Refresh", "ui.search": "Search", "ui.autoRefresh": "Auto Refresh", "ui.filtrate": "Filtrate", "ui.clearFiltrate": "Clear Filtrate", "ui.reset": "Reset", "ui.redirecting": "Redirecting", "ui.retry": "Retry", "ui.resetCategory": "Reset Category", "ui.deviceInfo": "Device Info", "ui.redirectingDescription": "Redirecting to target page...", "ui.featureDeveloping": "Feature under development", "ui.pageJumpFailed": "Page jump failed", "ui.viewDeviceTask": "View Device Task", "ui.productionReport": "Production Report", "ui.remoteAssist": "Remote Assist", "ui.add": "Add", "ui.more": "More", "text.contactUs": "Contact Us", "text.officialWebsite": "Official Website", "text.address": "Address", "text.email": "Email", "text.partsProductionGroup": "Parts Production Group", "text.creationTime": "Creation Time", "text.operate": "Operate", "text.add": "Add", "text.merge": "<PERSON><PERSON>", "text.detail": "Detail", "text.name": "Name", "text.encode": "Encode", "text.describe": "Describe", "text.location": "Location", "text.areaSize": "Area Size", "text.productionManager": "Production Manager", "text.pleaseEnterName": "Please Enter Name", "text.pleaseEnterCode": "Please Enter Code", "text.pleaseEnterDescribe": "Please Enter Describe", "text.pleaseEnterLocation": "Please Enter Location", "text.pleaseEnterAreaSize": "Please Enter Area Size", "text.pleaseSelectProductionManager": "Please Select Production Manager", "text.isEnable": "Is Enable", "text.picture": "Picture", "text.errorRequest": "Error request Please contact administrator", "text.warmReminder": "<PERSON> Reminder", "text.isDelete": "Do you want to delete it?", "text.deleteSuccess": "Delete Success", "text.mergeSuccess": "<PERSON><PERSON>", "text.mergeFailed": "Merge Failed", "text.selectAtLeastTwo": "Please select at least two to merge", "text.confirmMerge": "Are you sure to merge the selected items? This operation cannot be reversed", "text.materialName": "Material Name", "text.materialCode": "Material Code", "text.materialmodel": "Material Model", "text.materialSpecification": "Material Specification", "text.materialtype": "Material Type", "text.safetyStock": "Safety Stock", "text.minStock": "<PERSON>", "text.maxStock": "<PERSON>", "text.highValue": "High Value", "text.materialsAuditStatus": "Materials Audit Status", "text.measureUnitName": "Measure Unit Name", "text.supplierName": "Supplier Name", "text.selectionSort": "Selection Sort", "text.pleaseSelectSort": "Please Select Sort", "text.stock": "Stock", "text.pleaseEnterMaterialName": "Please Enter Material Name", "text.pleaseEnterMaterialCode": "Please Enter Material Code", "text.pleaseEnterMaterialModel": "Please Enter Material Model", "text.pleaseEnterMaterialSpecification": "Please Enter Material Specification", "text.pleaseEnterMaterialType": "Please Enter Material Type", "text.pleaseEnterSafetyStock": "Please Enter Safety Stock", "text.pleaseEnterMinStock": "Please Enter <PERSON>", "text.pleaseEnterMaxStock": "Please Enter <PERSON>", "text.pleaseEnterHighValue": "Please Enter High Value", "text.pleaseEnterDescription": "Please Enter Description", "text.pleaseSelectMaterialType": "Please Select Material Type", "text.pleaseSelectMaterialSort": "Please Select Material Sort", "text.pleaseSelectMeasureUnitName": "Please Select Measure Unit Name", "text.pleaseSelectSupplierName": "Please Select Supplier Name", "text.pleaseEnter": "Please Enter", "text.recover": "Recover", "text.isRecover": "Are you sure to recover?", "text.storageType": "Storage Type", "text.auditStatus": "Audit Status", "text.storageTime": "Storage Time", "text.applicantUserName": "Applicant", "text.reviewerUserName": "Reviewer", "text.executorUserName": "Executor", "text.actions": "Actions", "text.detailed": "Detailed", "text.edit": "Edit", "text.clone": "<PERSON><PERSON>", "text.deleteConfirm": "Confirm Delete?", "text.cloneConfirm": "Confirm Clone?", "text.cloneSuccess": "<PERSON>lone Success", "text.lastModificationTime": "Last Modification Time", "text.warehouseName": "Warehouse Name", "text.warehouseCode": "Warehouse Code", "text.warehouseLocation": "Warehouse Location", "text.warehouseAreaSize": "Warehouse Area Size", "text.warehouseDutyUser": "Warehouse Duty User", "text.baseInfo": "Base Info", "text.outWarehouseItemDetails": "Out Warehouse Item Details", "text.backToList": "Back To List", "text.materailCategory": "Material Category", "text.costPrice": "Cost Price", "text.stockStatus": "Stock Status", "text.batchCode": "Batch Code", "text.expireDate": "Expire Date", "text.isFrozen": "Is Frozen", "text.isNotFrozen": "Not Frozen", "text.isActivate": "Is Activate", "text.locationName": "Location Name", "text.categoryName": "Category Name", "text.unName": "UnNamed", "text.stockDetails": "Stock Details", "text.statisticInfo": "Statistic Info", "text.safeStock": "Safe Stock", "text.currentStock": "Current Stock", "text.averageStock": "Average Stock", "text.stockQuantityChange": "Stock Quantity Change", "text.supplierInfo": "Supplier Info", "text.noSupplierData": "No Supplier Data", "text.noMaterialFlowLogData": "No Material Flow Log Data", "text.materialFlowLog": "Material Flow Log", "text.noMenu": "No Menu", "text.deviceName": "Device Name", "text.deviceForm": "Device Form", "text.deviceType": "Device Type", "text.status": "Status", "text.isComplete": "Completed", "text.isNotComplete": "Not Completed", "text.creatorName": "Creator Name", "text.assistanceComplete": "Assistance Complete", "text.operationSuccess": "Operation Success", "text.factoryModel": "Factory Model", "text.produceDutyUserName": "Produce Duty User Name", "text.maintainDutyUserName": "Maintain Duty User Name", "text.iotDevice": "IoT Device", "text.navigateToFail": "Page jump failed", "text.confirmAssistanceComplete": "Confirm Assistance Complete?", "text.warehouseInfo": "Warehouse Info", "text.warehouseArea": "Warehouse Area", "text.dutyUserName": "Produce Duty User Name", "text.notYet": "Not Yet", "text.maxLoad": "<PERSON>", "text.positionX": "X Axis", "text.positionY": "Y Axis", "text.positionZ": "Z Axis", "text.dutyUser": "Produce Duty User", "text.submitSuccess": "Submit Success", "text.deleteFail": "Delete Failed", "text.warehouseLocationName": "Warehouse Location Name", "text.pleaseEnterWarehouseLocationName": "Please Enter Warehouse Location Name", "text.pleaseEnterEncode": "Please Enter Encode", "text.position": "Position", "text.pleaseSelectDutyUser": "Please Select Duty User", "WeChat.LoadingWithThreeDot": "Loading...", "WeChat.Search": "Search", "IotService.Gateway": "Gateway", "IotService.Device": "<PERSON><PERSON>", "IotService.None": "None", "IotService.PropertyName": "Property Name", "IotService.PropertyValue": "Property Value", "tag.SingleDevice": "Single Device", "tag.Machine": "Machine", "tag.true": "True", "tag.false": "False", "tag.enable": "Enable", "tag.disable": "Disable", "helper.noData": "No Data", "IotService": {"DeviceName": "Device Name", "DeviceId": "Device ID", "NodeId": "Node ID", "NodeType": "Node Type", "ProductName": "Product Name", "FwVersion": "Firmware Version", "SwVersion": "Software Version", "Status": "Status", "CreationTime": "Creation Time", "IotDevice": "IoT Device", "SubDevice": "Sub Device", "DeviceImages": "Device Images", "IotDeviceInfo": "IoT Device Info", "DeviceForm": "Device Form", "DeviceType": "Device Type", "IotStatus": "IoT Status", "WorkMode": "Work Mode", "DeviceAlarmList": "Device Alarm List", "AlarmLevel": "Alarm Level", "ONLINE": "Online", "OFFLINE": "Offline", "UNKNOWN": "Unknown", "INACTIVE": "Inactive", "ABNORMAL": "Abnormal", "FROZEN": "Frozen", "None": "None", "Device": "<PERSON><PERSON>", "Gateway": "Gateway"}, "Helper": {"ActiveTime": "Active Time", "Status": "Status", "UseStatus": "Use Status", "Time": "Time"}}