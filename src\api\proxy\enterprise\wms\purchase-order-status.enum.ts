import { mapEnumToOptions } from '@/utils/form-utils'

export enum PurchaseOrderStatus {
    Newly = 0,
    PendingApproval = 1,
    Approved = 2,
    Purchasing = 3,
    Ordered = 4,
    Rejected = 5,
    Cancelled = 6,
    Completed = 7,
    Returned = 8,

    // 组合状态枚举
    // 包含 通过|退回修改|拒绝
    ApprovedAndApprovalReturnedAndRejected = 100,
    // 包含 通过|退回修改
    ApprovedAndApprovalReturned,
    // 包含 通过|拒绝
    ApprovedAndRejected,
    // 包含 退回修改|拒绝
    ApprovalReturnedAndRejected,
    // 包含 下单|取消
    OrderedAndCancelled,
    // 包含 完成|退回
    CompletedAndReturned,
}

export const purchaseOrderStatusOptions = mapEnumToOptions(PurchaseOrderStatus)
