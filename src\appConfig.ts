import { TabBar } from '@tarojs/taro'

export const appConfig = {
    pages: ['pages/home/<USER>', 'pages/workbench/index', 'pages/profile/index'],
    window: {
        backgroundTextStyle: 'light',
        navigationBarBackgroundColor: '#fff',
        navigationBarTitleText: 'WeChat',
        navigationBarTextStyle: 'black',
    },
    // 随着业务代码和组件的引入越来越多，主包的大小一定会越来越大，超过 2m 的主包以后微信开发工具就无法使用预览的功能，为了提前做好准备在一开始就进行分包处理，主包只包含组件和公共代码，分包里放入业务代码
    subpackages: [
        {
            root: 'pages/subpackages/sys-setting',
            pages: ['index'],
        },
        {
            root: 'pages/subpackages/login',
            pages: ['index'],
        },
        {
            root: 'pages/subpackages/wechat-login',
            pages: ['index'],
        },
        {
            root: 'pages/workbench/device-list',
            pages: ['index', 'device-details/index'],
        },
        {
            root: 'pages/workbench/my-device',
            pages: ['index', 'device-details/index'],
        },
        {
            root: 'pages/workbench/device-board',
            pages: ['index'],
        },
        {
            root: 'pages/workbench/production-board',
            pages: ['index'],
        },
        {
            root: 'pages/workbench/device-iot/',
            pages: ['index', 'component/iot-device-details', 'component/gateway-web'],
        },
        // {
        //   root: 'pages/workbench/factory-camera/',
        //   pages: ['index']
        // },
        {
            root: 'pages/workbench/device-maintain/',
            pages: ['index', 'component/device-maintain-details'],
        },
        {
            root: 'pages/workbench/production-daily/',
            pages: ['index'],
        },
        {
            root: 'pages/workbench/warehouse-management/',
            pages: [
                'index',
                'component/warehouse-details-table',
                'component/warehouse-area-details',
                'component/warehouse-location-details',
            ],
        },
        {
            root: 'pages/subpackages/material-management/',
            pages: ['index', 'material-details/index'],
        },
        {
            root: 'pages/workbench/purchase-management/',
            pages: ['index', 'purchase-details/index'],
        },
        {
            root: 'pages/workbench/stock-management/',
            pages: ['index', 'stock-details/index'],
        },
        {
            root: 'pages/subpackages/in-warehouse-management/',
            pages: ['index', 'in-warehouse-details/index'],
        },
        {
            root: 'pages/subpackages/out-warehouse-management/',
            pages: ['index', 'out-warehouse-details/index'],
        },
        {
            root: 'pages/workbench/bom-table/',
            pages: ['index'],
        },
        {
            root: 'pages/route',
            pages: ['index'],
        },
        {
            root: 'pages/subpackages/remote-assistance/',
            pages: ['index', 'components/index'],
        },
    ],
    tabBar: {
        color: '#11111',
        selectedColor: '#3399CC',
        backgroundColor: '#ffffff',
        borderStyle: 'black',
        list: [
            {
                /** 页面路径，必须在 pages 中先定义 */
                pagePath: 'pages/home/<USER>',
                /** tab 上按钮文字 */
                text: 'home',
                /** 图片路径，icon 大小限制为 40kb，建议尺寸为 81px * 81px，当 position 为 top 时，此参数无效，不支持网络图片 */
                iconPath: './assets/images/tabBar/home.png',
                /** 选中时的图片路径，icon 大小限制为 40kb，建议尺寸为 81px * 81px ，当 position 为 top 时，此参数无效 */
                selectedIconPath: './assets/images/tabBar/selectedHome.png',
            },
            {
                /** 页面路径，必须在 pages 中先定义 */
                pagePath: 'pages/workbench/index',
                /** tab 上按钮文字 */
                text: 'menu.workbench',
                /** 图片路径，icon 大小限制为 40kb，建议尺寸为 81px * 81px，当 position 为 top 时，此参数无效，不支持网络图片 */
                iconPath: './assets/images/tabBar/workbench.png',
                /** 选中时的图片路径，icon 大小限制为 40kb，建议尺寸为 81px * 81px ，当 position 为 top 时，此参数无效 */
                selectedIconPath: './assets/images/tabBar/selectedWorkbench.png',
            },
            {
                /** 页面路径，必须在 pages 中先定义 */
                pagePath: 'pages/profile/index',
                /** tab 上按钮文字 */
                text: 'menu.my',
                /** 图片路径，icon 大小限制为 40kb，建议尺寸为 81px * 81px，当 position 为 top 时，此参数无效，不支持网络图片 */
                iconPath: './assets/images/tabBar/about.png',
                /** 选中时的图片路径，icon 大小限制为 40kb，建议尺寸为 81px * 81px ，当 position 为 top 时，此参数无效 */
                selectedIconPath: './assets/images/tabBar/selectedAbout.png',
            },
        ],
    } as TabBar,
}
