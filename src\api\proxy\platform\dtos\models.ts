import type { PagedAndSortedResultRequestDto } from '../../../app/model/baseModel'
import type { EntityDto } from '../../../app/model/baseModel'

export interface PagedSortedAndFilteredInputDto extends PagedAndSortedResultRequestDto {
    filterText?: string
    sorting?: string
}

export interface GetRemoteAssistanceEditorInput {
    id?: string
}

export interface GetRemoteAssistanceForEditorOutput {
    remoteAssistance: RemoteAssistanceEditDto
}

export interface GetRemoteAssistanceInput extends PagedSortedAndFilteredInputDto {
    maxResultCount: any
    skipCount: any
    filterText: any
    isComplete?: boolean
    sorting?: string
}

export interface RemoteAssistanceCreateDto {
    factoryDeviceId?: string
    describe?: string
}

export interface RemoteAssistanceDetailDto extends EntityDto<string> {
    name?: string
    factoryDeviceId?: string
    factoryDeviceName?: string
    iotId?: string
    isComplete: boolean
    creatorId?: string
    creatorName?: string
    creationTime?: string
}

export interface RemoteAssistanceEditDto {
    id: string
    isComplete: boolean
}

export interface RemoteAssistanceListDto extends EntityDto<string> {
    name?: string
    factoryDeviceId?: string
    deviceName?: string
    deviceForm?: string
    deviceType?: string
    iotId?: string
    isComplete: boolean
    creatorId?: string
    creatorName?: string
    creationTime?: string
}
