<template>
    <view class="in-warehouse-details">
        <nut-tabs v-model="tabActive" title-active-color="#0066ff" title-inactive-color="#616161">
            <nut-tab-pane title="基本信息">
                <DetailsTable :detailsList="detailsList" />
            </nut-tab-pane>
            <nut-tab-pane title="入库子项">
                <view v-if="!showItemDetails">
                    <InWarehouseItemsTable
                        :inWarehouseBillId="inWarehouseBillId"
                        ref="itemsTableRef"
                        @showDetails="handleShowDetails"
                    />
                </view>
                <view v-else class="item-details-container">
                    <view class="details-header">
                        <nut-button size="small" type="primary" @click="showItemDetails = false"> 返回列表 </nut-button>
                        <text class="header-title">入库子项详情</text>
                    </view>
                    <InWarehouseItemDetails ref="itemDetailsRef" />
                </view>
            </nut-tab-pane>
        </nut-tabs>
    </view>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { ref, nextTick } from 'vue'
import { t } from '@/locale/fanyi'
import { WmInWarehouseBillService } from '@/api/proxy/enterprise/controller/wm-in-warehouse-bill.service'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { Toast, Loading, HideLoading } from '@/utils/Taro-api'
import * as DetailsTableModule from './component/in-warehouse-details-table.vue'
import * as InWarehouseItemsTableModule from './component/in-warehouse-items-table.vue'
import * as InWarehouseItemDetailsModule from './component/in-warehouse-item-details.vue'
import { useDidShow } from '@/hooks/component/hooks'

// 当前激活的选项卡
const tabActive = ref(0)

// 初始化服务
const wmInWarehouseBillService = new WmInWarehouseBillService()
const identityUserService = new IdentityUserService()

// 入库单ID
const inWarehouseBillId = ref('')

// 入库单详情
const detailsList = ref({})
const itemsTableRef = ref(null)
const itemDetailsRef = ref(null)

// 子项详情显示控制
const showItemDetails = ref(false)

// 入库类型映射
const storageTypeMap = {
    Ordinary: { type: 'default', text: '普通', color: '#909399' },
    Purchase: { type: 'success', text: '采购', color: '#67C23A' },
    FinishedProduct: { type: 'warning', text: '成品', color: '#E6A23C' },
    SemiFinishedProduct: { type: 'warning', text: '半成品', color: '#E6A23C' },
    RawMaterial: { type: 'primary', text: '原料', color: '#3460fa' },
    Return: { type: 'danger', text: '退货', color: '#F56C6C' },
}

// 审核状态映射
const auditStatusMap = {
    Newly: { type: 'default', text: '新建', color: '#909399' },
    PendingApproval: { type: 'warning', text: '待审批', color: '#E6A23C' },
    Approved: { type: 'success', text: '已审批', color: '#67C23A' },
    Rejected: { type: 'danger', text: '已拒绝', color: '#F56C6C' },
    Cancelled: { type: 'default', text: '已取消', color: '#909399' },
    Completed: { type: 'success', text: '已完成', color: '#67C23A' },
}

// 使用通用方式导入组件
const DetailsTable = DetailsTableModule.default || DetailsTableModule
const InWarehouseItemsTable = InWarehouseItemsTableModule.default || InWarehouseItemsTableModule
const InWarehouseItemDetails = InWarehouseItemDetailsModule.default || InWarehouseItemDetailsModule

/**
 * @description 获取用户信息
 */
const fetchUserInfo = async (data: any) => {
    try {
        if (data.executorUserId) {
            const executorInfo = await identityUserService.get(data.executorUserId)
            data.executorUserName = `${executorInfo.surname || ''}${executorInfo.name || ''}`
        }
        if (data.applicantUserId) {
            const applicantInfo = await identityUserService.get(data.applicantUserId)
            data.applicantUserName = `${applicantInfo.surname || ''}${applicantInfo.name || ''}`
        }
        if (data.reviewerUserId) {
            const reviewerInfo = await identityUserService.get(data.reviewerUserId)
            data.reviewerUserName = `${reviewerInfo.surname || ''}${reviewerInfo.name || ''}`
        }
        return data
    } catch (error) {
        console.error('获取用户信息失败:', error)
        return data
    }
}

/**
 * @description 获取入库单详情
 */
const fetchData = async () => {
    try {
        Loading()

        // 获取路由参数
        const id = Taro.getCurrentInstance()?.router?.params?.id

        if (!id) {
            console.error('ID不存在')
            Toast('参数错误', { icon: 'error' })
            return
        }

        inWarehouseBillId.value = id

        // 获取入库单详情
        const result = await wmInWarehouseBillService.get(id)
        if (result) {
            // 获取用户信息
            const enrichedData = await fetchUserInfo(result)
            detailsList.value = enrichedData
        }
    } catch (error) {
        console.error('获取入库单详情失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

/**
 * 处理显示入库子项详情
 */
const handleShowDetails = async (id: string) => {
    showItemDetails.value = true

    // 等待下一个渲染循环，确保组件已经渲染
    await nextTick()

    // 调用子组件的方法加载详情数据
    if (itemDetailsRef.value && typeof itemDetailsRef.value.fetchData === 'function') {
        itemDetailsRef.value.fetchData(id)
    }
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    fetchData()
})
</script>

<style lang="scss">
.in-warehouse-details {
    padding: 16px;
    background-color: #f5f5f5;
    min-height: 100vh;

    :deep(.nut-tabs) {
        .nut-tabs__titles {
            background-color: #fff;
            padding: 8px 0;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .nut-tabs__content {
            padding-top: 10px;
        }

        .nut-tab-pane {
            padding: 0;
        }

        .nut-tabs__titles-item {
            font-size: 15px;

            &.active {
                font-weight: 500;
            }
        }
    }
}

.item-details-container {
    background-color: #fff;

    .details-header {
        display: flex;
        align-items: center;
        padding: 10px 16px;
        border-bottom: 1px solid #f0f0f0;

        .header-title {
            margin-left: 20px;
            font-size: 16px;
            font-weight: 500;
        }
    }
}
</style>
