<template>
    <view class="index">
        <view>
            <img src="" alt="" />
        </view>
    </view>
</template>

<script>
import { reactive, toRefs } from 'vue'
import { useDidShow } from '../../../hooks/component/hookss'

export default {
    setup() {},
}
</script>

<style lang="scss">
.index {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
}
</style>
