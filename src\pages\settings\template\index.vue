<template>
  <view> </view>
</template>

<script lang="ts">
import { usePullDownRefresh } from '@tarojs/taro';
import { reactive, toRefs } from 'vue';
// import { useDidShow } from '@/hooks/component/hookss';

export default {
  components: {},
  setup() {
    //响应式变量
    const data = reactive({});

    // 页面显示钩子函数
    // useDidShow(() => {});

    /**
     * 下拉刷新事件
     */
    usePullDownRefresh(() => {});

    return { ...toRefs(data) };
  }
};
</script>

<style lang="scss" scoped></style>
