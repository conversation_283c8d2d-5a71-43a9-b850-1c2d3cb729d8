import { defineStore } from 'pinia'
import { store } from '..'
import { APP_LOCAL_KEY, APP_MESSAGES_KEY } from '../../enums/cacheEnum'
import { LocaleType } from '@/locale/useLocale'
import { getCache, setCache } from '@/utils/cache'

export interface LanguagesMenus {
    cultureName: string
    displayName: string
    flagIcon: string
    uiCultureName: string
}

interface LocaleState {
    locale: LocaleType
    messages: { [key: string]: string }
    languagesMenus: LanguagesMenus[]
}

export const useLocaleStore = defineStore({
    id: 'app-locale',
    state: (): LocaleState => ({
        locale: 'zh-Hans',
        messages: {},
        languagesMenus: [],
    }),
    getters: {
        getLocale(): LocaleType {
            return this.locale
        },
        getMessages(): { [key: string]: string } {
            return this.messages
        },
        getLanguagesMenus(): LanguagesMenus[] {
            return this.languagesMenus
        },
        getDictionary(): { [key: string]: string } {
            return this.messages.values
        },
    },
    actions: {
        setLocale(locale: LocaleType) {
            setCache(APP_LOCAL_KEY, locale)
            this.locale = locale
        },
        setMessages(messages: { [key: string]: string }) {
            setCache(APP_MESSAGES_KEY, messages)
            this.messages = messages
        },
        initialize() {
            const locale = getCache<string>(APP_LOCAL_KEY) as LocaleType
            this.locale = locale ?? 'zh-Hans'
            const messages = getCache<{ [key: string]: string }>(APP_MESSAGES_KEY)
            if (messages !== undefined && messages !== null) {
                this.messages = messages.values ?? {}
                this.languagesMenus = messages.languages // 保存语言菜单
            }
        },
    },
})

// Need to be used outside the setup
export function useLocaleStoreWithOut() {
    return useLocaleStore(store)
}
