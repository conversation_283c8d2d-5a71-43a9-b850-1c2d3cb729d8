<template>
    <view>
        <InformationCard v-for="(v, k) in subDeviceList" :key="k" :title="v.deviceName" :status="v.status">
            <template #line-first>
                {{ $t('Helper.Status') }}:
                <nut-tag :type="v.status === 'ONLINE' ? 'success' : 'danger'">
                    {{ $t(`IotService.${v.status}`) }}
                </nut-tag>
            </template>
            <template #line-second> {{ $t('IotService.NodeType') }}:{{ $t(`IotService.${v.nodeType}`) }} </template>
            <template #line-third>{{ $t('Helper.UseStatus') }}:{{ $t(`Helper.${v.useStatus}`) }}</template>
        </InformationCard>
        <view v-if="isNoMore" style="text-align: center">{{ $t('ui.noMore') }}</view>
        <nut-empty v-if="noData" :description="$t('Helper.NoData')" />
    </view>
</template>

<script lang="ts" setup>
import { IotDeviceService } from '@/api/proxy/iot-device-service/iot-device-service.service'
import {
    GetIotDeviceInput,
    IotDeviceListDto,
    IotDeviceUseStatus,
    IotNodeType,
} from '@/api/proxy/iot-device-service/models'
import { getCurrentInstance } from '@tarojs/runtime'
import Taro, { useReachBottom } from '@tarojs/taro'
import { reactive, ref } from 'vue'
import { useDidShow, usePullDownRefresh } from '@/hooks/component/hooks'
import { HideLoading, Loading, RequestFail } from '@/utils/Taro-api'
import InformationCard from '@/components/InformationCard.vue'
import { t } from '@/locale/fanyi'

definePageConfig({
    enablePullDownRefresh: true,
})

const iotDeviceService = new IotDeviceService()
const { router } = getCurrentInstance()
const subDeviceList = ref<IotDeviceListDto[]>([])
const isNoMore = ref<boolean>(false)
const noData = ref<boolean>(false)
const parmas = reactive<GetIotDeviceInput>({
    nodeType: IotNodeType.Device,
    useStatus: IotDeviceUseStatus.Unknown,
    gatewayId: router?.params.id as string,
    maxResultCount: 10,
    sorting: '',
    filterText: '',
    skipCount: 0,
})

/**
 * @description 获取子设备
 */
const fetchSubDevice = async () => {
    try {
        Loading()
        const result = await iotDeviceService.getPaged(parmas)
        subDeviceList.value = result.items
        if (result.items.length === 0) {
            noData.value = true
        }
        HideLoading()
    } catch {
        RequestFail()
    }
}

/**
 * @description 修改头部标题
 */
const changeTitle = () => {
    Taro.setNavigationBarTitle({
        title: t('ui.ioTSubDevices'),
    })
}

/**
 * @description 清除parmas
 */
const clearParmas = () => {
    parmas.filterText = ''
    parmas.skipCount = 0
    parmas.sorting = ''
}

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {
    await fetchSubDevice()
    changeTitle()
})

/**
 * @description 页面触底加载更多
 */
useReachBottom(async () => {
    if (isNoMore.value) {
        return
    }
    Loading()
    parmas.skipCount += 10
    const result = await iotDeviceService.getPaged(parmas)
    subDeviceList.value = subDeviceList.value.concat(result.items)
    if (result.items.length === 0) {
        isNoMore.value = true
    }
    HideLoading()
})

/**
 * @description 页面下拉刷新
 */
usePullDownRefresh(async () => {
    clearParmas()
    isNoMore.value = false
    await fetchSubDevice()
    Taro.stopPullDownRefresh()
})
</script>

<style lang="scss"></style>
