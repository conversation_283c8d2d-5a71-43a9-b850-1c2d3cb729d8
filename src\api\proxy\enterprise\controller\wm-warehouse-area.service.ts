import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { GetForEditInput, NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetWmWarehouseAreaForEditorOutput,
    GetWmWarehouseAreaInput,
    WmWarehouseAreaCreateDto,
    WmWarehouseAreaDetailDto,
    WmWarehouseAreaEditDto,
    WmWarehouseAreaListDto,
} from '../wms/dtos/models'

export class WmWarehouseAreaService {
    apiName = 'EnterpriseService'

    create = (input: WmWarehouseAreaCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-warehouse-area',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-warehouse-area/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmWarehouseAreaDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-warehouse-area/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmWarehouseAreaDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse-area/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmWarehouseAreaForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse-area/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse-area/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetWmWarehouseAreaInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmWarehouseAreaListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse-area',
                params: {
                    warehouseId: input.warehouseId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-warehouse-area/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    update = (input: WmWarehouseAreaEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-warehouse-area',
                data: input,
            },
            options,
        )
}
