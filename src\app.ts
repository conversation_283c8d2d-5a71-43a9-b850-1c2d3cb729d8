import { createApp } from 'vue'
import './app.scss'
import '@nutui/touch-emulator'
import { initializeApp } from './hooks/web/initApp'
import { setupStore } from './stores'
import { setupI18n } from './locale/setupI18n'
import { setupGlobDirectives } from './directives'
import { hasPermission, hasRole } from './hooks/web/acl/permission'
import { t } from './locale/fanyi'
import { setUpTabBarI18n } from './hooks/tabBar'
import { routerInterceptor } from './hooks/Interceptor/router'
import { debounce } from './utils'
import { getAbpAppConfig } from './api/app/appConfig'

const App = createApp({
    beforeCreate() {
        getAbpAppConfig()
        //注册TabBar国际化
        setUpTabBarI18n()
    },
    // 入口组件不需要实现 render 方法，即使实现了也会被 taro 所覆盖
})

App.mixin({
    beforeCreate() {
        debounce(function () {
            routerInterceptor()
        }, 500) // 500 毫秒内连续调用时只执行一次
    },
})

// 初始化app
initializeApp()
// 注册状态管理Store
setupStore(App)
// 多语言
setupI18n(App)
// 注册全局指令
setupGlobDirectives(App)
// 注册全局组件
// App.component('AppDefault', AppDefault);
// 挂载全局翻译
App.config.globalProperties.$t = t
// 挂载全局权限判断方法
App.config.globalProperties.$permission = hasPermission
// 挂载全局角色判断方法
App.config.globalProperties.$role = hasRole

export default App
