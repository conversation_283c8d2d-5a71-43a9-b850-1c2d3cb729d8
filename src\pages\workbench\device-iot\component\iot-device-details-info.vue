<template>
    <view>
        <nut-empty v-if="isFetchError" description="Error" />
        <nut-table v-else :columns="columns" :data="tableData"></nut-table>
    </view>
</template>

<script lang="ts" setup>
import { IotDeviceService } from '@/api/proxy/iot-device-service/iot-device-service.service'
import dayjs from 'dayjs'
import { IotHubDeviceDetailedDto } from '@/api/proxy/iot-device-service/models'
import { getCurrentInstance } from '@tarojs/runtime'
import Taro from '@tarojs/taro'
import { ref, computed, h } from 'vue'
import { useDidShow, usePullDownRefresh } from '@/hooks/component/hooks'
import { HideLoading, Loading, RequestFail } from '@/utils/Taro-api'
import { t } from '@/locale/fanyi'
import moment from 'moment'
import { Tag, Ellipsis } from '@nutui/nutui-taro'

definePageConfig({
    enablePullDownRefresh: true,
})

const iotDeviceService = new IotDeviceService()
const { router } = getCurrentInstance()
const iotDeviceInfo = ref<IotHubDeviceDetailedDto>()
const isFetchError = ref<boolean>(false)

// 定义表格列
const columns = [
    { title: t('IotService.PropertyName'), key: 'label' },
    {
        title: t('IotService.PropertyValue'),
        key: 'value',
        render(row: any) {
            if (row.renderValue) {
                return h(Ellipsis, { direction: 'middle', content: row.value })
            } else if (row.renderStatus) {
                return h(Tag, { type: row.status === 'ONLINE' ? 'success' : 'danger' }, { default: () => row.value })
            } else {
                return row.value
            }
        },
    },
]

// 将设备信息转换为表格数据
const tableData = computed(() => {
    if (!iotDeviceInfo.value) return []
    return [
        { label: t('IotService.DeviceName'), value: iotDeviceInfo.value.deviceName || '-' },
        {
            label: t('IotService.DeviceId'),
            value: iotDeviceInfo.value.deviceId || '-',
            renderValue: true,
        },
        { label: t('IotService.NodeId'), value: iotDeviceInfo.value.nodeId || '-' },
        { label: t('IotService.NodeType'), value: iotDeviceInfo.value.nodeType || '-' },
        { label: t('IotService.ProductName'), value: iotDeviceInfo.value.productName || '-' },
        { label: t('IotService.FwVersion'), value: iotDeviceInfo.value.fwVersion || '-' },
        { label: t('IotService.SwVersion'), value: iotDeviceInfo.value.swVersion || '-' },
        {
            label: t('IotService.Status'),
            value: t(`IotService.${iotDeviceInfo.value.status}`),
            status: iotDeviceInfo.value.status,
            renderStatus: true,
        },
        {
            label: t('Helper.ActiveTime'),
            value: iotDeviceInfo.value.activeTime
                ? dayjs(iotDeviceInfo.value.activeTime).format('YYYY-MM-DD HH:mm:ss')
                : '-',
        },
        {
            label: t('IotService.CreationTime'),
            value: iotDeviceInfo.value.createTime
                ? moment(iotDeviceInfo.value.createTime).format('YYYY-MM-DD HH:mm:ss')
                : '-',
        },
    ]
})

/**
 * @description 获取网关详情
 */
const fetchData = async () => {
    try {
        Loading()
        const result = await iotDeviceService.getDeviceById(router?.params.id as string)
        iotDeviceInfo.value = result
        HideLoading()
    } catch {
        RequestFail()
        isFetchError.value = true
    }
}

/**
 * @description 修改头部标题
 */
const changeTitle = () => {
    Taro.setNavigationBarTitle({
        title: t('IotService.IotDeviceInfo'),
    })
}

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {
    await fetchData()
    changeTitle()
})

/**
 * @description 页面下拉刷新
 */
usePullDownRefresh(async () => {
    await fetchData()
    Taro.stopPullDownRefresh()
})
</script>

<style lang="scss">
.nut-cell__value {
    color: black;
}

.nut-table {
    width: 100%;
}
</style>
