<template>
    <nut-tabs v-model="tabsValue" :animated-time="0" :title-scroll="true" title-gutter="30" :ellipsis="true">
        <nut-tab-pane :title="$t('ui.dataView')" pane-key="1">
            <DialogSelectDate @show:visible="handleShowDialogSelectData" @update:select-date="handleSelectDate" />
            <view v-if="isActive('1') && !isShowDialog">
                <ProportionOfEquipmentUsage :device-id="routerParams?.id" :date-picker="selectDate" />
                <DeviceWorkModeRate :device-id="routerParams?.id" :date-picker="selectDate" />
                <DeviceStatusRate :device-id="routerParams?.id" :date-picker="selectDate" />
                <DeviceHistoryWorkMode :device-id="routerParams?.id" :date-picker="selectDate" />
                <DeviceHistoryStatus :device-id="routerParams?.id" :date-picker="selectDate" />
            </view>
        </nut-tab-pane>
        <nut-tab-pane :title="$t('ui.deviceDetails')" pane-key="2" style="padding: 0">
            <DeviceDetailsInformation v-if="isActive('2')" :id="routerParams?.id" />
        </nut-tab-pane>
        <nut-tab-pane :title="$t('Platform.ProductionStatistics')" pane-key="3">
            <ListScreen
                is-show-select-time
                :is-show-search="false"
                :is-show-select-factory="false"
                @update:show-menu="handleShowMenu"
                @update:selected-time="handleSelectTime"
            />
            <ProductionStatistics
                v-if="isActive('3') && showCanvas"
                :device-id="routerParams?.id"
                :start-time="startTime"
                :end-time="endTime"
            />
        </nut-tab-pane>
    </nut-tabs>
</template>

<script lang="ts" setup>
import { useDidShow } from '@/hooks/component/hooks'
import { getCurrentInstance } from '@tarojs/taro'
import { computed, ref } from 'vue'
import DeviceDetailsInformation from '@/pages/workbench/device-list/device-details-information/index.vue'
import ProportionOfEquipmentUsage from '@/pages/workbench/device-list/data-view/proportion-of-equipment-usage.vue'
import DeviceHistoryStatus from '@/pages/workbench/device-list/data-view/device-history-status.vue'
import DeviceHistoryWorkMode from '@/pages/workbench/device-list/data-view/device-history-work-mode.vue'
import DeviceWorkModeRate from '@/pages/workbench/device-list/data-view/device-work-mode-rate.vue'
import DeviceStatusRate from '@/pages/workbench/device-list/data-view/device-status-rate.vue'
import ProductionStatistics from '@/pages/workbench/device-list/data-view/production-statistics.vue'
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'

/**
 * @description 修改导航栏标题
 */
Taro.setNavigationBarTitle({ title: t('ui.deviceDetails') })

const Current = getCurrentInstance()
const routerParams = Current.router?.params
const tabsValue = ref<string>('1')
const showCanvas = ref<boolean>(true)
const isShowDialog = ref<boolean>(false)
const startTime = ref<string>('')
const endTime = ref<string>('')
const selectDate = ref<string>('')
const isActive = computed(() => {
    return (key: string) => {
        return tabsValue.value === key
    }
})

/**
 * @description 显示日期选择
 */
const handleShowDialogSelectData = (isShow: boolean) => {
    isShowDialog.value = isShow
}

/**
 * @description 选择日期
 */
const handleSelectDate = (date: string) => {
    selectDate.value = date
}

/**
 * @description 打开菜单
 */
const handleShowMenu = (isShowMenu: boolean) => {
    showCanvas.value = !isShowMenu
}

/**
 * @description 选择日期单元
 */
const handleSelectTime = (time: string[]) => {
    startTime.value = time[0]
    endTime.value = time[1]
}

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {})
</script>

<style>
.nut-cell__value {
    color: black;
}
</style>
