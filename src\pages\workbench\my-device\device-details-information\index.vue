<template>
    <nut-cell :title="$t(`Helper.Name`)" :desc="deviceDetail?.name" />
    <nut-cell :title="$t(`Helper.Encode`)" :desc="deviceDetail?.encode" />
    <nut-cell :title="$t(`Helper.SerialNo`)">
        <template #desc>
            {{ deviceDetail?.serialNo }}
        </template>
    </nut-cell>
    <nut-cell :title="$t(`IotService.DeviceForm`)" :desc="$t(`Platform.${deviceDetail?.deviceForm}`)" />
    <nut-cell :title="$t(`IotService.DeviceType`)" :desc="$t(`Platform.${deviceDetail?.deviceType}`)" />
    <nut-cell :title="$t(`Helper.IsEnabled`)">
        <template #desc>
            <nut-tag :type="deviceDetail?.isActivate ? 'success' : 'danger'">
                {{ deviceDetail?.isActivate ? $t(`Helper.Enable`) : $t(`Helper.Disable`) }}
            </nut-tag>
        </template>
    </nut-cell>
    <nut-cell :title="$t(`Helper.DutyUser`)" :desc="deviceDetail?.produceDutyUserName" />
    <nut-cell :title="$t(`Platform.MaintainDutyUser`)" :desc="deviceDetail?.maintainDutyUserName" />
    <nut-cell :title="$t(`Platform.IotDeviceName`)" :desc="deviceDetail?.iotDeviceName" />
    <nut-cell
        :title="$t(`AbpIdentity.CreationTime`)"
        :desc="dayjs(deviceDetail?.creationTime).format('YYYY-MM-DD HH:mm:ss')"
    />
    <nut-cell :title="$t(`Helper.TargetNum`)">
        <template #desc>
            {{ deviceDetail?.targetNum }}
        </template>
    </nut-cell>
    <nut-cell :title="$t(`Helper.WorkCount`)">
        <template #desc>
            {{ deviceDetail?.workCount }}
        </template>
    </nut-cell>
    <nut-cell>
        <template #default>
            <view style="width: 100%">
                <view>
                    {{ $t(`Platform.ProductionProgress`) }}
                </view>
                <nut-progress style="margin-top: 10px" :percentage="deviceDetail?.productivity" />
            </view>
        </template>
    </nut-cell>
    <nut-cell v-if="deviceDetail?.images && deviceDetail?.images !== '[]'" :title="$t('IotService.DeviceImages')">
        <template #desc>
            <view @click="handleShowImage"> {{ $t('ui.viewImage') }}> </view>
        </template>
    </nut-cell>
    <nut-empty v-if="isFetchError" description="Error" />
</template>

<script lang="ts" setup>
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { FactoryDeviceIotFlowListDto } from '@/api/proxy/data-view/models'
import { useDidShow } from '@/hooks/component/hooks'
import Taro from '@tarojs/taro'
import { ref } from 'vue'
import { t } from '@/locale/fanyi'
import { handleImages } from '@/utils/image'
import dayjs from 'dayjs'
import { HideLoading, Loading, Toast } from '@/utils/Taro-api'

const props = withDefaults(
    defineProps<{
        id: string
    }>(),
    {
        id: '',
    },
)

const datavDataFlowService = new DatavDataFlowService()
const deviceDetail = ref<FactoryDeviceIotFlowListDto>()
const isFetchError = ref<boolean>(false)

/**
 * @description 获取设备详细信息
 */
const fetchData = async () => {
    try {
        Loading()
        if (props.id === undefined || props.id === undefined) {
            Taro.showToast({ title: '设备ID不存在', icon: 'none' })
            return false
        }
        deviceDetail.value = await datavDataFlowService.getByDeviceId(props.id)
        HideLoading()
    } catch (e) {
        Toast(t('ui.requestFailed'), { icon: 'error' })
        isFetchError.value = true
    }
}

/**
 * @description 查看图片
 */
const handleShowImage = () => {
    if (deviceDetail.value?.images === undefined || deviceDetail.value?.images === '{}') {
        return
    }
    const urls = handleImages(deviceDetail.value?.images)
    Taro.previewImage({
        urls: urls ?? [],
    })
}

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {
    await fetchData()
})
</script>
<style>
.nut-cell__value {
    color: black;
}
</style>
