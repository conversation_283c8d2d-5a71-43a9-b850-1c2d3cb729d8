<template>
    <nut-tabs v-model="tabsValue">
        <nut-tab-pane :title="$t('IotService.DeviceAlarmList')" pane-key="1">
            <nut-empty v-if="alarmList?.length === 0" :description="$t('Platform.NoData')" />
            <view v-else>
                <InformationCard v-for="(v, k) in alarmList" :key="k" is-icon-image :icon-image="errorImage">
                    <template #title>
                        <view>{{ v.text }}</view>
                    </template>
                    <template #line-first>
                        <view>{{ `${$t('AbpIdentityServer.Type')}:${v.type}` }}</view>
                    </template>
                    <template #line-second>
                        <view>{{ `${$t('Helper.Time')}:${dayjs(v.time).format('YYYY-MM-DD HH:mm:ss')}` }}</view>
                    </template>
                    <template #line-third>
                        <view>{{ `${$t('IotService.AlarmLevel')}:${v.level}` }}</view>
                    </template>
                </InformationCard>
            </view>
        </nut-tab-pane>
    </nut-tabs>
    <view class="no-more" v-if="reachedEnd && params.skipCount > 0">{{ $t('ui.noMore') }}</view>
    <view class="ios-safe-distance" />
    <nut-empty v-if="isFetchError" description="Error" />
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { IotAlarmLogService } from '@/api/proxy/iot-alarm-log-service/iot-alarm-log-service.service'
import { CncAlarmLevel, GetIotAlarmLogInput, IotAlarmLogListDto } from '@/api/proxy/iot-alarm-log-service/models'
import { useDidShow } from '@/hooks/component/hooks'
import error from '@/assets/images/error.png'
import danger from '@/assets/images/danger.png'
import { HideLoading, Loading, RequestFail } from '@/utils/Taro-api'
import { usePullDownRefresh, useReachBottom } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import dayjs from 'dayjs'
import errorImage from '@/assets/images/error.png'

definePageConfig({
    enablePullDownRefresh: true,
})

const iotAlarmLogService = new IotAlarmLogService()
const tabsValue = ref<string>('1')
const alarmList = ref<IotAlarmLogListDto[]>()
const isFetchError = ref<boolean>(false)
const reachedEnd = ref<boolean>(false)
const params = reactive<GetIotAlarmLogInput>({
    iotDeviceId: Taro.useRouter().params.id,
    sorting: '',
    filterText: '',
    skipCount: 0,
    maxResultCount: 10,
})
const image = computed((type: string) => {
    if (type === CncAlarmLevel.Error) {
        return error
    }
    return danger
})

/**
 * @description 获取报警列表
 */
const fetchData = async () => {
    try {
        if (!reachedEnd.value) {
            Loading()
            const result = await iotAlarmLogService.getPaged(params)
            alarmList.value = result.items
            reachedEnd.value = result.items.length === 0
            HideLoading()
        }
    } catch {
        RequestFail()
        isFetchError.value = true
    }
}

/**
 * @description 页面显示时的钩子
 */
useDidShow(() => {
    fetchData()
})

useReachBottom(async () => {
    params.skipCount += 10
    try {
        if (!reachedEnd.value) {
            Loading()
            const result = await iotAlarmLogService.getPaged(params)
            alarmList.value?.push(result.items)
            reachedEnd.value = result.items.length === 0
            HideLoading()
        }
    } catch {
        RequestFail()
        isFetchError.value = true
    }
})

/**
 * @description 下拉刷新事件
 */
usePullDownRefresh(async () => {
    await fetchData()
    Taro.stopPullDownRefresh()
})
</script>

<style lang="scss">
.no-more {
    text-align: center;
    padding: 10px;
    color: #999;
    font-size: 32px;
}

.ios-safe-distance {
    bottom: 0;
    width: 100%;
    height: 10rpx;
    display: flex;
    z-index: 99;
}
</style>
