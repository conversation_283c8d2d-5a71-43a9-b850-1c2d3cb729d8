import device from '@/assets/images/icon/device.png'
import gateway from '@/assets/images/icon/gateway.png'
// import camera from '@/assets/images/icon/camera.png'
import deviceDashboard from '@/assets/images/icon/device-dashboard.png'
import productionDashboard from '@/assets/images/icon/production-dashboard.png'
import { t } from '@/locale/fanyi'

const menus = {
    menu: [
        {
            text: '首页',
            i18n: t('menu.main'),
            group: true,
            hideInBreadcrumb: true,
            children: [
                {
                    text: '快捷菜单',
                    i18n: t('menu.shortcutMenu'),
                    icon: 'anticon anticon-dashboard',
                    children: [
                        {
                            text: '设备列表',
                            link: '/pages/workbench/device-list/index',
                            i18n: t('menu.deviceList'),
                            icon: device,
                            guard: { permission: ['PlatformService.FactoryDevice'] },
                        },
                        {
                            text: '物联网关',
                            link: '/pages/workbench/device-iot/index',
                            i18n: t('menu.gateway'),
                            icon: gateway,
                            guard: { permission: ['IotService.IotHubDevice'] },
                        },
                        // {
                        //   text: '视频监控',
                        //   link: '/pages/workbench/factory-camera/index',
                        //   i18n: 'Enterprise.FactoryCamera',
                        //   icon: camera
                        // },
                        {
                            text: '设备看板',
                            link: '/pages/workbench/device-board/index',
                            i18n: t('menu.deviceKanban'),
                            icon: deviceDashboard,
                            guard: { permission: ['PlatformService.FactoryDevice'] },
                        },

                        {
                            text: '生产看板',
                            link: '/pages/workbench/production-board/index',
                            i18n: t('menu.productionKanban'),
                            icon: productionDashboard,
                            guard: { permission: ['PlatformService.FactoryDevice'] },
                        },
                    ],
                },
                {
                    text: '工具菜单',
                    i18n: t('menu.toolMenu'),
                    icon: 'anticon anticon-tool',
                    children: [
                        {
                            text: '扫一扫',
                            i18n: t('menu.scanCode'),
                            icon: 'scan2',
                            link: '',
                        },
                    ],
                },
            ],
        },
    ],
}

export default menus
