<template>
    <ListScreen @update:screen-factory="handleScreenFactory" @update:search-key="handleSearchKey" />
</template>

<script lang="ts" setup>
import { ******************** } from '@/api/proxy/factory-camera-service/factory-camera-service.service'
import { FactoryCameraListDto } from '@/api/proxy/factory-camera-service/models'
import { reactive, ref } from 'vue'

const factoryCameraService = new ********************()
const factoryCameraList = ref<FactoryCameraListDto>()
const searchParams = reactive({
    factoryParentId: '',
    sorting: '',
    filterText: '',
    skipCount: 0,
    maxResultCount: 10,
})

/**
 * @description 筛选关键词
 */
const handleSearchKey = (key: string) => {
    console.log(key)
}

/**
 * @description 筛选工厂
 */
const handleScreenFactory = (screenFactoryId: string) => {
    searchParams.factoryParentId = screenFactoryId
}

/**
 * @description 获取工厂摄像头列表
 */
const fetchData = async () => {
    // const result = await factoryCameraService.getPaged(searchParams)
    // factoryCameraList.value = result.items
}
</script>
<style scoped></style>
