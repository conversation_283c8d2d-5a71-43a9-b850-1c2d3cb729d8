<template>
    <view>
        <nut-cell :title="t('text.name')" :desc="detailsList.name"></nut-cell>
        <nut-cell :title="t('text.encode')" :desc="detailsList.encode"></nut-cell>
        <nut-cell :title="t('text.describe')" :desc="detailsList.description"></nut-cell>
        <nut-cell :title="t('text.creationTime')" :desc="formatTime(detailsList.creationTime)"></nut-cell>
        <nut-cell :title="'采购状态'">
            <template #desc>
                <nut-tag :color="getStatusColor(detailsList.status)">
                    {{ getStatusText(detailsList.status) }}
                </nut-tag>
            </template>
        </nut-cell>
        <nut-cell :title="'订单日期'" :desc="formatTime(detailsList.orderDate)"></nut-cell>
        <nut-cell :title="'预计到达日期'" :desc="formatTime(detailsList.eta)"></nut-cell>
        <nut-cell :title="'实际到达日期'" :desc="formatTime(detailsList.ata)"></nut-cell>
        <nut-cell :title="'申请人'" :desc="detailsList.applicantUserName || '-'"></nut-cell>
        <nut-cell :title="'执行人'" :desc="detailsList.executorUserName || '-'"></nut-cell>
        <nut-cell :title="'总价'" :desc="detailsList.totalPrice"></nut-cell>
    </view>
</template>

<script setup lang="ts">
import { t } from '@/locale/fanyi'
import dayjs from 'dayjs'

// 采购单状态映射
const purchaseStatusMap = {
    Newly: { type: 'info', text: '新建', color: '#909399' },
    PendingApproval: { type: 'warning', text: '待审批', color: '#E6A23C' },
    Approved: { type: 'success', text: '已审批', color: '#67C23A' },
    Purchasing: { type: 'warning', text: '采购中', color: '#E6A23C' },
    Ordered: { type: 'warning', text: '已下单', color: '#E6A23C' },
    Rejected: { type: 'danger', text: '已拒绝', color: '#F56C6C' },
    Cancelled: { type: 'info', text: '已取消', color: '#909399' },
    Completed: { type: 'success', text: '已完成', color: '#67C23A' },
    Returned: { type: 'danger', text: '有退回', color: '#F56C6C' },
}

// 获取状态颜色
const getStatusColor = (status: string) => {
    return purchaseStatusMap[status]?.color || '#909399'
}

// 获取状态文本
const getStatusText = (status: string) => {
    return purchaseStatusMap[status]?.text || status
}

// 格式化时间
const formatTime = (time: string) => {
    if (!time) return '-'
    return dayjs(time).format('YYYY-MM-DD HH:mm')
}

interface TableData {
    detailsList: {
        id: string
        name: string
        encode: string
        description: string
        orderDate?: string
        eta?: string
        ata?: string
        status: string
        applicantUserId: string
        applicantUserName?: string
        reviewerUserId: string
        executorUserId: string
        executorUserName: string
        totalPrice: number
        creatorId?: string
        creationTime: string
    }
}

const props = defineProps<TableData>()
</script>

<style lang="scss" scoped></style>
