<template>
    <view class="in-warehouse-items">
        <view v-if="loading" class="loading-container">
            <nut-skeleton rows="5" title animated />
        </view>
        <view v-else-if="itemsList.length === 0" class="empty-container">
            <nut-empty description="暂无入库子项" image="empty" />
        </view>
        <view v-else class="items-list">
            <view
                v-for="(item, index) in itemsList"
                :key="item.id"
                class="item-container"
                @click="navigateToItemDetails(item.id)"
            >
                <InformationCard :isActivate="true">
                    <template #front-title>[{{ index + 1 }}]</template>
                    <template #title>{{ item.name }}</template>
                    <template #line-first>编码：{{ item.encode }}</template>
                    <template #line-second>描述：{{ item.describe || '-' }}</template>
                    <template #line-third>
                        <view class="quantity-row">
                            <text>数量：{{ item.quantity }}</text>
                            <text v-if="item.unitPrice" style="margin-left: 20px">单价：¥{{ item.unitPrice }}</text>
                        </view>
                    </template>
                    <template #content-default v-if="item.totalPrice">总价：¥{{ item.totalPrice }}</template>
                    <template #space-one>
                        <nut-tag :color="getStatusColor(item.status)" style="margin-bottom: 0.5vh">
                            {{ getStatusText(item.status) }}
                        </nut-tag>
                    </template>
                </InformationCard>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { t } from '@/locale/fanyi'
import { WmInWarehouseListService } from '@/api/proxy/enterprise/controller/wm-in-warehouse-list.service'
import { Toast, Loading, HideLoading } from '@/utils/Taro-api'
import * as InformationCardModule from '@/components/InformationCard.vue'

// 使用通用方式导入组件
const InformationCard = InformationCardModule.default || InformationCardModule

interface InWarehouseItem {
    id: string
    name: string
    encode: string
    describe?: string
    materialId: string
    materialName?: string
    locationId?: string
    locationName?: string
    quantity: number
    unitPrice?: number
    totalPrice?: number
    inWarehouseBillId: string
    status: string
}

interface InWarehouseItemsProps {
    inWarehouseBillId: string
}

const props = defineProps<InWarehouseItemsProps>()

const wmInWarehouseListService = new WmInWarehouseListService()
const itemsList = ref<InWarehouseItem[]>([])
const loading = ref(false)

// 状态映射
const statusMap = {
    Newly: { type: 'default', text: '新建', color: '#909399' },
    Pending: { type: 'warning', text: '待处理', color: '#E6A23C' },
    InProgress: { type: 'primary', text: '处理中', color: '#3460fa' },
    Received: { type: 'success', text: '已入库', color: '#67C23A' },
    Returned: { type: 'danger', text: '已退回', color: '#F56C6C' },
    Cancelled: { type: 'default', text: '已取消', color: '#909399' },
    PartiallyReturned: { type: 'warning', text: '部分退回', color: '#E6A23C' },
}

// 状态颜色映射
const getStatusColor = (status: string) => {
    return statusMap[status]?.color || '#909399'
}

// 状态文本映射
const getStatusText = (status: string) => {
    return statusMap[status]?.text || status
}

// 获取入库子项数据
const fetchInWarehouseItems = async () => {
    if (!props.inWarehouseBillId) return

    try {
        loading.value = true
        Loading()

        const result = await wmInWarehouseListService.getPaged({
            inWarehouseBillId: props.inWarehouseBillId,
            filterText: '',
        })

        if (result && result.items) {
            itemsList.value = result.items
        } else {
            itemsList.value = []
        }
    } catch (error) {
        console.error('获取入库子项失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        loading.value = false
        HideLoading()
    }
}

// 监听入库单ID变化
watch(
    () => props.inWarehouseBillId,
    newVal => {
        if (newVal) {
            fetchInWarehouseItems()
        } else {
            itemsList.value = []
        }
    },
    { immediate: true },
)

// 暴露刷新方法
defineExpose({
    refresh: fetchInWarehouseItems,
})

// 导航到详情页
const navigateToItemDetails = (id: string) => {
    emit('showDetails', id)
}

// 定义事件
const emit = defineEmits(['showDetails'])
</script>

<style lang="scss" scoped>
.in-warehouse-items {
    background-color: #f5f5f5;
    padding: 12px;
    min-height: calc(100vh - 100px);

    .loading-container {
        padding: 20px 0;
    }

    .empty-container {
        padding: 30px 0;
        text-align: center;
    }

    .items-list {
        .item-container {
            margin-bottom: 12px;

            :deep(.information-card) {
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            }
        }

        .quantity-row {
            display: flex;
            align-items: center;
            color: #606266;

            text:nth-child(2) {
                color: #e6a23c;
                font-weight: 500;
            }
        }
    }
}
</style>
