import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/model/axios'
import {
    DeviceOptModeRateDataDto,
    DeviceProductionTotalProgressDto,
    DeviceStatusCountsDto,
    DeviceStatusHistoryDatasDto,
    DeviceStatusRateDataDto,
    DeviceUseRateDataDto,
    DeviceWorkCountModelDto,
    DeviceWorkCountRankDto,
    FactoryDeviceIotFlowListDto,
    GetDataViewDeviceInput,
    GetDeviceDataInput,
    GetDeviceTimeRangeInput,
    GetDeviceTimeUnitInput,
} from './models'
import { PagedResultDto } from '@/shared/models/dtos'
import { GetFactoryDeviceInput } from '../factory-device-service/models'

export class DatavDataFlowService {
    getDeviceStatusCount = (input: GetDataViewDeviceInput, options?: RequestOptions) =>
        defHttp.request<DeviceStatusCountsDto>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/device-status-count',
                params: {
                    factoryParentId: input.factoryParentId,
                },
            },
            options,
        )
    getByDeviceId = (deviceId: string, options?: RequestOptions) =>
        defHttp.request<FactoryDeviceIotFlowListDto>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/device-id',
                params: {
                    deviceId,
                },
            },
            options,
        )

    getDeviceUseRateByInput = (input: GetDeviceDataInput, options?: RequestOptions) =>
        defHttp.request<DeviceUseRateDataDto>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/rate-use',
                params: {
                    factoryModelId: input.factoryModelId,
                    deviceId: input.deviceId,
                    dateTime: input.dateTime,
                },
            },
            options,
        )

    getDeviceModeRateByInput = (input: GetDeviceDataInput, options?: RequestOptions) =>
        defHttp.request<DeviceOptModeRateDataDto>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/rate-mode',
                params: {
                    factoryModelId: input.factoryModelId,
                    deviceId: input.deviceId,
                    dateTime: input.dateTime,
                },
            },
            options,
        )

    getDeviceStatusRateByInput = (input: GetDeviceDataInput, options?: RequestOptions) =>
        defHttp.request<DeviceStatusRateDataDto>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/rate-status',
                params: {
                    factoryModelId: input.factoryModelId,
                    deviceId: input.deviceId,
                    dateTime: input.dateTime,
                },
            },
            options,
        )

    getPaged = (input: GetFactoryDeviceInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<FactoryDeviceIotFlowListDto>>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/paged',
                params: {
                    factoryParentId: input.factoryParentId,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    getDeviceWorkCountByDevice = (input: GetDataViewDeviceInput, options?: RequestOptions) =>
        defHttp.request<DeviceWorkCountRankDto[]>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/work-count-by-device',
                params: {
                    factoryParentId: input.factoryParentId,
                },
            },
            options,
        )

    getProductionTotalProgress = (input: GetDataViewDeviceInput, options?: RequestOptions) =>
        defHttp.request<DeviceProductionTotalProgressDto>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/work-total-progress',
                params: {
                    factoryParentId: input.factoryParentId,
                },
            },
            options,
        )

    getDeviceWorkCountByProg = (input: GetDataViewDeviceInput, options?: RequestOptions) =>
        defHttp.request<DeviceWorkCountRankDto[]>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/work-count-by-prog',
                params: {
                    factoryParentId: input.factoryParentId,
                },
            },
            options,
        )

    getWorkCountStatistics = (input: GetDeviceTimeRangeInput, options?: RequestOptions) =>
        defHttp.request<DeviceWorkCountModelDto[]>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/work-count-statistics',
                params: {
                    factoryModelId: input.factoryModelId,
                    deviceId: input.deviceId,
                    startTime: input.startTime,
                    endTime: input.endTime,
                },
            },
            options,
        )

    getDeviceHistoryStatusByInput = (input: GetDeviceDataInput, options?: RequestOptions) =>
        defHttp.request<DeviceStatusHistoryDatasDto>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/history-status',
                params: {
                    factoryModelId: input.factoryModelId,
                    deviceId: input.deviceId,
                    dateTime: input.dateTime,
                },
            },
            options,
        )

    getDeviceHistoryModeByInput = (input: GetDeviceDataInput, options?: RequestOptions) =>
        defHttp.request<DeviceStatusHistoryDatasDto>(
            {
                method: 'GET',
                url: '/api/platform/datav-data-flow/history-mode',
                params: {
                    factoryModelId: input.factoryModelId,
                    deviceId: input.deviceId,
                    dateTime: input.dateTime,
                },
            },
            options,
        )
}
