<template>
    <view class="card" :class="props.status">
        <view class="title">{{ props.title }}</view>
        <view class="number">{{ props.number }} 台</view>
    </view>
</template>
<script lang="ts" setup>
const props = withDefaults(
    defineProps<{
        title?: string
        number?: number
        status?: string
    }>(),
    {
        title: '',
        number: 0,
        status: 'unknown',
    },
)
</script>
<style lang="scss">
.card {
    width: 100%;
    min-height: 9vh;
    margin: auto;
    border-radius: 8px;
    padding: 20px;
    box-sizing: border-box;
    .title {
        font-size: 30px;
        color: white;
    }
    .number {
        margin-top: 10px;
        font-size: 35px;
        color: white;
        font-weight: bolder;
    }
}

/* Alarm */
.alarm {
    background: linear-gradient(to right, #f99192, #fd5151);
}

/* ESP */
.esp {
    background: linear-gradient(to right, #38ef7d, #11998e);
}

/* Offline */
.offline {
    background: linear-gradient(to right, #bdc3c7, #2c3e50);
}

/* Pause */
.pause {
    background: linear-gradient(to right, #ff6a00, #ee0979);
}

/* Running */
.runing {
    background: linear-gradient(to right, #6ddfa1, #1cd06c);
}

/* Stop */
.stop {
    background: linear-gradient(to right, #ebc387, #e9b568);
}

/* Total */
.total {
    background: linear-gradient(to right, #609ffd, #0268ff);
}

/* Unknown */
.unknown {
    background: linear-gradient(to right, #182848, #4b6cb7);
}
</style>
