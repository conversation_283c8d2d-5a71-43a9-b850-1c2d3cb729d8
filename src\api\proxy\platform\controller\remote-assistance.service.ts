import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetRemoteAssistanceEditorInput,
    GetRemoteAssistanceForEditorOutput,
    GetRemoteAssistanceInput,
    RemoteAssistanceCreateDto,
    RemoteAssistanceDetailDto,
    RemoteAssistanceEditDto,
    RemoteAssistanceListDto,
} from '../dtos/models'

export class RemoteAssistanceService {
    apiName = 'PlatformService'

    create = (input: RemoteAssistanceCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/platform/remote-assistance',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/platform/remote-assistance/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<RemoteAssistanceDetailDto>(
            {
                method: 'GET',
                url: `/api/platform/remote-assistance/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<RemoteAssistanceDetailDto>(
            {
                method: 'GET',
                url: '/api/platform/remote-assistance/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetRemoteAssistanceEditorInput, options?: RequestOptions) =>
        defHttp.request<GetRemoteAssistanceForEditorOutput>(
            {
                method: 'GET',
                url: '/api/platform/remote-assistance/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/platform/remote-assistance/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetRemoteAssistanceInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<RemoteAssistanceListDto>>(
            {
                method: 'GET',
                url: '/api/platform/remote-assistance',
                params: {
                    isComplete: input.isComplete,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    update = (input: RemoteAssistanceEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/platform/remote-assistance',
                data: input,
            },
            options,
        )
}
