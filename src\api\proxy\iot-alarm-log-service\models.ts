import { PagedSortedAndFilteredInputDto } from '@/api/proxy/factory-device-service/models'
import { EntityDto } from '@/shared/models/dtos'

export interface GetIotAlarmLogInput extends PagedSortedAndFilteredInputDto {
    iotDeviceId?: string
    sorting?: string
}

export interface IotAlarmLogListDto extends EntityDto<string> {
    iotId?: string
    code?: string
    type: CncAlarmType
    level: CncAlarmLevel
    time?: string
    text?: string
}

export enum CncAlarmType {
    None = 'None',
    PSW = 'PSW',
    RBT = 'RBT',
    PLC = 'PLC',
    PS = 'PS',
    OT = 'OT',
    OH = 'OH',
    MEM = 'MEM',
    SV = 'SV',
    SB = 'SB',
    OW = 'OW',
    IOB = 'IOB',
    IOM = 'IOM',
    MF = 'MF',
    FM = 'FM',
    STL = 'STL',
    Unknown = 'Unknown',
}

export enum CncAlarmLevel {
    Info = 'Info',
    Warn = 'Warn',
    Alarm = 'Alarm',
    Error = 'Error',
    Unknown = 'Unknown',
}
