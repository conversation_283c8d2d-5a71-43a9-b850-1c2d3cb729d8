import { t } from '@/locale/fanyi'
import Taro from '@tarojs/taro'

/**
 *@description 显示消息提示框
 */
export function Toast(message: string, options?: IToast) {
    const param = {
        title: message,
        icon: options?.icon ?? 'success',
        duration: 2000,
        ...options,
    }
    Taro.showToast(param)
}

/**
 *@description 非TAB页面跳转
 */
export function NavigateTo(url: string) {
    Taro.navigateTo({
        url,
    })
}

/**
 * @description 加载中
 */
export function Loading() {
    Taro.showLoading({
        title: t('WeChat.LoadingWithThreeDot'),
    })
}

/**
 * @description 隐藏加载中
 */
export function HideLoading() {
    Taro.hideLoading()
}

/**
 * @description 提示请求失败
 */
export function RequestFail() {
    Taro.showToast({
        title: t('ui.connectFailed'),
        icon: 'error',
    })
}

/**
 * @description 调起扫码功能
 * @param options 扫码配置选项
 * @returns Promise<Taro.scanCode.SuccessCallbackResult>
 */
export function scanCode(options?: Taro.scanCode.Option): Promise<Taro.scanCode.SuccessCallbackResult> {
    return new Promise((resolve, reject) => {
        Taro.scanCode({
            onlyFromCamera: options?.onlyFromCamera || false,
            scanType: options?.scanType,
            success: res => {
                resolve(res)
            },
            fail: err => {
                // 用户主动取消不提示失败
                if (err && err.errMsg && err.errMsg.indexOf('cancel') !== -1) {
                    // 只reject，不提示
                    reject(err)
                } else {
                    // 真正的扫码失败才提示
                    Toast('扫码失败', { icon: 'error' })
                    reject(err)
                }
            },
        })
    })
}
