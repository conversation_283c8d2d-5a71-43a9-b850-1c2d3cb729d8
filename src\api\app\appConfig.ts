import { AbpApplicationConfigurationService, CurrentUserDto } from '@/api/proxy/application-configurations'
import { CurrentTenantDto } from '@/api/proxy/multi-tenancy'
import { setLocaleMessage } from '@/locale/useLocale'
import { useLocaleStore } from '@/stores/modules/locale'
import { usePermissionStore } from '@/stores/modules/permission'
import { useUserStore } from '@/stores/modules/user'

/**
 * 获取app配置
 */
export async function getAbpAppConfig(): Promise<boolean> {
    const AbpAppConfigService = new AbpApplicationConfigurationService()

    try {
        const res = await AbpAppConfigService.get()

        // 保存权限
        savePermission(res.auth.grantedPolicies, res.currentUser.roles)
        // 保存租户信息
        saveTenantInfo(res.currentTenant)
        // 保存用户信息
        saveUserInfo(res.currentUser)
        // 保存本地化
        saveLocale(res.localization)

        return true
    } catch (error) {
        return Promise.reject(false)
    }
}

//保存授权信息
const savePermission = (grantedPolicies: Record<string, boolean>, roles: string[]) => {
    const usePermission = usePermissionStore()
    // 保存权限
    const policies: string[] = []
    if (grantedPolicies) {
        Object.keys(grantedPolicies).forEach((key: any) => {
            if (grantedPolicies[key]) {
                policies.push(key)
            }
        })
    }
    usePermission.setGrantedPolicies(policies)
    usePermission.setRoles(roles)
}
/**
 * 保存租户信息
 * @param currentTenant
 */
const saveTenantInfo = (currentTenant: CurrentTenantDto) => {
    // 保存租户已在用户点击登录前实现
}

/**
 * 保存用户信息
 * @param currentUser
 */
const saveUserInfo = (currentUser: CurrentUserDto) => {
    const userStore = useUserStore()
    userStore.setUserInfo(currentUser)
}

/**
 * 保存本地化环境
 */
const saveLocale = (localization: any) => {
    const localeStore = useLocaleStore()
    const message = { ...localization }
    localeStore.setMessages(message)
    setLocaleMessage(localeStore.getLocale)
}
