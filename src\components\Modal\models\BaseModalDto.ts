import { VNode } from 'vue'

export interface BaseModalDto {
    /**
     * 标题
     */
    title?: string
    /**
     * 内容
     */
    content: string | any
    /**
     * 宽度 /可以是px或%
     */
    width?: string
    /**
     * 指定挂载节点
     */
    teleport?: string
    /**
     * 点击蒙层是否关闭对话框
     */
    closeOnClickOverlay?: Boolean
    /**
     * 是否隐藏底部按钮栏
     */
    noFooterBoolean?: Boolean
    /**
     * 是否隐藏确定按钮
     */
    noOkBtn?: Boolean
    /**
     * 是否隐藏取消按钮
     */
    noCancelBtn?: Boolean
    /**
     * 取消按钮文案
     */
    cancelText?: string
    /**
     * 确定按钮文案
     */
    okText?: string
    /**
     * 取消按钮是否默认关闭弹窗
     */
    cancelAutoClose?: Boolean
    /**
     * 文字对齐方向，可选值同 css 的 text-align
     */
    textAlign?: string
    /**
     * 是否在页面回退时自动关闭
     */
    closeOnPopstate?: Boolean
    /**
     * 背景是否锁定
     */
    lockScroll?: Boolean
    /**
     * 使用横纵方向 可选值 horizontal、vertical
     */
    footerDirection?: string
    /**
     * 自定义遮罩类名
     */
    overlayClass?: string
    /**
     * 自定义遮罩样式
     */
    overlayStyle?: any
    /**
     * 自定义 popup 弹框类名
     */
    popClass?: string
    /**
     * 	自定义 popup 弹框样式
     */
    popStyleCSSProperties?: any
    /**
     * 自定义 class
     */
    customClass?: string
    /**
     * 关闭前的回调函数，返回 false 可阻止关闭，支持返回 Promise
     */
    beforeClose?: Function
}
