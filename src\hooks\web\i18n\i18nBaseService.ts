import { LocaleType } from '@/locale/useLocale'
import { AlainI18NService, NzSafeAny } from './model'

class AlainI18nBaseService implements AlainI18NService {
    //   private cog: AlainThemeI18nConfig;
    protected _change$ = new Map<string | Symbol, Function>()
    protected _currentLang: string = ''
    protected _defaultLang: string = ''
    protected _data: Record<string, string> = {}
    get change(): Map<string | Symbol, Function> {
        return this._change$
    }
    get defaultLang(): string {
        return this._defaultLang
    }
    get currentLang(): string {
        return this._currentLang
    }
    get data(): Record<string, string> {
        return this._data
    }

    //   constructor(cogSrv: AlainConfigService) {
    //     this.cog = cogSrv.merge('themeI18n', {
    //       interpolation: ['{{', '}}']
    //     })!;
    //   }

    /**
     * Flattened data source
     *
     * @example
     * {
     *   "name": "Name",
     *   "sys": {
     *     "": "System",
     *     "title": "Title"
     *   }
     * }
     * =>
     * {
     *   "name": "Name",
     *   "sys": "System",
     *   "sys.title": "Title"
     * }
     */
    flatData(data: Record<string, unknown>, parentKey: string[]): Record<string, string> {
        const res: Record<string, string> = {}
        if (data === null || data === undefined) {
            return res
        }
        for (const key of Object.keys(data)) {
            const value = data[key]
            if (typeof value === 'object') {
                const child = this.flatData(value as Record<string, unknown>, parentKey.concat(key))
                // 将当前子级提升到当前级
                Object.keys(child).forEach(childKey => (res[childKey] = child[childKey]))
            } else {
                res[(key ? parentKey.concat(key) : parentKey).join('.')] = `${value}`
            }
        }

        return res
    }

    use(lang: LocaleType, data: Record<string, unknown>): void {
        this._data = this.flatData(data ?? {}, [])
        this._currentLang = lang
        // i8n发送变化时调用需要触发的函数
        // this._change$.forEach(v => {
        //   v()
        // })
    }

    getLangs(): NzSafeAny[] {
        return []
    }

    fanyi(path: string, params?: Record<string, unknown>): string {
        const content = this._data[path] || ''
        if (!content) return path

        // if (params) {
        //   const interpolation = this.cog.interpolation!!;
        //   Object.keys(params).forEach(
        //     key =>
        //       (content = content.replace(
        //         new RegExp(`${interpolation[0]}\s?${key}\s?${interpolation[1]}`, 'g'),
        //         `${params[key]}`
        //       ))
        //   );
        // }

        return content
    }
}

const i18nService = new AlainI18nBaseService()

export function useI18n() {
    /**
     * 设置多语言
     * @param lang
     * @param data
     */
    function setLocaleMessage(lang: LocaleType, data: Record<string, unknown>): void {
        i18nService.use(lang, data)
    }

    /**
     * 翻译
     * @param path
     * @param params
     * @returns
     */
    function fanyi(path: string, params?: Record<string, unknown>): string {
        return i18nService.fanyi(path, params)
    }

    /**
     * 获取当前语言
     * @returns
     */
    function getCurrentLang() {
        return i18nService.currentLang
    }

    /**
     * 设置语言发生改变时触发的回调
     */
    function setChangeCall(key: string | Symbol, callback) {
        i18nService.change.set(key, callback)
    }

    return {
        setLocaleMessage,
        fanyi,
        getCurrentLang,
        setChangeCall,
    }
}
