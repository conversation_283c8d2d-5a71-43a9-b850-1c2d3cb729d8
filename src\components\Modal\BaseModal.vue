<template>
    <nut-dialog
        style="width: 100vw"
        class="custom-modal-class"
        :title="title"
        :content="content"
        :visible="visible"
        @cancel="onCancel"
        @ok="onOk"
    />
    <!-- 如果不使用slot方式，不能使用</nut-dialog>方式结尾 -->
    <!-- <slot v-if="slotVisible"></slot> -->
    <!-- </nut-dialog> -->
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRefs } from 'vue'
import { BaseModalDto } from './models/BaseModalDto'

const emit = defineEmits(['register'])

const visible = ref<boolean>(false)
const title = ref<string>('提示')
const content = ref<string>('123456')
const width = ref<string>('96%')

const setModalprops = (props: BaseModalDto & { visible: boolean }) => {
    if (Reflect.has(props, 'visible')) {
        visible.value = !!props.visible
    }
    if (Reflect.has(props, 'title')) {
        title.value = props.title ?? '提示'
    }
    if (Reflect.has(props, 'content')) {
        content.value = props.content
    }
    if (Reflect.has(props, 'width')) {
        width.value = props.width ?? '96%'
    }
}

const onCancel = () => {
    visible.value = false
}

const onOk = () => {}

//组件挂载事件
onMounted(() => {
    emit('register', setModalprops)
})
</script>

<style lang="scss">
.custom-modal-class {
    width: 96vw;
}
</style>
