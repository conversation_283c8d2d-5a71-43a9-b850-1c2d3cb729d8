import device from '@/assets/images/icon/device.png'
import deviceMaintenance from '@/assets/images/icon/device-maintenance.png'
import gateway from '@/assets/images/icon/gateway.png'
// import camera from '@/assets/images/icon/camera.png'
import deviceDashboard from '@/assets/images/icon/device-dashboard.png'
import productionDashboard from '@/assets/images/icon/production-dashboard.png'
import daily from '@/assets/images/icon/daily.png'
import { t } from '@/locale/fanyi'
import material from '@/assets/images/产品物料.png'
import purchase from '@/assets/images/采购申请.png'
import inWarehouse from '@/assets/images/入库.png'
import outWarehouse from '@/assets/images/出库.png'
import stock from '@/assets/images/库存.png'
import bom from '@/assets/images/BOM单.png'
import warehouseSetting from '@/assets/images/仓库设置.png'
import myDevice from '@/assets/images/我的设备.png'
import remoteAssistance from '@/assets/images/远程协助.png'

const menus = {
    menu: [
        {
            text: '工作台',
            i18n: 'menu.main',
            group: true,
            hideInBreadcrumb: true,
            children: [
                {
                    text: '物联设备',
                    i18n: t('menu.equipmentIoT'),
                    icon: 'anticon anticon-dashboard',
                    children: [
                        {
                            text: '设备列表',
                            link: '/pages/workbench/device-list/index',
                            i18n: t('menu.deviceList'),
                            icon: device,
                            guard: { permission: ['PlatformService.FactoryDevice'] },
                        },
                        {
                            text: '我的设备',
                            link: '/pages/workbench/my-device/index',
                            i18n: t('menu.myDevice'),
                            icon: myDevice,
                            guard: { permission: ['PlatformService.FactoryDevice'] },
                        },
                        {
                            text: '设备维护',
                            link: '/pages/workbench/device-maintain/index',
                            i18n: t('menu.deviceMaintenance'),
                            icon: deviceMaintenance,
                            guard: { permission: ['PlatformService.FactoryDevice'] },
                        },
                        {
                            text: '物联网关',
                            link: '/pages/workbench/device-iot/index',
                            i18n: t('menu.iotDevice'),
                            icon: gateway,
                            guard: { permission: ['IotService.IotHubDevice'] },
                        },
                        // {
                        //   text: '视频监控',
                        //   link: '/pages/workbench/factory-camera/index',
                        //   i18n: 'Enterprise.FactoryCamera',
                        //   icon: camera
                        // },
                        {
                            text: '远程协助',
                            link: '/pages/subpackages/remote-assistance/index',
                            i18n: t('menu.remoteAssistance'),
                            icon: remoteAssistance,
                        },
                    ],
                },
                {
                    text: '生产报表',
                    i18n: t('menu.productionReport'),
                    icon: 'anticon anticon-dashboard',
                    children: [
                        {
                            text: '设备看板',
                            link: '/pages/workbench/device-board/index',
                            i18n: t('menu.deviceKanban'),
                            icon: deviceDashboard,
                            guard: { permission: ['PlatformService.FactoryDevice'] },
                        },
                        {
                            text: '生产看板',
                            link: '/pages/workbench/production-board/index',
                            i18n: t('menu.productionKanban'),
                            icon: productionDashboard,
                            guard: { permission: ['PlatformService.FactoryDevice'] },
                        },
                        {
                            text: '生产日报',
                            link: '/pages/workbench/production-daily/index',
                            i18n: t('menu.productionDaily'),
                            icon: daily,
                            guard: { permission: ['PlatformService.FactoryDevice'] },
                        },
                    ],
                },
                {
                    text: '仓储管理',
                    i18n: t('menu.warehouseManagement'),
                    icon: 'anticon anticon-dashboard',
                    children: [
                        {
                            text: '仓库设置',
                            link: '/pages/workbench/warehouse-management/index',
                            i18n: t('menu.warehouseSetup'),
                            icon: warehouseSetting,
                            guard: { permission: ['EnterpriseService.Wms.WmWarehouse'] },
                        },
                        {
                            text: '物料管理',
                            link: '/pages/subpackages/material-management/index',
                            i18n: t('menu.materialManagement'),
                            icon: material,
                            guard: { permission: ['EnterpriseService.Wms.WmMaterial'] },
                        },
                        {
                            text: '采购管理',
                            link: '/pages/workbench/purchase-management/index',
                            i18n: t('menu.purchaseManagement'),
                            icon: purchase,
                            guard: { permission: ['EnterpriseService.Wms.PurchaseOrder'] },
                        },
                        {
                            text: '入库管理',
                            link: '/pages/subpackages/in-warehouse-management/index',
                            i18n: t('menu.inWarehouseManagement'),
                            icon: inWarehouse,
                            guard: { permission: ['EnterpriseService.Wms.WmInWarehouseBill'] },
                        },
                        {
                            text: '出库管理',
                            link: '/pages/subpackages/out-warehouse-management/index',
                            i18n: t('menu.outWarehouseManagement'),
                            icon: outWarehouse,
                            guard: { permission: ['EnterpriseService.Wms.WmOutWarehouseBill'] },
                        },
                        {
                            text: '库存管理',
                            link: '/pages/workbench/stock-management/index',
                            i18n: t('menu.stockManagement'),
                            icon: stock,
                            guard: { permission: ['EnterpriseService.Wms.WmStockQuantity'] },
                        },
                        {
                            text: 'BOM表',
                            link: '/pages/workbench/bom-table/index',
                            i18n: t('menu.bomTable'),
                            icon: bom,
                            guard: { permission: ['EnterpriseService.Wms.WmProductBom'] },
                        },
                    ],
                },
            ],
        },
    ],
}

export default menus
