<template>
    <view>
        <DetailsTable :detailsList="detailsList" :supplierList="supplierList" />
    </view>
</template>

<script setup lang="ts">
import Taro, { useDidShow } from '@tarojs/taro'
import { WmMaterialService } from '@/api/proxy/enterprise/controller/wm-material.service'
import { WmMeasureUnitService } from '@/api/proxy/enterprise/controller/wm-measure-unit.service'
import DetailsTable from './component/material-details-table.vue'
import { ref } from 'vue'
import { t } from '@/locale/fanyi'
import { WmMaterialDetailDto } from '@/api/proxy/enterprise/wms/dtos'
import { NodeSelectDataDto } from '@/api/proxy/helper/shared/models'

/**
 * @description 修改导航栏标题
 */
Taro.setNavigationBarTitle({ title: t('menu.materialDetail') })

const wmMaterialService = new WmMaterialService()

const detailsList = ref<WmMaterialDetailDto>({
    name: '',
    encode: '',
    model: '',
    specification: '',
    describe: '',
    type: '',
    isActivate: false,
    safeStock: '',
    minStock: 0,
    maxStock: 0,
    highValue: '',
    status: '',
    measureUnitId: '',
    categoryId: '',
    supplierId: '',
    unitName: '',
    categoryName: '',
    supplierName: '',
    creatorId: '',
    creationTime: '',
    lastModifierId: '',
    lastModificationTime: '',
    extraProperties: {},
    concurrencyStamp: '',
    id: '',
    isDeleted: false,
    measureUnitName: '',
} as WmMaterialDetailDto)

const wmMeasureUnitService = new WmMeasureUnitService()

// 创建一个单独的供应商列表引用
const supplierList = ref<NodeSelectDataDto[]>([])

/**
 * @description 获取数据
 */
const fetchData = async () => {
    const id = Taro.useRouter().params.id
    try {
        const result = await wmMaterialService.getById(id || '')
        detailsList.value = result
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

/**
 * @description 获取单位
 */
const fetchUnit = async () => {
    try {
        // 获取路由参数中的 measureUnitId
        const measureUnitId = Taro.useRouter().params.measureUnitId

        // 优先使用路由参数中的 measureUnitId
        if (measureUnitId) {
            const result = await wmMeasureUnitService.getById(measureUnitId)
            // 安全地设置单位名称
            if (result) {
                if (typeof result === 'object') {
                    if (Array.isArray(result) && result.length > 0 && result[0].label) {
                        detailsList.value.unitName = result[0].label
                    } else if (result.name) {
                        detailsList.value.unitName = result.name
                    }
                }
            }
        }
        // 如果路由参数中没有 measureUnitId，则尝试从物料详情中获取
        else if (detailsList.value && detailsList.value.measureUnitId) {
            const backupId = detailsList.value.measureUnitId
            const result = await wmMeasureUnitService.getById(backupId)
            // 安全地设置单位名称
            if (result) {
                if (typeof result === 'object') {
                    if (Array.isArray(result) && result.length > 0 && result[0].label) {
                        detailsList.value.unitName = result[0].label
                    } else if (result.name) {
                        detailsList.value.unitName = result.name
                    }
                }
            }
        } else {
            console.warn('未找到计量单位ID')
        }
    } catch (error) {
        console.error('获取单位数据错误:', error.response ? error.response.data : error.message)
    }
}

/**
 * @description 获取类型
 */
const fetchType = async () => {
    try {
        const result = await wmMaterialService.getEditor({ id: '' })
        if (result && result.materialsTypeEnum) {
            const matchedItem = result.materialsTypeEnum.find(item => item.value.includes(detailsList.value.type))
            if (matchedItem) {
                detailsList.value.type = matchedItem.key // 重新赋值为对应的 key
            }
        }
    } catch (error) {
        console.error('获取类型数据错误:', error.response ? error.response.data : error.message)
    }
}

/**
 * @description 获取供应商信息
 */
const fetchSupplier = async () => {
    const id = Taro.useRouter().params.id
    try {
        const result = await wmMaterialService.getSuppliersOptionItems(id || '')
        if (result && Array.isArray(result) && result.length > 0) {
            // 设置供应商名称 (保留原来的单个供应商名称，用于兼容)
            detailsList.value.supplierName = result[0].label || ''
            // 保存所有供应商信息到单独的引用中
            supplierList.value = result
        }
    } catch (error) {
        console.error('获取供应商数据错误:', error.response ? error.response.data : error.message)
    }
}

/**
 * @description 页面显示时钩子
 */
useDidShow(async () => {
    // 先获取物料详情数据
    await fetchData()
    // 获取详情数据后，再获取单位和类型信息
    await fetchUnit()
    await fetchType()
    await fetchSupplier()
})
</script>

<style lang="scss" scoped></style>
