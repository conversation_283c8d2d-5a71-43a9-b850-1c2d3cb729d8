<template>
    <nut-config-provider :theme="theme">
        <view>
            <nut-cell @click="handleNoLogin">
                <view class="user-info-box">
                    <view class="g-flex-centen" style="display: flex">
                        <nut-avatar size="normal">
                            <img
                                src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/38/8105/14329/60c806a4Ed506298a/e6de9fb7b8490f38.png"
                            />
                        </nut-avatar>
                        <view class="g-title account" style="margin-top: 10px; margin-left: 10px">
                            <template v-if="loading"> <Loading size="14"></Loading> {{ $t('ui.loading') }} </template>
                            <template v-else>
                                {{ accountName }}
                            </template>
                        </view>
                    </view>
                    <view class="g-flex-centen">
                        <nut-space>
                            <nut-tag
                                class="account-tag"
                                style="margin-top: 10px"
                                v-for="role in roles"
                                :key="role"
                                type="success"
                            >
                                {{ role }}
                            </nut-tag>
                        </nut-space>
                    </view>
                </view>
            </nut-cell>
            <view>
                <nut-cell center @click="handleProfileSetting">
                    <template v-slot:title>
                        <view class="setting-cell-title">
                            <My2></My2>
                            <span>{{ $t('ui.personal') }}</span>
                        </view>
                    </template>
                    <template v-slot:link>
                        <Right></Right>
                    </template>
                </nut-cell>
                <nut-cell center :desc="currentLanguage" @click="show = true">
                    <template v-slot:title>
                        <view class="setting-cell-title">
                            <Comment></Comment>
                            <span>{{ $t('ui.language') }}</span>
                        </view>
                    </template>
                </nut-cell>
                <nut-cell center @click="aboutUsClick">
                    <template v-slot:title>
                        <view class="setting-cell-title">
                            <People></People>
                            <span>{{ $t('ui.aboutUs') }}</span>
                        </view>
                    </template>
                    <template v-slot:link>
                        <Right></Right>
                    </template>
                </nut-cell>
                <nut-cell center @click="sysSettingClick">
                    <template v-slot:title>
                        <view class="setting-cell-title">
                            <Setting></Setting>
                            <span>{{ $t('ui.setting') }}</span>
                        </view>
                    </template>
                    <template v-slot:link>
                        <Right></Right>
                    </template>
                </nut-cell>
            </view>
        </view>
        <nut-button type="primary" class="logout-btn" @click="handleClick" v-if="isLogin">{{
            $t('ui.logout')
        }}</nut-button>
    </nut-config-provider>
    <!-- 语言列表选择器 -->
    <view>
        <nut-popup position="bottom" v-model:visible="show">
            <nut-picker
                v-model="langPicker"
                :columns="languageList"
                :cancel-text="$t('ui.cancel')"
                :ok-text="$t('ui.confirm')"
                :title="$t('ui.selectLanguage')"
                @confirm="languagePickerConfirm"
                @cancel="show = false"
                :field-names="{
                    text: 'displayName',
                    value: 'cultureName',
                }"
            >
            </nut-picker>
        </nut-popup>
    </view>
    <SettingProfile ref="personalInfo" :title="$t('ui.personal')" />
    <!-- <BaseModal @register="register"></BaseModal> -->
    <nut-dialog
        :content="$t('ui.reloadApp')"
        v-model:visible="showReloadDialog"
        @cancel="showReloadDialog = false"
        @ok="handleReloadMiniApp"
        :cancel-text="$t('Helper.Cancel')"
        :ok-text="$t('Helper.Confirm')"
    />
    <!-- 退出确认对话框 -->
    <nut-dialog
        :content="$t('ui.logoutConfirm')"
        v-model:visible="showLogoutDialog"
        @cancel="showLogoutDialog = false"
        @ok="handleConfirmLogout"
        :cancel-text="$t('ui.cancel')"
        :ok-text="$t('ui.confirm')"
    />
    <!-- 关于我们 -->
    <view>
        <nut-popup v-model:visible="showBottom" position="bottom" closeable round :style="{ height: '70%' }">
            <nut-navbar :title="$t('ui.aboutUs')"></nut-navbar>
            <view style="font-size: 13px; padding: 5px; margin: 5px; letter-spacing: 1px; user-select: text">
                <view>
                    络特拉科技是一家专注于数字化工业领域的高科技企业，致力于为制造业、能源、物流等行业提供创新的数字化解决方案。我们通过融合先进技术与行业经验，推动产业升级与智能化发展。
                </view>
                <view style="margin-top: 10px">
                    以数字化技术为核心，络特拉科技研发了覆盖云计算、大数据、物联网及人工智能等领域的创新产品与服务，帮助客户实现高效生产、智能决策和业务增长。
                </view>
                <view style="margin-top: 10px">
                    我们的核心团队由经验丰富的技术专家和行业精英组成，覆盖多个领域，包括软件开发、云计算、大数据、人工智能等。凭借不断创新的精神和客户至上的理念，我们为全球范围内的企业提供定制化解决方案，助力其实现业务增长与可持续发展。
                </view>
                <view style="margin-top: 10px">
                    未来，络特拉科技将继续引领科技创新，以技术赋能产业，助推数字化经济的发展，为客户创造更大价值。
                </view>
                <view style="margin-top: 10px">
                    {{ $t('text.contactUs') }}：
                    <view style="margin-top: 10px">{{ $t('text.officialWebsite') }}：https://www.ioterra.net</view>
                    <view style="margin-top: 10px"> {{ $t('text.address') }}：广东省佛山市顺德区 </view>
                    <view style="margin-top: 10px"> {{ $t('text.email') }}：<EMAIL> </view>
                </view>
            </view>
        </nut-popup>
    </view>
</template>

<script lang="ts" setup>
import { useDidShow } from '@/hooks/component/hooks'
import { usePermissionStore } from '@/stores/modules/permission'
import { useUserStore } from '@/stores/modules/user'
import { Right, My2, Setting, Comment, People, Loading } from '@nutui/icons-vue-taro'
import Taro from '@tarojs/taro'
import { ref } from 'vue'
import { LocaleType, changeLocale } from '@/locale/useLocale'
import { LanguagesMenus, useLocaleStore } from '@/stores/modules/locale'
import { computed } from '@vue/reactivity'
import SettingProfile from '@/pages/profile/components/SettingProfile.vue'
import { t } from '@/locale/fanyi'
import { getCache } from '@/utils/cache'
import { APP_LOCAL_KEY } from '@/enums/cacheEnum'
import { initializeApp } from '@/hooks/web/initApp'
import { getAppToken } from '@/utils/getAppInfo'
import { useAuthStore } from '@/stores/modules/auth'
import { LOGIN_PAGE } from '@/enums/routerEnum'
import { getAbpAppConfig } from '@/api/app/appConfig'

const permissionStore = usePermissionStore()
const userStore = useUserStore()
const authStore = useAuthStore()
const isLogin = getAppToken()
const accountName = ref<string>(t('ui.notLogin'))
const personalInfo = ref<any>(null)
const roles = ref<string[]>([])
const theme = ref<string>('light')
const languageList = ref<LanguagesMenus[]>([])
const show = ref<boolean>(false)
const langPicker = ref<string[]>([])
const showReloadDialog = ref<boolean>(false)
const showLogoutDialog = ref<boolean>(false)
const showBottom = ref<boolean>(false)
const loading = ref<boolean>(true)

/**
 * @description 设置页面标题
 */
Taro.setNavigationBarTitle({ title: t('menu.my') })

/**
 * 当前语言
 */
const currentLanguage = computed(() => {
    let locale = useLocaleStore().getLocale
    langPicker.value = [locale]
    const langauge = languageList.value.find(item => item.cultureName == locale)?.displayName
    return langauge ?? t('ui.loading')
})

/**
 * @description 系统设置
 */
const sysSettingClick = () => {
    Taro.navigateTo({
        url: '/pages/subpackages/sys-setting/index',
    })
}

/**
 * @description 处理个人设置
 */
const handleProfileSetting = () => {
    personalInfo.value.showDialog()
}

/**
 * @description 获取语言列表
 */
const getLanguageList = () => {
    if (useLocaleStore().getLanguagesMenus.length === 0) {
        initializeApp()
    }
    languageList.value = useLocaleStore().getLanguagesMenus
    // const languageService = new LanguageService();
    // const input: GetLanguagesTextsInput = {
    //   getOnlyEmptyValues: false,
    //   filter: '',
    //   skipCount: 0,
    //   maxResultCount: 10,
    //   sorting: 'CreationTime DESC'
    // };
    // languageService.getList(input).then(res => {
    //   data.languageList = res.items.map(item => {
    //     return {
    //       text: item.displayName,
    //       value: item.displayName as string
    //     };
    //   });
    // });
}

/**
 * @description 语言选择器确认事件
 */
const languagePickerConfirm = (e: any) => {
    const locale = e.selectedValue[0]
    const storageLocale = getCache<string>(APP_LOCAL_KEY) as LocaleType
    show.value = false
    if (locale === storageLocale) {
        return
    }
    changeLocale(locale as LocaleType)
    showReloadDialog.value = true
}

const handleNoLogin = () => {
    if (!isLogin) {
        Taro.reLaunch({
            url: '/pages/subpackages/login/index',
        })
    }
}

/**
 * @description 关于我们
 */

const aboutUsClick = () => {
    showBottom.value = true
}

/**
 * @description 退出登录
 */
const handleClick = () => {
    showLogoutDialog.value = true
}

/**
 * @description 确认退出登录
 */
const handleConfirmLogout = () => {
    authStore.logout()
    Taro.reLaunch({ url: LOGIN_PAGE })
}

/**
 * @description 重新加载小程序
 */
const handleReloadMiniApp = () => {
    //退出小程序
    // Taro.exitMiniProgram()

    // 此方法需要发布的小程序才可以跳转
    // Taro.navigateToMiniProgram({shortLink: '#小程序://络特拉科技/wWyQWzkY2pTVkyx'})

    // 此方法这里报错，实际编译到【微信小程序】可以正常使用
    ;(Taro as any).restartMiniProgram({ path: '/pages/home/<USER>' })
}

/**
 * 处理token无效（有token但用户未认证）的情况
 */
const handleTokenInvalid = () => {
    // 执行登出操作，清除所有状态
    authStore.logout()
    // 更新界面状态
    accountName.value = t('ui.notLogin')
    roles.value = []
    loading.value = false
    // 显示提示
    Taro.showToast({
        title: '登录已失效，请重新登录',
        icon: 'none',
        duration: 2000,
    })
    // 2秒后跳转到登录页
    setTimeout(() => {
        Taro.reLaunch({ url: LOGIN_PAGE })
    }, 2000)
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    // 检查登录状态
    loading.value = true

    if (isLogin) {
        const userInfo = userStore.getUserInfo

        // 检查用户是否已认证
        if (userInfo && userInfo.isAuthenticated === false) {
            handleTokenInvalid()
            return
        }

        if (!userInfo || !userInfo.userName) {
            // 如果已登录但用户信息不存在或不完整，重新获取
            getAbpAppConfig()
                .then(() => {
                    // 获取新的用户信息
                    const updatedUserInfo = userStore.getUserInfo

                    // 检查更新后的用户是否已认证
                    if (updatedUserInfo && updatedUserInfo.isAuthenticated === false) {
                        handleTokenInvalid()
                        return
                    }

                    accountName.value = updatedUserInfo?.userName ?? t('ui.notLogin')
                    roles.value = permissionStore.getRoles
                })
                .catch(() => {
                    // 获取失败时仍显示未登录
                    accountName.value = t('ui.notLogin')
                    roles.value = []
                })
                .finally(() => {
                    loading.value = false
                })
        } else {
            // 用户信息正常，直接使用
            accountName.value = userInfo.userName
            roles.value = permissionStore.getRoles
            loading.value = false
        }
    } else {
        // 未登录状态
        accountName.value = t('ui.notLogin')
        roles.value = []
        loading.value = false
    }

    // 获取语言列表
    getLanguageList()
})
</script>

<style lang="scss">
.user-info-box {
    display: flex;

    // align-items: center;
    .g-flex-centen {
        padding: 0 10px;
    }
}

.setting-cell-title {
    display: flex;
    align-items: center;

    span {
        margin-left: 8px;
    }
}

.logout-btn {
    width: 50%;
    margin: 50px auto;
    display: block;
}
</style>
