import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { GetForEditInput, NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetWmWarehouseLocationForEditorOutput,
    GetWmWarehouseLocationInput,
    WmWarehouseLocationCreateDto,
    WmWarehouseLocationDetailDto,
    WmWarehouseLocationEditDto,
    WmWarehouseLocationListDto,
} from '../wms/dtos/models'

export class WmWarehouseLocationService {
    apiName = 'EnterpriseService'

    create = (input: WmWarehouseLocationCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-warehouse-location',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-warehouse-location/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmWarehouseLocationDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-warehouse-location/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmWarehouseLocationDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse-location/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmWarehouseLocationForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse-location/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse-location/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetWmWarehouseLocationInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmWarehouseLocationListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-warehouse-location',
                params: {
                    areaId: input.areaId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-warehouse-location/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    update = (input: WmWarehouseLocationEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-warehouse-location',
                data: input,
            },
            options,
        )
}
