// import { useI18n } from 'vue-i18n'
// import { Toast } from '../../uniapi/prompt'
// import type { ErrorMessageMode } from 'types/axios'
// import { useAuthStoreWithOut } from '/@/state/modules/auth'

import { t } from '@/locale/fanyi'
import { useAuthStore } from '@/stores/modules/auth'
import Taro from '@tarojs/taro'

export function checkStatus(status: number, data: any): Promise<string> {
    if (status >= 200 && status <= 300) {
        return Promise.resolve('')
    }
    const authStore = useAuthStore()
    const errMessage = `Msg${status}:${data.error ? data.error : ''}`
    switch (status) {
        case 400:
            // errMessage = t(`Msg403${errMessage}`);
            break
        // 401: Not logged in
        // Jump to the login page if not logged in, and carry the path of the current page
        // Return to the current page after successful login. This step needs to be operated on the login page.
        case 401:
            authStore.logout()
            Taro.showModal({
                title: '提示',
                content: '登录过期，需要重新登录',
                success: function (res) {
                    if (res.confirm) {
                        Taro.navigateTo({
                            url: '/pages/passport/login/index',
                        })
                    } else if (res.cancel) {
                        // console.log('用户点击取消');
                    }
                },
            })
            break
        case 403:
            break
        // 404请求不存在
        case 404:
            break
        case 405:
            break
        case 408:
            break
        case 500:
            break
        case 501:
            break
        case 502:
            break
        case 503:
            break
        case 504:
            break
        case 505:
            break
        default:
    }
    Taro.showToast({
        title: errMessage,
        icon: 'error',
        duration: 2000,
    })
    throw errMessage
    // return Promise.reject(errMessage);
    // if (errMessage) {
    //   if (errorMessageMode === 'modal') {
    //     createErrorModal({ title: t('sys.api.errorTip'), content: errMessage });
    //   } else if (errorMessageMode === 'message') {
    //     error({ content: errMessage, key: `global_error_message_status_${status}` });
    //   }
    // }
}

// export function checkResponse(response: any): string | undefined {
//   if (!response?.data) {
//     // 都没捕获到则提示默认错误信息
//     // const { t } = useI18n()
//     // const message = t('sys.api.apiRequestFailed')
//     const message = 'sys.api.apiRequestFailed';
//     // checkStatus(response.status, message);
//     return message;
//   }

//   // 会话超时
//   // if (response.status === 401) {
//   //   const authStore = useAuthStoreWithOut()
//   //   authStore.loginOut().then(() => {
//   //     uni.reLaunch({
//   //       url: '/pages/login/wechat/applets/index'
//   //     })
//   //   })
//   //   const { t } = useI18n()
//   //   return t('sys.api.errMsg401')
//   // }

//   // let errorJson = response.data.error;
//   // abp框架抛出异常信息
//   // if (response.headers['_abperrorformat'] === 'true') {
//   //   if (errorJson === undefined && response.data.type === 'application/json') {
//   //     const reader = new FileReader();
//   //     reader.onload = function (e) {
//   //       errorJson = JSON.parse(e.target?.result as string);
//   //       console.log(errorJson);
//   //       console.log(errorJson.error.message);
//   //       // error(errorJson.error.message);
//   //     };
//   //     reader.readAsText(response.data);
//   //   } else {
//   //     let errorMessage = errorJson.message;
//   //     if (errorJson.validationErrors) {
//   //       errorMessage += errorJson.validationErrors.map((v) => v.message).join('\n');
//   //     }
//   //     // error(errorMessage);
//   //     console.log(errorMessage);
//   //     return errorMessage;
//   //   }
//   // }

//   // oauth错误信息
//   // if (response.data.error_description) {
//   //   // error(response.data.error_description);
//   //   console.log(response.data.error_description);
//   //   return response.data.error_description;
//   // }
//   // 其他错误
//   const details = response.data?.error?.details;
//   if (details) {
//     return details;
//   }

//   if (response.data.error?.message) {
//     // error(response.data.error.message);
//     return response.data.error.message;
//   }

//   return undefined;
// }
