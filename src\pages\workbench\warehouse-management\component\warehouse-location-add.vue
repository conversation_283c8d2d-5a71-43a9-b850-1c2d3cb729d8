<template>
    <view>
        <nut-navbar>
            <template #left>
                <view style="font-size: 16px; color: black">{{ t('text.add') }}</view>
            </template>
        </nut-navbar>
        <nut-form>
            <nut-form-item :label="t('text.name')">
                <nut-input v-model="addForm.name" type="text" :placeholder="t('text.pleaseEnterName')" />
            </nut-form-item>
            <nut-form-item :label="t('text.encode')">
                <nut-input v-model="addForm.encode" type="text" :placeholder="t('text.pleaseEnterEncode')" />
            </nut-form-item>
            <nut-form-item :label="t('text.location')">
                <nut-input v-model="addForm.location" type="text" :placeholder="t('text.pleaseEnterLocation')" />
            </nut-form-item>
            <nut-form-item :label="t('text.areaSize')">
                <nut-input-number v-model="addForm.areaSize" :button-size="30" :min="0" :max="10000" />
            </nut-form-item>
            <nut-form-item :label="t('text.maxLoad')">
                <nut-input-number v-model="addForm.maxLoad" :button-size="30" :min="0" :max="10000" />
            </nut-form-item>
            <nut-form-item :label="t('text.positionX')">
                <nut-input-number v-model="addForm.positionX" :button-size="30" :min="0" :max="1000" />
            </nut-form-item>
            <nut-form-item :label="t('text.positionY')">
                <nut-input-number v-model="addForm.positionY" :button-size="30" :min="0" :max="1000" />
            </nut-form-item>
            <nut-form-item :label="t('text.positionZ')">
                <nut-input-number v-model="addForm.positionZ" :button-size="30" :min="0" :max="1000" />
            </nut-form-item>
            <nut-form-item :label="t('text.dutyUser')">
                <view @click="showDutyUser = true">
                    <text>{{
                        addForm.dutyUserInfo?.surname || addForm.dutyUserInfo?.name || addForm.dutyUserInfo?.email
                            ? (addForm.dutyUserInfo?.surname || '') + (addForm.dutyUserInfo?.name || '') ||
                              addForm.dutyUserInfo?.email
                            : t('text.pleaseSelectDutyUser')
                    }}</text>
                </view>
            </nut-form-item>
            <nut-form-item :label="t('text.isActivate')">
                <nut-switch v-model="addForm.isActivate" />
            </nut-form-item>
            <nut-form-item :label="t('text.describe')">
                <nut-textarea v-model="addForm.describe" limit-show max-length="200" />
            </nut-form-item>
            <nut-space direction="vertical" fill v-show="!showDutyUser">
                <nut-button style="width: 100%" @click="emit('update:addVisible', false)">{{
                    t('ui.cancel')
                }}</nut-button>
                <nut-button type="primary" style="width: 100%" @click="confirmAdd">{{ t('ui.submit') }}</nut-button>
            </nut-space>
        </nut-form>
    </view>
    <nut-popup v-model:visible="showDutyUser" position="bottom" round :style="{ height: '50%' }">
        <nut-picker
            :columns="userList"
            :title="t('text.pleaseSelectDutyUser')"
            @confirm="confirmSelectUser"
            @cancel="showDutyUser = false"
        />
    </nut-popup>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import { ref, watch } from 'vue'
import { WmWarehouseLocationService } from '@/api/proxy/enterprise/controller/wm-warehouse-location.service'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { IdentityUserDto } from '@/api/proxy/identity-user-service/models'
import { WmWarehouseLocationCreateDto } from '@/api/proxy/enterprise/wms/dtos/models'

// 添加默认导出
defineOptions({
    name: 'WarehouseLocationAdd',
})

Taro.setNavigationBarTitle({ title: t('menu.addWarehouseLocation') })

const props = defineProps<{
    addVisible: boolean
    areaId?: string
}>()

const emit = defineEmits<{
    (e: 'update:addVisible', value: boolean): void
    (e: 'refresh'): void
}>()

const addVisible = ref(props.addVisible)
const wmWarehouseLocationService = new WmWarehouseLocationService()
const identityUserService = new IdentityUserService()
const showDutyUser = ref(false)
const userList = ref<Array<{ text: string; value: string; id: string }>>([])

interface AddForm {
    name: string
    encode: string
    location?: string
    areaSize: number
    maxLoad: number
    positionX: number
    positionY: number
    positionZ: number
    dutyUserId?: string
    dutyUserInfo?: IdentityUserDto
    isActivate: boolean
    describe?: string
    areaId?: string
}

const addForm = ref<AddForm>({
    name: '',
    encode: '',
    location: '',
    areaSize: 0,
    maxLoad: 0,
    positionX: 0,
    positionY: 0,
    positionZ: 0,
    dutyUserId: '',
    dutyUserInfo: {} as IdentityUserDto,
    isActivate: false,
    describe: '',
    areaId: props.areaId || '',
})

const initForm = () => {
    addForm.value = {
        name: '',
        encode: '',
        location: '',
        areaSize: 0,
        maxLoad: 0,
        positionX: 0,
        positionY: 0,
        positionZ: 0,
        dutyUserId: '',
        dutyUserInfo: {} as IdentityUserDto,
        isActivate: false,
        describe: '',
        areaId: props.areaId || '',
    }
}

const fetchUserList = async () => {
    try {
        const defaultParams = {
            skipCount: 0,
            maxResultCount: 100,
            filterText: '',
            sorting: 'userName',
        }
        const response = await identityUserService.getList(defaultParams)
        if (response && Array.isArray(response.items)) {
            userList.value = response.items.map(item => ({
                text: item.userName + '：' + (item.surname || '') + (item.name || ''),
                value: item.id,
                id: item.id,
            }))
        } else {
            console.error('获取用户列表出错:', response)
        }
    } catch (error) {
        console.error('获取用户列表失败:', error)
    }
}

const confirmSelectUser = ({ selectedOptions }: { selectedOptions: any[] }) => {
    if (selectedOptions && selectedOptions.length > 0) {
        const selectedUser = selectedOptions[0]
        addForm.value.dutyUserId = selectedUser.value
        identityUserService
            .get(selectedUser.value)
            .then(result => {
                addForm.value.dutyUserInfo = result
            })
            .catch(() => {
                const nameParts = selectedUser.text.split('：')
                addForm.value.dutyUserInfo = {
                    id: selectedUser.value,
                    name: nameParts.length > 1 ? nameParts[1] : '',
                    surname: nameParts.length > 1 ? nameParts[0] : '',
                } as IdentityUserDto
            })
    }
    showDutyUser.value = false
}

const confirmAdd = async () => {
    const input: WmWarehouseLocationCreateDto = {
        ...addForm.value,
        isActivate: addForm.value.isActivate || false,
        areaId: props.areaId,
    }

    try {
        await wmWarehouseLocationService.create(input)
        emit('update:addVisible', false)
        emit('refresh')
        Taro.showToast({
            title: t('text.submitSuccess'),
            icon: 'success',
        })
        initForm()
    } catch (error) {
        console.error(error)
    }
}

watch(
    () => props.addVisible,
    newValue => {
        addVisible.value = newValue
        if (newValue) {
            initForm()
            fetchUserList()
        }
    },
    { immediate: true },
)

watch(
    () => showDutyUser.value,
    async newValue => {
        if (newValue) {
            await fetchUserList()
        }
    },
)
</script>

<style lang="scss"></style>
