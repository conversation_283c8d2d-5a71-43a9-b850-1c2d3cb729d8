/// <reference types="@tarojs/taro" />

declare module '*.png';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.styl';

declare namespace NodeJS {
  interface ProcessEnv {
    TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'quickapp' | 'qq' | 'jd';
    // AUTH_URL_HOST: string
  }
}

declare module '@tarojs/components' {
  export * from '@tarojs/components/types/index.vue3';
}

declare const AUTH_URL_HOST: string;
declare const API_URL_HOST: string;
declare const TenantKey: string;
declare const TenantName: string;
