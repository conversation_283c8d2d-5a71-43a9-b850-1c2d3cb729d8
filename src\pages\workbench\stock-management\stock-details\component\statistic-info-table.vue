<template>
    <view class="statistic-info-table">
        <view style="width: 100%; height: 300px" v-show="showCanvas">
            <nut-empty v-if="isFetchError" description="暂无数据" />
            <EChart v-else ref="canvas" style="width: 100%; height: 100%" />
        </view>
        <view class="data-list" v-if="statisticsData">
            <view class="data-item">
                <text class="label">{{ t('text.safeStock') }}:</text>
                <text class="value">{{ statisticsData.safeStock || 0 }}</text>
            </view>
            <view class="data-item">
                <text class="label">{{ t('text.minStock') }}:</text>
                <text class="value">{{ statisticsData.minStock || 0 }}</text>
            </view>
            <view class="data-item">
                <text class="label">{{ t('text.maxStock') }}:</text>
                <text class="value">{{ statisticsData.maxStock || 0 }}</text>
            </view>
            <view class="data-item">
                <text class="label">{{ t('text.currentStock') }}:</text>
                <text class="value">{{ statisticsData.last || 0 }}</text>
            </view>
            <view class="data-item">
                <text class="label">{{ t('text.averageStock') }}:</text>
                <text class="value">{{ statisticsData.average || 0 }}</text>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { WmStockQuantityService } from '@/api/proxy/enterprise/controller/wm-stock-quantity.service'
import { useDidShow } from '@tarojs/taro'
import { ref, watch } from 'vue'
import Taro from '@tarojs/taro'
import EChart from '@/pages/workbench/device-list/data-view/echarts/index'
import { t } from '@/locale/fanyi'
import { HideLoading, Loading, Toast } from '@/utils/Taro-api'
import type { Statistics } from '@/api/proxy/enterprise/wms/dtos/models'

defineOptions({
    name: 'StatisticInfoTable',
})

const props = defineProps<{
    materialId: string
}>()

const wmStockQuantityService = new WmStockQuantityService()
interface StockStatistics extends Statistics {
    safeStock?: string | number
    minStock?: number
    maxStock?: number
    inLogs?: Record<string, any>
    outLogs?: Record<string, any>
    stockQuantityChange?: Record<string, any>
}

const canvas = ref<any>(null)
const chartOption = ref<any>()
const showCanvas = ref<boolean>(true)
const isFetchError = ref<boolean>(false)
const statisticsData = ref<StockStatistics | null>(null)

// 构建图表选项
const buildOption = (data: StockStatistics) => {
    // 处理真实数据
    // 从stockQuantityChange中提取日期和库存值
    const stockQuantityChange = data.stockQuantityChange || {}
    const inLogs = data.inLogs || {}
    const outLogs = data.outLogs || {}
    
    // 获取所有日期并排序
    const allDates = [...new Set([...Object.keys(stockQuantityChange), ...Object.keys(inLogs), ...Object.keys(outLogs)])]
        .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
    
    // 格式化日期显示（MM-DD格式）
    const dates = allDates.map(date => {
        const d = new Date(date)
        return `${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`
    })
    
    // 提取库存数量变化值
    const stockValues = allDates.map(date => stockQuantityChange[date] || 0)
    
    // 设置安全库存、最小库存和最大库存
    const safeStock = Number(data.safeStock) || 0
    const minStock = data.minStock || 0
    const maxStock = data.maxStock || 0
    
    // 确定Y轴最大值，确保所有水平线和数据点都可见
    // 计算数据中的最大值，并确保它至少比最大库存或最大库存数据点大20%
    const dataMax = Math.max(...stockValues, maxStock, minStock, safeStock)
    const maxValue = Math.ceil(dataMax * 1.2)

    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985',
                },
            },
            formatter: function (params: any[]) {
                const stockItem = params.find(item => item.seriesName === t('text.stockQuantity'))
                if (stockItem) {
                    return `${stockItem.marker}${stockItem.seriesName}: ${stockItem.value}`
                }
                return ''
            },
        },
        legend: {
            data: [t('text.stockQuantity'), t('text.maxStock'), t('text.minStock'), t('text.safeStock')],
            right: '5%',
            top: '5%',
            itemWidth: 15,
            itemHeight: 10,
            textStyle: {
                fontSize: 12,
            },
        },
        grid: {
            left: '3%',
            right: '10%',
            bottom: '3%',
            top: '70px',
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: dates,
            axisLabel: {
                interval: 0,
                align: 'center',
                margin: 8,
                fontSize: 12,
            },
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: maxValue,
            axisLabel: {
                formatter: (value: number) => value.toFixed(0),
            },
            splitLine: {
                lineStyle: {
                    type: 'dashed',
                },
            },
        },
        series: [
            {
                name: t('text.stockQuantity'),
                type: 'line',
                data: stockValues,
                smooth: true,
                symbol: 'circle',
                symbolSize: 8, // 增大点的大小，使其更明显
                itemStyle: {
                    color: '#3A4DE9',
                },
                lineStyle: {
                    width: 3, // 增大线的宽度，使其更明显
                    shadowBlur: 5,
                    shadowColor: 'rgba(0,0,0,0.3)',
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: 'rgba(58, 77, 233, 0.8)' },
                            { offset: 1, color: 'rgba(58, 77, 233, 0.1)' },
                        ],
                    },
                },
                emphasis: {
                    itemStyle: {
                        color: '#ff0000',
                        borderWidth: 3,
                    },
                },
            },
            {
                name: t('text.maxStock'),
                type: 'line',
                symbol: 'none',
                itemStyle: {
                    color: '#5AD8A6',
                },
                lineStyle: {
                    color: '#5AD8A6',
                    width: 2,
                    type: 'dashed',
                },
                data: new Array(dates.length).fill(maxStock),
            },
            {
                name: t('text.minStock'),
                type: 'line',
                symbol: 'none',
                itemStyle: {
                    color: '#FFA39E',
                },
                lineStyle: {
                    color: '#FFA39E',
                    width: 2,
                    type: 'dashed',
                },
                data: new Array(dates.length).fill(minStock),
            },
            {
                name: t('text.safeStock'),
                type: 'line',
                symbol: 'none',
                itemStyle: {
                    color: '#FF6B3B',
                },
                lineStyle: {
                    color: '#FF6B3B',
                    width: 2,
                    type: 'dashed',
                },
                data: new Array(dates.length).fill(safeStock),
            },
        ],
    }

    return option
}

// 渲染图表
const renderChart = () => {
    const echartComponentInstance: any = canvas.value // 组件实例
    Taro.nextTick(() => {
        if (!echartComponentInstance) return
        // 初始化图表
        echartComponentInstance.refresh(chartOption.value).then(myChart => {
            myChart.showLoading()
            /** 更新图表数据 */
            setTimeout(() => {
                myChart.setOption(chartOption.value)
                myChart.hideLoading()
            }, 500)
        })
    })
}

const fetchData = async () => {
    try {
        if (!props.materialId) {
            console.warn('materialId为空，无法获取统计数据')
            return
        }
        Loading()
        const result = await wmStockQuantityService.statistics(props.materialId)

        if (result) {
            // 确保数据格式正确
            const processedResult = {
                ...result,
                // 确保这些字段存在，即使API没有返回
                safeStock: result.safeStock || '0',
                minStock: result.minStock || 0,
                maxStock: result.maxStock || 0,
                last: result.last || 0,
                inLogs: result.inLogs || {},
                outLogs: result.outLogs || {},
                stockQuantityChange: result.stockQuantityChange || {},
            }

            statisticsData.value = processedResult
            chartOption.value = buildOption(processedResult)
            renderChart()
        }
    } catch (error) {
        console.error('获取库存统计信息失败:', error)
        isFetchError.value = true
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

useDidShow(() => {
    if (props.materialId) {
        fetchData()
    }
})

watch(
    () => props.materialId,
    newVal => {
        if (newVal) {
            fetchData()
        }
    },
    { immediate: true },
)
</script>

<style lang="scss">
.statistic-info-table {
    padding: 16px;

    .data-list {
        margin-top: 16px;
        padding: 16px;
        background-color: #fff;
        border-radius: 8px;

        .data-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;

            &:last-child {
                border-bottom: none;
            }

            .label {
                color: #606266;
                font-size: 26px;
            }

            .value {
                color: #303133;
                font-size: 26px;
                font-weight: 500;
            }
        }
    }
}
</style>
