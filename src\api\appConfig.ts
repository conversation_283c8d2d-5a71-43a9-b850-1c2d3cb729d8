import { AbpApplicationConfigurationService, CurrentUserDto } from '@/api/application-configurations';
import { CurrentTenantDto } from '@/api/multi-tenancy';
import { setLocaleMessage } from '@/locale/useLocale';
import { useLocaleStore } from '@/stores/modules/locale';
import { usePermissionStore } from '@/stores/modules/permission';
import { useUserStore } from '@/stores/modules/user';

/**
 * 获取app配置
 */
export function getAbpAppConfig(): Promise<boolean> {
  const AbpAppConfigService = new AbpApplicationConfigurationService();
  // 如果用户重新登录时，显示加载中，如果不是登录登录则不显示
  return AbpAppConfigService.get()
    .then(res => {
      // 保存权限
      savePermission(res.auth.grantedPolicies, res.currentUser.roles);
      // 保存租户信息
      saveTenantInfo(res.currentTenant);
      // 保存用户信息
      saveUserInfo(res.currentUser);
      //保存本地化词法环境
      saveLocale(res.localization);
      return true;
    })
    .catch(() => {
      return Promise.reject(false);
    });
}

//保存授权信息
function savePermission(grantedPolicies: Record<string, boolean>, roles: string[]) {
  const usePermission = usePermissionStore();
  // 保存权限
  const policies: string[] = [];
  if (grantedPolicies) {
    Object.keys(grantedPolicies).forEach((key: any) => {
      if (grantedPolicies[key]) {
        policies.push(key);
      }
    });
  }
  usePermission.setGrantedPolicies(policies);
  // 保存角色
  usePermission.setRoles(roles);
}
/**
 * 保存租户信息
 * @param currentTenant
 */
function saveTenantInfo(currentTenant: CurrentTenantDto) {
  // 保存租户已在用户点击登录前实现
}

/**
 * 保存用户信息
 * @param currentUser
 */
function saveUserInfo(currentUser: CurrentUserDto) {
  const userStore = useUserStore();
  const { email, userName, surName, name, id } = currentUser;
  // 保存用户信息
  email && userStore.setEmail(email);
  userName && userStore.setUserName(userName);
  surName && name && userStore.setFullName(surName + name);
  id && userStore.setId(id);
}

/**
 * 保存本地化词法环境
 */
function saveLocale(localization: any) {
  const { AftersaleService } = localization.values;
  const message = { ...AftersaleService };
  const localeStore = useLocaleStore();
  localeStore.setMessages(message);
  setLocaleMessage(localeStore.getLocale);
}
