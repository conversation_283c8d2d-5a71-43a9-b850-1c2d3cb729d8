import type { GetForEditInput } from '../../helper/shared/models'
import type {
    GetWmInWarehouseListForEditorOutput,
    GetWmInWarehouseListInput,
    WmInWarehouseListCloneDto,
    WmInWarehouseListCreateDto,
    WmInWarehouseListDetailDto,
    WmInWarehouseListEditDto,
    WmInWarehouseListInProgressDto,
    WmInWarehouseListIsCancelDto,
    WmInWarehouseListIsInDto,
    WmInWarehouseListListDto,
    WmInWarehouseListProcessPartialDto,
    WmInWarehouseListSubmitDto,
    WmInWarehouseListUpdateStatusDto,
} from '../wms/dtos/models'
import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class WmInWarehouseListService {
    apiName = 'EnterpriseService'

    clone = (dto: WmInWarehouseListCloneDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-in-warehouse-list/CloneInWarehouseList',
                data: dto,
            },
            options,
        )

    create = (input: WmInWarehouseListCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-in-warehouse-list',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/wm-in-warehouse-list/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<WmInWarehouseListDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/wm-in-warehouse-list/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<WmInWarehouseListDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-in-warehouse-list/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetWmInWarehouseListForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-in-warehouse-list/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getPaged = (input: GetWmInWarehouseListInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<WmInWarehouseListListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/wm-in-warehouse-list',
                params: {
                    status: input.status,
                    inWarehouseBillId: input.inWarehouseBillId,
                    materialId: input.materialId,
                    isDeleted: input.isDeleted,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    processPartialInWarehouseListByDto = (dto: WmInWarehouseListProcessPartialDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/wm-in-warehouse-list/ProcessPartial',
                data: dto,
            },
            options,
        )

    update = (input: WmInWarehouseListEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-in-warehouse-list',
                data: input,
            },
            options,
        )

    updateStatus = (updateStatusDao: WmInWarehouseListUpdateStatusDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/wm-in-warehouse-list/updateStatus',
                data: updateStatusDao,
            },
            options,
        )

    submit = (input: WmInWarehouseListSubmitDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-in-warehouse-list/submit',
                data: input,
            },
            options,
        )

    inProgress = (input: WmInWarehouseListInProgressDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-in-warehouse-list/in-progress',
                data: input,
            },
            options,
        )

    isIn = (input: WmInWarehouseListIsInDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-in-warehouse-list/is-in',
                data: input,
            },
            options,
        )

    isCancel = (input: WmInWarehouseListIsCancelDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/wm-in-warehouse-list/is-cancel',
                data: input,
            },
            options,
        )
}
