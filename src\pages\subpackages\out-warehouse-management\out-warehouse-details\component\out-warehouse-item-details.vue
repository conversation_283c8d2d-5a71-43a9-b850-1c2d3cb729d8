<template>
    <view class="out-warehouse-item-details">
        <view v-if="loading" class="loading-container">
            <nut-skeleton rows="10" title animated />
        </view>
        <view v-else-if="!itemDetails.id" class="empty-container">
            <nut-empty description="暂无出库子项详情" image="empty" />
        </view>
        <view v-else>
            <nut-cell :title="'状态'">
                <template #desc>
                    <nut-tag :color="getStatusColor(itemDetails.status)">
                        {{ getStatusText(itemDetails.status) }}
                    </nut-tag>
                </template>
            </nut-cell>
            <nut-cell :title="t('text.name')" :desc="itemDetails.name || '-'"></nut-cell>
            <nut-cell :title="t('text.encode')" :desc="itemDetails.encode || '-'"></nut-cell>
            <nut-cell :title="'物料名称'" :desc="materialName || '-'"></nut-cell>
            <nut-cell :title="'库位'" :desc="locationName || '-'"></nut-cell>
            <nut-cell :title="'数量'" :desc="itemDetails.quantity || 0"></nut-cell>
            <nut-cell v-if="itemDetails.unitPrice" :title="'单价'">
                <template #desc>
                    <text class="price-value">¥{{ itemDetails.unitPrice || 0 }}</text>
                </template>
            </nut-cell>
            <nut-cell v-if="itemDetails.totalPrice" :title="'总价'">
                <template #desc>
                    <text class="total-price-value">¥{{ itemDetails.totalPrice || 0 }}</text>
                </template>
            </nut-cell>
            <nut-cell :title="t('text.describe')" :desc="itemDetails.describe || '-'"></nut-cell>
            <nut-cell :title="'创建时间'" :desc="formatTime(itemDetails.creationTime) || '-'"></nut-cell>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { t } from '@/locale/fanyi'
import { WmOutWarehouseListService } from '@/api/proxy/enterprise/controller/wm-out-warehouse-list.service'
import { WmMaterialService } from '@/api/proxy/enterprise/controller/wm-material.service'
import { WmWarehouseLocationService } from '@/api/proxy/enterprise/controller/wm-warehouse-location.service'
import { Loading, HideLoading, Toast } from '@/utils/Taro-api'
import dayjs from 'dayjs'

// 初始化服务
const wmOutWarehouseListService = new WmOutWarehouseListService()
const wmMaterialService = new WmMaterialService()
const wmWarehouseLocationService = new WmWarehouseLocationService()

// 详情数据
const itemDetails = ref<any>({})
const loading = ref(false)
const materialName = ref('')
const locationName = ref('')

interface OutWarehouseItemDetailsProps {
    itemId?: string // 可选，可以通过props传入或者通过方法设置
}

const props = defineProps<OutWarehouseItemDetailsProps>()

// 状态颜色映射
const getStatusColor = (status: string) => {
    switch (status) {
        case 'Pending':
            return '#E6A23C' // 橙色
        case 'Shipped':
            return '#67C23A' // 绿色
        case 'Returned':
            return '#F56C6C' // 红色
        case 'Cancelled':
            return '#909399' // 灰色
        default:
            return '#909399' // 灰色
    }
}

// 状态文本映射
const getStatusText = (status: string) => {
    switch (status) {
        case 'Pending':
            return '待处理'
        case 'Shipped':
            return '已出库'
        case 'Returned':
            return '已退回'
        case 'Cancelled':
            return '已取消'
        default:
            return status
    }
}

// 格式化时间
const formatTime = (time: string) => {
    if (!time) return '-'
    return dayjs(time).format('YYYY-MM-DD HH:mm')
}

/**
 * 获取物料名称
 */
const fetchMaterialName = async (id: string) => {
    if (!id) return
    try {
        const result = await wmMaterialService.get(id)
        materialName.value = result.name || '-'
    } catch (error) {
        console.error('获取物料名称失败:', error)
        materialName.value = '-'
    }
}

/**
 * 获取库位名称
 */
const fetchLocationName = async (id: string) => {
    if (!id) return
    try {
        const result = await wmWarehouseLocationService.get(id)
        locationName.value = result.name || '-'
    } catch (error) {
        console.error('获取库位名称失败:', error)
        locationName.value = '-'
    }
}

/**
 * 获取出库子项详细信息
 */
const fetchData = async (id: string) => {
    if (!id) return

    try {
        loading.value = true
        Loading()

        // 获取出库子项详情
        const result = await wmOutWarehouseListService.get(id)

        if (result) {
            itemDetails.value = result

            // 获取物料和库位信息
            await Promise.all([
                result.materialId && fetchMaterialName(result.materialId),
                result.locationId && fetchLocationName(result.locationId),
            ])
        } else {
            itemDetails.value = {}
            Toast(t('text.noData'), { icon: 'none' })
        }
    } catch (error) {
        console.error('获取出库子项详情失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        loading.value = false
        HideLoading()
    }
}

// 监听props变化加载数据
onMounted(() => {
    if (props.itemId) {
        fetchData(props.itemId)
    }
})

// 添加对itemId变化的监听
watch(
    () => props.itemId,
    newVal => {
        if (newVal) {
            fetchData(newVal)
        }
    },
    { immediate: true },
)

// 将方法暴露给父组件
defineExpose({
    fetchData,
})
</script>

<style lang="scss" scoped>
.out-warehouse-item-details {
    background-color: #fff;

    .loading-container {
        padding: 20px 0;
    }

    .price-value {
        color: #0066ff;
        font-weight: 500;
    }

    .total-price-value {
        color: #f56c6c;
        font-weight: 500;
    }
}
</style>
