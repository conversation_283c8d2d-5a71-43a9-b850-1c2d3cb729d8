import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'
import type { NodeSelectDataDto } from '../../helper/shared/models'
import type {
    GetProductItemEditorInput,
    GetProductItemForEditorOutput,
    GetProductItemInput,
    ProductItemCreateDto,
    ProductItemDetailDto,
    ProductItemEditDto,
    ProductItemTreeListDto,
    ProductItemTreeNodesDto,
} from '../pms/dtos/models'

export class ProductItemService {
    apiName = 'EnterpriseService'

    create = (input: ProductItemCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/product-item',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/product-item/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<ProductItemDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/product-item/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<ProductItemDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/product-item/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetProductItemEditorInput, options?: RequestOptions) =>
        defHttp.request<GetProductItemForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/product-item/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getOptionItems = (filterText: string, options?: RequestOptions) =>
        defHttp.request<NodeSelectDataDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/product-item/options',
                params: {
                    filterText,
                },
            },
            options,
        )

    getPaged = (input: GetProductItemInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<ProductItemTreeListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/product-item',
                params: {
                    isDeleted: input.isDeleted,
                    parentId: input.parentId,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    getTreeNodes = (filterText: string, options?: RequestOptions) =>
        defHttp.request<ProductItemTreeNodesDto[]>(
            {
                method: 'GET',
                url: '/api/enterprise/product-item/options-trees',
                params: {
                    filterText,
                },
            },
            options,
        )

    revokeDelete = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/product-item/revoke-delete',
                params: {
                    id,
                },
            },
            options,
        )

    update = (input: ProductItemEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/product-item',
                data: input,
            },
            options,
        )
}
