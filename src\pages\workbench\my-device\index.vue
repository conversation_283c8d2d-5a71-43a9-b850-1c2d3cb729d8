<template>
    <view class="main-viewer">
        <nut-space direction="vertical" fill>
            <nut-row :gutter="5">
                <nut-col v-for="(v, k) in deviceStatus" :key="k" :span="24 / deviceStatus.length">
                    <StatusCard :title="v.title" :status="v.status" :number="v.number" />
                </nut-col>
            </nut-row>
            <view class="grid-box"></view>
            <ListScreen
                @update:screen-factory="handleUpdateScreenFactory"
                @update:search-key="handleUpdateSearchKey"
                :is-pull-down="isPullDown"
            />
        </nut-space>
        <nut-cell style="padding: 0 10px; margin: 0; margin-top: -10px">
            <template #title>
                <nut-checkbox
                    v-model="isAutoRefresh"
                    style="width: 10%; margin-top: 3%; margin-left: 5%"
                ></nut-checkbox>
                <span style="position: absolute; left: 9%; top: 15%">{{ $t('ui.autoRefresh') }}</span>
            </template>
            <template #desc>
                <nut-button type="success" :loading="isLoading" size="small" @click="refresh">
                    <template #icon>
                        <Refresh2 style="font-size: 12px; margin-left: -5px"></Refresh2>
                        {{ $t('ui.refresh') }}
                    </template>
                </nut-button>
            </template>
        </nut-cell>
        <InformationCard
            v-for="(item, index) in deviceList"
            :key="index"
            :item-image-url="item.imagesUrl"
            @click:content="toDeviceDetails(item.id)"
            :isActivate="item.isActivate"
            :status="item.status"
            :iotStatus="item.iotStatus"
        >
            <template #front-title>{{ `[${item.serialNo}]` }}</template>
            <template #title>{{ item.name }}</template>
            <template #line-first>
                <div>{{ `${$t('Platform.Encode')}:${item.encode}` }}</div>
            </template>
            <template #line-second>
                <div>{{ `${$t('AbpIdentity.Type')}:${$t(`Platform.${item.deviceType}`)}` }}</div>
            </template>
            <template #line-third>
                <div>{{ `${$t('ui.factoryName')}:${$t(`${item.factoryParentName}`)}` }}</div>
            </template>
            <template #line-fourth>
                <div v-if="item.mode">{{ `${$t('IotService.WorkMode')}:${$t(`IotService.Mode${item.mode}`)}` }}</div>
            </template>
            <template #space-one>
                <nut-tag style="margin-top: 0.5vh" :color="item.isActivate ? '#30D479' : '#FF0000'">
                    {{ $t(item.isActivate ? 'Helper.Enable' : 'Helper.Disable') }}
                </nut-tag>
            </template>
            <template #space-two v-if="item.status">
                <nut-tag style="margin-top: 0.5vh" :color="getStatusColor(item.status)">
                    {{ $t(`IotService.${item.status}`) }}
                </nut-tag>
            </template>
        </InformationCard>
        <nut-empty v-if="deviceList.length === 0" image="empty" :description="$t('Platform.NoData')" />
        <view class="no-more" v-if="reachedEnd && searchParams.skipCount > 0">{{ $t('ui.noMore') }}</view>
        <view class="ios-safe-distance" />
    </view>
</template>

<script lang="ts" setup>
import { DatavDataFlowService } from '@/api/proxy/data-view/datav-data-flow-service.service'
import { useDidShow, usePullDownRefresh, useReachBottom } from '@/hooks/component/hooks'
import Taro from '@tarojs/taro'
import { onBeforeUnmount, reactive, ref } from 'vue'
import { FactoryModelsService } from '@/api/proxy/factory-models-service/factory-models-service.service'
import { FactoryModelsTreeNodesDto } from '@/api/proxy/factory-models-service/models'
import { FactoryDeviceIotFlowListDto } from '@/api/proxy/data-view/models'
import { DeviceStatus } from '@/api/proxy/data-view/models'
import { handleImages } from '@/utils/image'
import { HideLoading, Loading, NavigateTo, Toast } from '@/utils/Taro-api'
import { t } from '@/locale/fanyi'
import { Refresh2 } from '@nutui/icons-vue-taro'
import { watch } from 'vue'

/**
 * @description 修改导航栏标题
 */
Taro.setNavigationBarTitle({ title: t('menu.deviceList') })

interface DeviceStatusCard {
    title: string
    number: number
    status: string
}

const datavDataFlowService = new DatavDataFlowService()
const factoryModelsService = new FactoryModelsService()
const deviceStatus = ref<DeviceStatusCard[]>([])
const factoryTreeNode = ref<FactoryModelsTreeNodesDto[]>([])
const isPullDown = ref<boolean>(false)
const deviceList = ref<FactoryDeviceIotFlowListDto[]>([])
const searchParams = reactive({
    factoryParentId: '',
    sorting: '',
    filterText: '',
    skipCount: 0,
    maxResultCount: 10,
    isDeleted: false,
})
const reachedEnd = ref<boolean>(false)
const isLoading = ref<boolean>(false)
const isAutoRefresh = ref<boolean>(false)
let refreshInterval: NodeJS.Timeout | null = null

/**
 * @description 刷新数据
 */
const refresh = async () => {
    isLoading.value = true
    await fetchDeviceStatus()
    await fetchFactoryDevice()
    isLoading.value = false
}

/**
 * @description 更新搜索关键字
 */
const handleUpdateSearchKey = async (key: string) => {
    searchParams.filterText = key
    await fetchFactoryDevice()
}

/**
 * @description 更新筛选工厂
 */
const handleUpdateScreenFactory = async (factoryId: string) => {
    searchParams.factoryParentId = factoryId
    await fetchFactoryDevice()
}

/**
 * @description 获取状态颜色
 */
const getStatusColor = (status: DeviceStatus) => {
    switch (status) {
        case DeviceStatus.Runing:
            return '#30D479'
        case DeviceStatus.Alarm:
            return '#FF0000'
        case DeviceStatus.Stop:
            return '#FF9900'
        case DeviceStatus.Pause:
            return '#3460FA'
        case DeviceStatus.Esp:
            return '#FF0000'
        case DeviceStatus.Offline:
            return '#999999'
        default:
            return '#3460FA'
    }
}

/**
 * @description 获取工厂节点数据
 */
const fetchFactoryNode = async () => {
    try {
        const result = await factoryModelsService.getTreeNodes('')
        factoryTreeNode.value = result
        if (result.length > 0 && result[0].id) {
            // 默认选中第一个节点
            searchParams.factoryParentId = result[0].id
        }
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
    }
}

/**
 * @description 获取设备count数据
 */
const fetchDeviceStatus = async () => {
    try {
        deviceStatus.value = []
        const result = await datavDataFlowService.getDeviceStatusCount({})
        for (const key in result) {
            switch (key) {
                case 'total':
                    deviceStatus.value.push({
                        title: t('ui.totalDevice'),
                        number: result[key],
                        status: 'total',
                    })
                    break
                case 'alarm':
                    deviceStatus.value.push({
                        title: t('ui.alarmDevice'),
                        number: result[key],
                        status: 'alarm',
                    })
                    break
                case 'stop':
                    deviceStatus.value.push({
                        title: t('ui.stopDevice'),
                        number: result[key],
                        status: 'stop',
                    })
                    break
                case 'runing':
                    deviceStatus.value.push({
                        title: t('ui.runDevice'),
                        number: result[key],
                        status: 'runing',
                    })
                    break
            }
        }
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
    }
}

/**
 * @description 获取工厂节点设备
 */
const fetchFactoryDevice = async () => {
    try {
        Loading()
        searchParams.skipCount = 0
        reachedEnd.value = false
        const result = await datavDataFlowService.getPaged(searchParams)
        deviceList.value = result.items
        // 处理图片
        deviceList.value.forEach(item => {
            if (item.images !== undefined) {
                item.imagesUrl = handleImages(item.images)
            }
        })
        HideLoading()

        // 更新 reachedEnd 状态
        reachedEnd.value = result.items.length === 0
    } catch {
        Toast(t('ui.requestFailed'), { icon: 'error' })
    }
}

/**
 * @description 清除搜索条件
 */
const clearSearch = () => {
    searchParams.filterText = ''
    searchParams.skipCount = 0
    searchParams.sorting = ''
}

/**
 * @description 跳转设备详情
 */
const toDeviceDetails = (id: string) => {
    NavigateTo(`/pages/workbench/device-list/device-details/index?id=${id}`)
}

/**
 * @description 自动刷新数据
 */
watch(
    isAutoRefresh,
    newValue => {
        if (newValue) {
            refreshInterval = setInterval(async () => {
                await refresh()
            }, 5000)
        } else {
            // 停止定时器
            if (refreshInterval !== null) {
                clearInterval(refreshInterval)
                refreshInterval = null
            }
        }
    },
    { immediate: true },
)

/**
 * 组件销毁时，清除定时器
 */
onBeforeUnmount(() => {
    if (refreshInterval !== null) {
        clearInterval(refreshInterval)
    }
})

/**
 * @description 下拉刷新
 */
usePullDownRefresh(async () => {
    Loading()
    clearSearch()
    await fetchDeviceStatus() //获取头部卡片数据
    await fetchFactoryDevice() //获取工厂设备数据
    Taro.stopPullDownRefresh()
    HideLoading()
})

/**
 * @description 页面触底钩子
 */
useReachBottom(async () => {
    if (!reachedEnd.value) {
        // 判断是否到达末尾
        Loading()
        searchParams.skipCount += searchParams.maxResultCount
        const result = await datavDataFlowService.getPaged(searchParams)
        deviceList.value = [...deviceList.value, ...result.items]
        // 处理图片
        deviceList.value.forEach(item => {
            if (item.images !== undefined) {
                item.imagesUrl = handleImages(item.images)
            }
        })
        // 更新 reachedEnd 状态
        reachedEnd.value = result.items.length === 0
        HideLoading()
    }
})

/**
 * @description 页面加载时钩子
 */
useDidShow(async () => {
    Loading()
    clearSearch()
    await fetchDeviceStatus() //获取头部卡片数据
    await fetchFactoryNode() //获取工厂节点数据
    HideLoading()
})
</script>

<style lang="scss">
.main-viewer {
    padding: 20px;

    .grid-box {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
    }

    .no-more {
        text-align: center;
        padding: 10px;
        color: #999;
        font-size: 32px;
    }

    .ios-safe-distance {
        bottom: 0;
        width: 100%;
        height: 10rpx;
        display: flex;
        z-index: 99;
    }
}
</style>
