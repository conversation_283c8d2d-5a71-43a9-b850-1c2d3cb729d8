<template>
    <view>
        <view>
            <nut-menu>
                <nut-menu-item ref="searchBox" :title="t('ui.search')">
                    <nut-searchbar
                        v-model="queryForm.filterText"
                        :clearable="true"
                        :placeholder="t('text.pleaseEnterName')"
                    >
                        <template #leftin>
                            <Search2 />
                        </template>
                        <template #rightout>
                            <view style="display: flex; gap: 10px">
                                <nut-button type="primary" size="small" @click="queryData">
                                    {{ t('ui.search') }}
                                </nut-button>
                                <nut-button type="primary" plain size="small" @click="handleReset">
                                    {{ t('ui.reset') }}
                                </nut-button>
                            </view>
                        </template>
                    </nut-searchbar>
                </nut-menu-item>
            </nut-menu>
        </view>
        <view style="display: flex; justify-content: space-between; align-items: center; padding: 12px">
            <view></view>
            <view>
                <nut-button class="custom-btn outline-btn" size="small" @click="queryData">
                    {{ t('ui.refresh') }}
                </nut-button>
            </view>
        </view>
        <view class="content-wrapper">
            <view v-if="loading" class="loading-container">
                <nut-skeleton rows="5" title animated />
            </view>
            <view v-else-if="bomList.length === 0" class="empty-state">
                <nut-empty description="暂无数据" image="empty"> </nut-empty>
            </view>
            <view v-else class="bom-list">
                <view v-for="(item, index) in bomList" :key="item.id" @click="handleInfo(item)">
                    <InformationCard :isActivate="item.isActivate" :show-empty-image="false">
                        <template #front-title> [{{ index + 1 }}] </template>
                        <template #title>
                            {{ item.name }}
                        </template>
                        <template #line-first> {{ t('text.encode') }}：{{ item.encode }} </template>
                        <template #line-second> {{ t('text.describe') }}：{{ item.describe || '-' }} </template>
                        <template #line-third>
                            <view class="bom-details">
                                <text>规格：{{ item.specification || '-' }}</text>
                                <text style="margin-left: 20px">数量：{{ item.quantity }}</text>
                            </view>
                        </template>
                        <template #space-one>
                            <nut-tag :color="getTypeColor(item.type)" style="margin-bottom: 0.5vh; margin-right: 0.5vh">
                                {{ getTypeText(item.type) }}
                            </nut-tag>
                            <nut-tag :color="getStatusColor(item.isActivate)" style="margin-bottom: 0.5vh">
                                {{ getStatusText(item.isActivate) }}
                            </nut-tag>
                        </template>
                    </InformationCard>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { useDidShow } from '@/hooks/component/hooks'
import { ref, reactive } from 'vue'
import { t } from '@/locale/fanyi'
import { Search2 } from '@nutui/icons-vue-taro'
import dayjs from 'dayjs'
import Taro from '@tarojs/taro'
import { WmProductBomService } from '@/api/proxy/enterprise/controller/wm-product-bom.service'
import { Toast, Loading, HideLoading } from '@/utils/Taro-api'
import InformationCard from '@/components/InformationCard.vue'

// 初始化服务
const wmProductBomService = new WmProductBomService()

// BOM数据接口
interface WmProductBomTreeListDto {
    id?: string
    name: string
    encode: string
    specification?: string
    describe?: string
    type: string
    quantity: number
    isActivate: boolean
    bomId?: string
    materialId?: string
    extraProperties?: Record<string, object>
    concurrencyStamp?: string
    children: WmProductBomTreeListDto[]
}

// 查询表单
const queryForm = reactive({
    filterText: '',
    parentId: '',
})

const loading = ref(false)
const bomList = ref<WmProductBomTreeListDto[]>([])

// 物料类型颜色映射
const getTypeColor = (type: string) => {
    switch (type) {
        case 'Material':
            return '#2196F3' // 蓝色
        case 'Product':
            return '#FF9800' // 橙色
        default:
            return '#9E9E9E' // 灰色
    }
}

// 物料类型文本映射
const getTypeText = (type: string) => {
    switch (type) {
        case 'Material':
            return '物料'
        case 'Product':
            return '产品'
        default:
            return '无'
    }
}

// 状态颜色映射
const getStatusColor = (isActivate: boolean) => {
    return isActivate ? '#4CAF50' : '#F44336' // 绿色或红色
}

// 状态文本映射
const getStatusText = (isActivate: boolean) => {
    return isActivate ? '启用' : '禁用'
}

// 查询数据
const queryData = async () => {
    try {
        loading.value = true
        Loading()

        // 构建查询参数
        const params = {
            filterText: queryForm.filterText,
            skipCount: 0,
            maxResultCount: 50,
        }

        // 调用API获取数据
        const result = await wmProductBomService.getPaged(params)

        if (result && result.items) {
            // 只取顶级项，不处理嵌套的children
            bomList.value = result.items
        } else {
            bomList.value = []
        }
    } catch (error) {
        console.error('获取BOM表数据失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
        bomList.value = []
    } finally {
        loading.value = false
        HideLoading()
    }
}

// 重置查询条件
const handleReset = () => {
    queryForm.filterText = ''
    queryForm.parentId = ''
    queryData()
}

// 查看BOM详情
const handleInfo = (item: WmProductBomTreeListDto) => {
    Toast(`查看BOM：${item.name}`, { icon: 'none' })
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    // 确保页面加载数据
    queryData()
})
</script>

<style lang="scss">
.content-wrapper {
    padding: 12px;
    background: #f5f5f5;
    min-height: calc(100vh - 100px);
}

.loading-container {
    padding: 20px 0;
}

.empty-state {
    padding: 40px 0;
    text-align: center;
}

.bom-list {
    .bom-details {
        display: flex;
        color: #606266;
    }
}

.custom-btn {
    &.primary-btn {
        background-color: #4970f2;
    }

    &.outline-btn {
        color: #4970f2;
        border: 1px solid #4970f2;
    }
}
</style>
