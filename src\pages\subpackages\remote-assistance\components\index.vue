<template>
    <view class="content-wrapper">
        <nut-tabs v-model="value" :ellipsis="false" title-scroll title-gutter="10">
            <nut-tab-pane v-for="tab in tabList" :key="tab.key" :title="t(tab.titleKey)" :pane-key="tab.key">
                <remote-details v-if="tab.key === '1'" :remote-data="clickedItemData" />
                <template v-else>{{ tab.content }}</template>
            </nut-tab-pane>
        </nut-tabs>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { t } from '@/locale/fanyi'
import { useDidShow } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { RemoteAssistanceListDto } from '@/api/proxy/platform/dtos/models'
import RemoteDetails from './remote-details.vue'

const value = ref('1')
const clickedItemData = ref<RemoteAssistanceListDto>()
const tabList = [
    { key: '1', titleKey: 'menu.deviceInfo', content: '' },
    { key: '2', titleKey: 'menu.liveStatus', content: 'Content 2' },
    { key: '3', titleKey: 'menu.alarmList', content: 'Content 3' },
    { key: '4', titleKey: 'menu.diagnosticInfo', content: 'Content 4' },
    { key: '5', titleKey: 'menu.deviceParameters', content: 'Content 5' },
    { key: '6', titleKey: 'menu.deviceMonitoring', content: 'Content 6' },
    { key: '7', titleKey: 'menu.deviceImage', content: 'Content 7' },
]

useDidShow(() => {
    try {
        const currentInstance = Taro.getCurrentInstance()
        const page = currentInstance?.page
        if (!page) {
            return
        }
        const eventChannel = page.getOpenerEventChannel?.()
        if (!eventChannel) {
            return
        }
        eventChannel.on('acceptClickedItemData', (data: { data: RemoteAssistanceListDto }) => {
            clickedItemData.value = data.data
        })
    } catch (error) {
        console.error('获取事件通道失败:', error)
    }
})
</script>

<style lang="scss" scoped></style>
