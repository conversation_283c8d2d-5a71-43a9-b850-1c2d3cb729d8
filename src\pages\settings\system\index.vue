<template>
  <view>
    <nut-button block class="logout-btn" type="primary" @click="handleClick()">退出登录</nut-button>
  </view>
</template>

<script lang="ts">
import { useAuthStore } from '@/stores/modules/auth';
import Taro from '@tarojs/taro';

export default {
  setup() {
    const authStore = useAuthStore();
    const handleClick = () => {
      authStore.logout();
      Taro.navigateTo({
        url: '/pages/passport/login/index'
      });
    };
    return {
      handleClick
    };
  }
};
</script>

<style lang="scss">
.logout-btn {
  margin-top: 48px;
}
</style>
