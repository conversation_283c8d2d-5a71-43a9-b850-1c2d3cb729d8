<template>
    <view class="device-card" :class="cardClass">
        <nut-row class="card-row">
            <nut-col :span="9" v-if="showImageContainer" class="image-column">
                <view class="card-image" @click="handleOpenImgPreview">
                    <image
                        :style="props.imageStyle"
                        :src="imageSrc"
                        :class="props.isIconImage ? 'icon-image' : 'default-image'"
                        v-if="imageSrc"
                    />
                </view>
            </nut-col>
            <nut-col :span="showImageContainer ? 15 : 24" @click="handleClickContent">
                <div class="content">
                    <div
                        v-if="$slots['front-title'] || $slots['title'] || props.serialNo || props.title"
                        class="content-title content-space"
                    >
                        <slot name="front-title">
                            <span>{{ props.serialNo }}</span>
                        </slot>
                        <slot name="title">
                            <span>{{ props.title }}</span>
                        </slot>
                    </div>
                    <div v-if="$slots['line-first']" class="content-space">
                        <slot name="line-first"> </slot>
                    </div>
                    <div v-if="$slots['line-second']" class="content-space">
                        <slot name="line-second"> </slot>
                    </div>
                    <div v-if="$slots['line-third']" class="content-space">
                        <slot name="line-third"> </slot>
                    </div>
                    <div v-if="$slots['line-fourth']" class="content-space">
                        <slot name="line-fourth"> </slot>
                    </div>
                    <div v-if="$slots['line-fifth']" class="content-space">
                        <slot name="line-fifth"> </slot>
                    </div>
                    <div v-if="$slots['content-default']" class="content-space content-default">
                        <slot name="content-default"></slot>
                    </div>
                    <div v-if="$slots['space-one'] || $slots['space-two']" class="content-space">
                        <div style="display: flex; justify-content: space-between; align-items: center">
                            <slot name="space-one"></slot>
                            <slot name="space-two"></slot>
                        </div>
                    </div>
                </div>
            </nut-col>
        </nut-row>
    </view>
    <nut-image-preview :show="isShowPreview" :images="imageObj" autoplay="0" @close="hidePreview" />
</template>

<script lang="ts" setup>
import { computed, PropType, ref } from 'vue'
import { ImageInterface } from '@nutui/nutui-taro/dist/types/__VUE/imagepreview/types'
import { useDidShow } from '@/hooks/component/hooks'
import emptyPng from '@/assets/images/empty.png'

const props = defineProps({
    itemImageUrl: {
        type: [String, Array] as PropType<string | string[]>,
    },
    iconImage: {
        type: String,
    },
    serialNo: {
        type: String,
    },
    title: {
        type: String,
    },
    isIconImage: {
        type: Boolean,
    },
    isActivate: {
        type: Boolean,
        default: undefined,
    },
    iotStatus: {
        type: String,
    },
    status: {
        type: String,
    },
    imageStyle: {
        type: Object,
    },
    showEmptyImage: {
        type: Boolean,
        default: true,
    },
})

const cardClass = computed(() => {
    // 处理激活状态类名
    let activateClass = 'NONE'
    if (props.isActivate === true) {
        activateClass = 'ACTIVE'
    } else if (props.isActivate === false) {
        activateClass = 'OFFLINE'
    }
    // 如果有status属性，根据status设置对应的类名
    if (props.status) {
        switch (props.status) {
            case 'Runing':
                activateClass = 'ACTIVE' // 运行中状态，使用绿色
                break
            case 'Alarm':
                activateClass = 'OFFLINE' // 报警状态，使用红色
                break
            case 'Stop':
                activateClass = 'INACTIVE' // 停止状态，使用橙色
                break
            case 'Pause':
                activateClass = 'UNKNOWN' // 暂停状态，使用蓝色
                break
            case 'Esp':
                activateClass = 'OFFLINE' // 紧急停止状态，使用红色
                break
            case 'Offline':
                activateClass = 'NONE' // 离线状态，使用灰色
                break
            default:
                break
        }
    }
    // 收集所有类名
    const classList = [activateClass]
    // 添加其他类名
    if (props.iotStatus) classList.push(props.iotStatus)
    if (props.status) classList.push(props.status)
    return classList
})

const imageSrc = computed(() => {
    if (props.itemImageUrl !== undefined && props.itemImageUrl !== '[]') {
        return props.itemImageUrl[0]
    }
    if (props.iconImage && props.itemImageUrl !== '[]') {
        return props.iconImage
    }
    return props.showEmptyImage ? emptyPng : ''
})

const showImageContainer = computed(() => {
    // 显示图片容器的条件：
    // 1. 有实际图片
    // 2. 或者 showEmptyImage 为 true（允许显示占位图）
    return (props.itemImageUrl && props.itemImageUrl !== '[]') || props.iconImage || props.showEmptyImage
})

const emit = defineEmits(['click:content'])
const isShowPreview = ref<boolean>(false)
const imageObj = ref<ImageInterface[]>()

/**
 * @description 隐藏图片预览
 */
const hidePreview = () => {
    isShowPreview.value = false
}

/**
 * @description 处理图片
 */
const handleOpenImgPreviewUrl = () => {
    if (props.itemImageUrl === undefined) {
        return
    }
    if (Array.isArray(props.itemImageUrl)) {
        imageObj.value = props.itemImageUrl.map(item => {
            return { src: item }
        })
    }
}

/**
 * @description 打开图片预览
 */
const handleOpenImgPreview = () => {
    if (!props.itemImageUrl || (Array.isArray(props.itemImageUrl) && props.itemImageUrl.length === 0)) {
        return
    }
    isShowPreview.value = true
}

/**
 * @description 点击内容
 */
const handleClickContent = () => {
    emit('click:content')
}

useDidShow(() => {
    handleOpenImgPreviewUrl()
})

// 添加默认导出
defineOptions({
    name: 'InformationCard',
})
</script>

<style lang="scss">
.device-card {
    border-radius: 16px;
    margin-bottom: 28px;
    padding: 22px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.18), 0 8px 20px rgba(0, 0, 0, 0.12), 0 4px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &.ACTIVE {
        background: linear-gradient(135deg, #e6f9f0 0%, #c7f2e3 100%);
        border-left: 6px solid #30d479;
    }

    &.OFFLINE {
        background: linear-gradient(135deg, #fff0f0 0%, #ffe0e0 100%);
        border-left: 6px solid #ff0000;
    }

    &.UNKNOWN {
        background: linear-gradient(135deg, #f0f5ff 0%, #e5eeff 100%);
        border-left: 6px solid #3460fa;
    }

    &.ONLINE {
        background: linear-gradient(135deg, #e6f9f0 0%, #c7f2e3 100%);
        border-left: 6px solid #30d479;
    }

    &.INACTIVE {
        background: linear-gradient(135deg, #fff5f0 0%, #ffe8e0 100%);
        border-left: 6px solid #ff9966;
    }

    &.NONE {
        background: linear-gradient(135deg, #f7f9fe 0%, #edf1fa 100%);
        border-left: 6px solid #a3b1cc;
    }

    .image-column {
        padding-right: 15px;
    }
}

.content-space {
    margin-top: 6px;
}

.card-row {
    display: flex;

    .nut-col:first-child {
        padding-right: 15px;
    }
}

.card-image {
    width: 95%;
    height: 240px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -10px;
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.18) inset, 0 2px 6px rgba(0, 0, 0, 0.15);

    .default-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .icon-image {
        width: 75px !important;
        height: 75px !important;
        object-fit: contain;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

.content {
    padding-left: 10px;
}
</style>
