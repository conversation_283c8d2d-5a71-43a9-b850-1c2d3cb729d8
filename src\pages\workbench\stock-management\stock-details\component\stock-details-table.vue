<template>
    <view>
        <nut-cell-group title="基本信息">
            <nut-cell :title="t('text.name')" :desc="detailsData.name || '未命名'" />
            <nut-cell :title="t('text.encode')" :desc="detailsData.encode || '-'" />
            <nut-cell :title="t('text.materialtype')">
                <template #desc>
                    <span>{{ getMaterialTypeText(detailsData.type) }}</span>
                </template>
            </nut-cell>
            <nut-cell :title="t('text.materialsAuditStatus')">
                <template #desc>
                    <nut-tag :type="getMaterialsAuditStatusType(detailsData.materialsAuditStatus)">
                        {{ getMaterialsAuditStatusText(detailsData.materialsAuditStatus) }}
                    </nut-tag>
                </template>
            </nut-cell>
        </nut-cell-group>
        <nut-cell-group title="库存信息">
            <nut-cell :title="t('text.stock')" :desc="`${detailsData.quantity || 0}`" />
            <nut-cell :title="t('text.costPrice')" :desc="`¥${detailsData.costPrice?.toFixed(2) || '0.00'}`" />
            <nut-cell :title="t('text.stockStatus')">
                <template #desc>
                    <nut-tag :type="getStatusType(detailsData.stockStatus)">
                        {{ getStatusText(detailsData.stockStatus) }}
                    </nut-tag>
                </template>
            </nut-cell>
            <nut-cell :title="t('text.batchCode')" :desc="detailsData.batchCode || '无'" />
            <nut-cell
                :title="t('text.expireDate')"
                :desc="detailsData.expireDate ? formatDate(detailsData.expireDate) : '无'"
            />
            <nut-cell :title="t('text.isFrozen')">
                <template #desc>
                    <nut-tag :type="detailsData.isFrozen ? 'warning' : 'success'">
                        {{ detailsData.isFrozen ? '已冻结' : '未冻结' }}
                    </nut-tag>
                </template>
            </nut-cell>
            <nut-cell :title="t('text.isActivate')">
                <template #desc>
                    <nut-tag :type="detailsData.isActivate ? 'success' : 'danger'">
                        {{ detailsData.isActivate ? '已启用' : '未启用' }}
                    </nut-tag>
                </template>
            </nut-cell>
        </nut-cell-group>
        <nut-cell-group title="其他信息">
            <nut-cell
                :title="t('text.creationTime')"
                :desc="detailsData.creationTime ? formatDate(detailsData.creationTime) : '无'"
            />
            <nut-cell
                :title="t('text.lastModificationTime')"
                :desc="detailsData.lastModificationTime ? formatDate(detailsData.lastModificationTime) : '无'"
            />
        </nut-cell-group>
    </view>
</template>

<script setup lang="ts">
import { t } from '@/locale/fanyi'
import dayjs from 'dayjs'

// 库存状态映射
const stockStatusMap = {
    Available: { type: 'success', text: '可用' },
    Reserved: { type: 'warning', text: '已预留' },
    InTransit: { type: 'info', text: '在途' },
    Damaged: { type: 'danger', text: '损坏' },
    Expired: { type: 'danger', text: '过期' },
    OutOfStock: { type: 'danger', text: '缺货' },
}

// 物料审核状态映射
const materialsAuditStatusMap = {
    Newly: { type: 'info', text: '新建' },
    Pending: { type: 'warning', text: '待审核' },
    Approved: { type: 'success', text: '已审核' },
    Rejected: { type: 'danger', text: '已拒绝' },
}

// 物料类型映射
const materialTypeMap = {
    None: { text: '无' },
    RawMaterial: { text: '原材料' },
    Product: { text: '产品' },
    SemiFinishedProduct: { text: '半成品' },
    Package: { text: '包装物' },
}

// 获取状态类型
const getStatusType = (status: string) => {
    return stockStatusMap[status]?.type || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
    return stockStatusMap[status]?.text || status
}

// 获取物料类型文本
const getMaterialTypeText = (type: string) => {
    return materialTypeMap[type]?.text || type
}

// 获取物料审核状态类型
const getMaterialsAuditStatusType = (status: string) => {
    return materialsAuditStatusMap[status]?.type || 'default'
}

// 获取物料审核状态文本
const getMaterialsAuditStatusText = (status: string) => {
    return materialsAuditStatusMap[status]?.text || status
}

// 格式化日期
const formatDate = (date: string) => {
    if (!date) return '-'
    return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 定义组件属性
interface StockDetailsProps {
    detailsData: {
        id?: string
        name?: string
        encode?: string
        model?: string
        specification?: string
        describe?: string
        type?: string
        isActivate?: boolean
        safeStock?: string
        minStock?: number
        maxStock?: number
        highValue?: string
        materialsAuditStatus?: string
        images?: string
        measureUnitId?: string
        categoryId?: string
        supplierId?: string
        quantity?: number
        costPrice?: number
        stockStatus?: string
        batchCode?: string
        expireDate?: string
        isFrozen?: boolean
        locationId?: string
        materialId?: string
        creatorId?: string
        creationTime?: string
        lastModifierId?: string
        lastModificationTime?: string
        locationName?: string
        categoryName?: string
        measureUnitName?: string
    }
}

// 定义组件属性
const props = defineProps<StockDetailsProps>()

// 添加默认导出
defineOptions({
    name: 'StockDetailsTable',
})
</script>

<style lang="scss" scoped></style>
