<template>
    <view>
        <view>
            <nut-menu>
                <nut-menu-item ref="searchBox" :title="t('ui.search')">
                    <nut-searchbar v-model="searchText" :clearable="true" :placeholder="t('text.pleaseEnterName')">
                        <template #leftin>
                            <Search2 />
                        </template>
                        <template #rightout>
                            <view style="display: flex; gap: 10px">
                                <nut-button type="primary" size="small" @click="handleSearch">
                                    {{ t('ui.search') }}
                                </nut-button>
                                <nut-button type="primary" plain size="small" @click="resetSearch">
                                    {{ t('ui.reset') }}
                                </nut-button>
                            </view>
                        </template>
                    </nut-searchbar>
                </nut-menu-item>
            </nut-menu>
        </view>
        <view style="display: flex; justify-content: space-between; align-items: center; padding: 12px">
            <view></view>
            <view>
                <nut-button class="custom-btn outline-btn" size="small" @click="fetchData">
                    {{ t('ui.refresh') }}
                </nut-button>
            </view>
        </view>
        <view class="content-wrapper">
            <view v-if="outWarehouseList.length > 0">
                <view v-for="(item, index) in outWarehouseList" :key="index" @click="handleDetailed(item)">
                    <InformationCard :isActivate="true" style="margin-bottom: 10px" :show-empty-image="false">
                        <template #front-title> [{{ index + 1 }}] </template>
                        <template #title>
                            {{ item.name }}
                        </template>
                        <template #line-first> {{ t('text.encode') }} ： {{ item.encode }} </template>
                        <template #line-second> {{ t('text.describe') }} ： {{ item.describe || '-' }} </template>
                        <template #line-third>
                            <span>{{ t('text.creationTime') }}：{{ formatDate(item.creationTime) }}</span>
                        </template>
                        <template #space-one>
                            <nut-tag :type="getStorageTypeTagType(item.outboundType)" style="margin-bottom: 0.5vh">
                                {{ getStorageTypeTagText(item.outboundType) }}
                            </nut-tag>
                        </template>
                        <template #space-two>
                            <nut-tag :type="getAuditStatusTagType(item.status)" style="margin-bottom: 0.5vh">
                                {{ getAuditStatusText(item.status) }}
                            </nut-tag>
                        </template>
                    </InformationCard>
                </view>
            </view>
            <view v-else class="empty-state">
                <nut-empty description="暂无数据" image="empty"> </nut-empty>
            </view>
        </view>
        <nut-dialog
            :title="t('text.warmReminder')"
            :content="t('text.deleteConfirm')"
            v-model:visible="isDelete"
            @cancel="isDelete = false"
            @ok="confirmDelete"
            :ok-text="t('ui.confirm')"
            :cancel-text="t('ui.cancel')"
        />
    </view>
</template>

<script lang="ts" setup>
import { useDidShow } from '@/hooks/component/hooks'
import { ref } from 'vue'
import { t } from '@/locale/fanyi'
import { Search2 } from '@nutui/icons-vue-taro'
import dayjs from 'dayjs'
import Taro from '@tarojs/taro'
import { WmOutWarehouseBillService } from '@/api/proxy/enterprise/controller/wm-out-warehouse-bill.service'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { Toast, Loading, HideLoading } from '@/utils/Taro-api'
import '@/components/InformationCard.vue'
import type { TagType } from '@nutui/nutui-taro'

// 定义出库单列表项接口
interface OutWarehouseBillItem {
    id?: string
    name: string
    encode: string
    describe?: string
    outboundType: string
    status: string
    outboundTime?: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties?: Record<string, object>
    concurrencyStamp?: string
}

// 初始化服务
const wmOutWarehouseBillService = new WmOutWarehouseBillService()
const identityUserService = new IdentityUserService()

// 搜索相关
const searchBox = ref()
const searchText = ref('')

// 出库单列表
const outWarehouseList = ref<OutWarehouseBillItem[]>([])
const originalList = ref<OutWarehouseBillItem[]>([])

// 删除确认
const isDelete = ref(false)
const currentItem = ref<OutWarehouseBillItem | null>(null)

// 用户信息缓存
const userCache = ref<Record<string, string>>({})

// 出库类型映射
const storageTypeMap = {
    Ordinary: { type: 'default', text: '普通' },
    Sales: { type: 'success', text: '销售' },
    Production: { type: 'warning', text: '生产' },
    Transfer: { type: 'primary', text: '调拨' },
    PurchaseReturn: { type: 'danger', text: '采购退货' },
}

// 审核状态映射
const auditStatusMap = {
    Newly: { type: 'default', text: '新建' },
    Received: { type: 'success', text: '已完成' },
    Pending: { type: 'warning', text: '待审批' },
    Approved: { type: 'success', text: '已审批' },
    Rejected: { type: 'danger', text: '已拒绝' },
    Cancelled: { type: 'default', text: '已取消' },
    Completed: { type: 'success', text: '已出库' },
}

// 获取标签类型
const getStorageTypeTagType = (type: string): TagType => {
    if (!type) return 'default'
    return (storageTypeMap[type]?.type as TagType) || 'default'
}

// 获取标签文本
const getStorageTypeTagText = (type: string): string => {
    if (!type) return '未知'
    return storageTypeMap[type]?.text || type
}

// 获取审核状态标签类型
const getAuditStatusTagType = (status: string) => {
    return auditStatusMap[status]?.type || 'default'
}

// 获取审核状态文本
const getAuditStatusText = (status: string) => {
    return auditStatusMap[status]?.text || status
}

// 格式化日期
const formatDate = (date?: string) => {
    if (!date) return '-'
    return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 获取用户列表
const fetchUserList = async () => {
    try {
        Loading()
        const defaultParams = {
            skipCount: 0,
            maxResultCount: 1000,
            filter: '',
            sorting: '',
        }
        const result = await identityUserService.getList(defaultParams)
        if (result && result.items) {
            result.items.forEach(user => {
                if (user.id) {
                    userCache.value[user.id] = user.userName || ''
                }
            })
        }
    } catch (error) {
        console.error('获取用户列表失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

// 获取出库单列表
const fetchData = async () => {
    try {
        Loading()
        // 根据API定义构造参数
        const params: any = {
            filterText: '',
            skipCount: 0,
            maxResultCount: 1000,
        }
        const result = await wmOutWarehouseBillService.getPaged(params)
        if (result && result.items) {
            originalList.value = result.items
            outWarehouseList.value = result.items.map(item => ({
                ...item,
                applicantUserName: userCache.value[item.applicantUserId] || item.applicantUserId,
                executorUserName: userCache.value[item.executorUserId] || item.executorUserId,
            }))
        } else {
            originalList.value = []
            outWarehouseList.value = []
        }
    } catch (error) {
        console.error('获取出库单列表失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
        originalList.value = []
        outWarehouseList.value = []
    } finally {
        HideLoading()
    }
}

const handleSearch = async () => {
    try {
        Loading()
        if (searchText.value.trim() === '') {
            await fetchData()
        } else {
            const searchParams: any = {
                filterText: searchText.value.trim(),
                skipCount: 0,
                maxResultCount: 100,
            }
            const result = await wmOutWarehouseBillService.getPaged(searchParams)
            if (result && result.items) {
                originalList.value = result.items
                outWarehouseList.value = result.items.map(item => ({
                    ...item,
                    applicantUserName: userCache.value[item.applicantUserId] || item.applicantUserId,
                    executorUserName: userCache.value[item.executorUserId] || item.executorUserId,
                }))
            } else {
                originalList.value = []
                outWarehouseList.value = []
            }
        }
        if (searchBox.value && typeof searchBox.value.toggle === 'function') {
            searchBox.value.toggle()
        }
    } catch (error) {
        console.error('搜索失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

// 重置搜索
const resetSearch = async () => {
    searchText.value = ''
    await fetchData()
    if (searchBox.value && typeof searchBox.value.toggle === 'function') {
        searchBox.value.toggle()
    }
}

// 处理查看详情
const handleDetailed = (item: OutWarehouseBillItem) => {
    if (item.id) {
        Taro.navigateTo({
            url: `/pages/subpackages/out-warehouse-management/out-warehouse-details/index?id=${item.id}`,
        })
    }
}

// 处理删除
const handleDelete = (item: OutWarehouseBillItem) => {
    currentItem.value = item
    isDelete.value = true
}

// 确认删除
const confirmDelete = async () => {
    if (!currentItem.value?.id) return
    try {
        Loading()
        await wmOutWarehouseBillService.deleteById(currentItem.value.id)
        Toast(t('text.deleteSuccess'), { icon: 'success' })
        fetchData()
    } catch (error) {
        console.error('删除出库单失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
        isDelete.value = false
        currentItem.value = null
    }
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    // 获取用户列表
    fetchUserList()
    // 获取出库单列表
    fetchData()
})
</script>

<style lang="scss">
.content-wrapper {
    padding: 12px;
    background: #f5f5f5;
    min-height: calc(100vh - 100px);
}

.empty-state {
    padding: 40px 0;
    text-align: center;
}

.custom-btn {
    &.primary-btn {
        background-color: #4970f2;
    }

    &.outline-btn {
        color: #4970f2;
        border-color: #4970f2;
    }
}
</style>
