<template>
    <view>
        <nut-form>
            <nut-form-item :label="t('text.name')">
                <nut-input v-model="editForm.name" :placeholder="t('text.pleaseEnterName')" type="text" />
            </nut-form-item>
            <nut-form-item :label="t('text.encode')">
                <nut-input v-model="editForm.encode" :placeholder="t('text.pleaseEnterEncode')" type="text" />
            </nut-form-item>
            <nut-form-item :label="t('text.areaSize')">
                <nut-input v-model="editForm.areaSize" :placeholder="t('text.pleaseEnterAreaSize')" type="text" />
            </nut-form-item>
            <nut-form-item :label="t('text.maxLoad')">
                <nut-input v-model="editForm.maxLoad" :placeholder="t('text.pleaseEnterMaxLoad')" type="text" />
            </nut-form-item>
            <nut-form-item :label="t('text.positionX')">
                <nut-input v-model="editForm.positionX" :placeholder="t('text.pleaseEnterPositionX')" type="text" />
            </nut-form-item>
            <nut-form-item :label="t('text.positionY')">
                <nut-input v-model="editForm.positionY" :placeholder="t('text.pleaseEnterPositionY')" type="text" />
            </nut-form-item>
            <nut-form-item :label="t('text.positionZ')">
                <nut-input v-model="editForm.positionZ" :placeholder="t('text.pleaseEnterPositionZ')" type="text" />
            </nut-form-item>
            <nut-form-item :label="t('text.describe')">
                <nut-input v-model="editForm.describe" :placeholder="t('text.pleaseEnterDescribe')" type="text" />
            </nut-form-item>
            <nut-form-item :label="t('text.dutyUser')">
                <view @click="showDutyUser = true">
                    <text>{{
                        dutyUserInfo?.surname || dutyUserInfo?.name || dutyUserInfo?.email
                            ? (dutyUserInfo?.surname || '') + (dutyUserInfo?.name || '') || dutyUserInfo?.email
                            : t('text.pleaseSelectDutyUser')
                    }}</text>
                </view>
            </nut-form-item>
            <nut-form-item :label="t('text.location')">
                <nut-input v-model="editForm.location" :placeholder="t('text.pleaseEnterLocation')" type="text" />
            </nut-form-item>
            <nut-form-item :label="t('text.isActivate')">
                <nut-switch v-model="editForm.isActivate" />
            </nut-form-item>
            <nut-space direction="vertical" fill>
                <nut-button style="width: 100%" @click="cancelEdit">{{ t('ui.cancel') }}</nut-button>
                <nut-button type="primary" style="width: 100%" @click="confirmEdit">{{ t('ui.submit') }}</nut-button>
            </nut-space>
        </nut-form>
    </view>
    <nut-popup v-model:visible="showDutyUser" position="bottom" round :style="{ height: '50%' }">
        <nut-picker
            :columns="userList"
            :title="t('text.pleaseSelectDutyUser')"
            @confirm="confirmSelectUser"
            @cancel="showDutyUser = false"
        />
    </nut-popup>
</template>

<script setup lang="ts">
import { t } from '@/locale/fanyi'
import { ref, watch } from 'vue'
import { WmWarehouseLocationEditDto } from '@/api/proxy/enterprise/wms/dtos/models'
import { WmWarehouseLocationService } from '@/api/proxy/enterprise/controller/wm-warehouse-location.service'
import { GetForEditInput } from '@/api/proxy/helper/shared/models'
import Taro from '@tarojs/taro'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { IdentityUserDto, GetIdentityUsersInput } from '@/api/proxy/identity-user-service/models'

const wmWarehouseLocationService = new WmWarehouseLocationService()
const props = defineProps<{
    id: string
    editVisible: boolean
}>()

const emit = defineEmits<{
    (e: 'update:editVisible', value: boolean): void
    (e: 'refresh'): void
}>()

const editVisible = ref(props.editVisible)
const editId = ref('')
const identityUserService = new IdentityUserService()
const showDutyUser = ref(false)
const userList = ref<Array<{ text: string; value: string; id: string }>>([])
const dutyUserInfo = ref<IdentityUserDto>({} as IdentityUserDto)

const editForm = ref<WmWarehouseLocationEditDto>({
    name: '',
    encode: '',
    areaSize: 0,
    maxLoad: 0,
    positionX: 0,
    positionY: 0,
    positionZ: 0,
    describe: '',
    isActivate: false,
    location: '',
    dutyUserId: '',
})

const fetchEditData = async () => {
    const input: GetForEditInput = {
        id: editId.value,
    }
    try {
        const result = await wmWarehouseLocationService.getEditor(input)
        editForm.value = result.wmWarehouseLocation
    } catch (error) {
        console.error('获取仓库库位数据失败', error)
    }
}

const fetchUserInfo = async () => {
    try {
        if (editForm.value.dutyUserId) {
            const result = await identityUserService.get(editForm.value.dutyUserId)
            dutyUserInfo.value = result
        }
    } catch (error) {
        console.error('获取用户信息失败', error)
    }
}

const fetchUserList = async () => {
    try {
        const defaultParams: GetIdentityUsersInput = {
            skipCount: 0,
            maxResultCount: 1000,
            filter: '',
            sorting: '',
        }
        const response = await identityUserService.getList(defaultParams)
        if (response && Array.isArray(response.items)) {
            userList.value = response.items.map(item => ({
                text: item.userName + '：' + (item.surname || '') + (item.name || ''),
                value: item.id,
                id: item.id,
            }))
        } else {
            console.error('获取用户列表出错:', response)
        }
    } catch (error) {
        console.error('获取用户列表失败:', error)
    }
}

const confirmSelectUser = ({ selectedOptions }: { selectedOptions: any[] }) => {
    if (selectedOptions && selectedOptions.length > 0) {
        const selectedUser = selectedOptions[0]
        editForm.value.dutyUserId = selectedUser.value
        identityUserService
            .get(selectedUser.value)
            .then(result => {
                dutyUserInfo.value = result
            })
            .catch(() => {
                const nameParts = selectedUser.text.split('：')
                dutyUserInfo.value = {
                    id: selectedUser.value,
                    name: nameParts.length > 1 ? nameParts[1] : '',
                    surname: nameParts.length > 1 ? nameParts[0] : '',
                } as IdentityUserDto
            })
    }
    showDutyUser.value = false
}

const cancelEdit = () => {
    emit('update:editVisible', false)
}

const confirmEdit = async () => {
    try {
        await wmWarehouseLocationService.update(editForm.value)
        emit('update:editVisible', false)
        emit('refresh')
        Taro.showToast({
            title: t('text.submitSuccess'),
            icon: 'success',
        })
    } catch (error) {
        console.error('更新仓库库位数据失败', error)
    }
}

watch(
    () => props.id,
    async newValue => {
        if (newValue && newValue.trim()) {
            editId.value = newValue
            await fetchEditData()
            await fetchUserInfo()
        }
    },
    { immediate: true },
)

watch(
    () => props.editVisible,
    newValue => {
        editVisible.value = newValue
        if (newValue) {
            fetchUserList()
        }
    },
    { immediate: true },
)

watch(
    () => showDutyUser.value,
    async newValue => {
        if (newValue) {
            await fetchUserList()
        }
    },
)
</script>

<style lang="scss"></style>
