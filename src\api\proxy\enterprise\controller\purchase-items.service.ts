import type { GetForEditInput, ReturnResult } from '../../helper/shared/models'
import type {
    GetPurchaseItemsForEditorOutput,
    GetPurchaseItemsInput,
    PurchaseItemsApprovalDto,
    PurchaseItemsCloneDto,
    PurchaseItemsCompletedDto,
    PurchaseItemsCreateDto,
    PurchaseItemsCreateMaterialByTemporaryDto,
    PurchaseItemsDetailDto,
    PurchaseItemsEditDto,
    PurchaseItemsListDto,
    PurchaseItemsOrderDto,
    PurchaseItemsProcurementDto,
    PurchaseItemsRefundedDto,
    PurchaseItemsReturnModifyDto,
    PurchaseItemsSubmitApprovalDto,
    PurchaseItemsUpdateStatusDto,
} from '../wms/dtos/models'
import type { Statistics } from '../wms/models'
import type { PagedResultDto } from '@/api/app/model/baseModel'
import { defHttp } from '@/utils/http/axios'
import { RequestOptions } from '@/utils/http/axios/axiosModels'

export class PurchaseItemsService {
    apiName = 'EnterpriseService'

    clone = (dto: PurchaseItemsCloneDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/purchase-items/ClonePurchaseItems',
                data: dto,
            },
            options,
        )

    create = (input: PurchaseItemsCreateDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/purchase-items',
                data: input,
            },
            options,
        )

    deleteById = (id: string, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'DELETE',
                url: `/api/enterprise/purchase-items/${id}`,
            },
            options,
        )

    get = (id: string, options?: RequestOptions) =>
        defHttp.request<PurchaseItemsDetailDto>(
            {
                method: 'GET',
                url: `/api/enterprise/purchase-items/${id}`,
            },
            options,
        )

    getById = (id: string, options?: RequestOptions) =>
        defHttp.request<PurchaseItemsDetailDto>(
            {
                method: 'GET',
                url: '/api/enterprise/purchase-items/id',
                params: {
                    id,
                },
            },
            options,
        )

    getEditor = (input: GetForEditInput, options?: RequestOptions) =>
        defHttp.request<GetPurchaseItemsForEditorOutput>(
            {
                method: 'GET',
                url: '/api/enterprise/purchase-items/editor',
                params: {
                    id: input.id,
                },
            },
            options,
        )

    getPaged = (input: GetPurchaseItemsInput, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<PurchaseItemsListDto>>(
            {
                method: 'GET',
                url: '/api/enterprise/purchase-items',
                params: {
                    purchaseOrderId: input.purchaseOrderId,
                    status: input.status,
                    isDeleted: input.isDeleted,
                    type: input.type,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )

    statisticsMaterial = (materialId: string, options?: RequestOptions) =>
        defHttp.request<Statistics>(
            {
                method: 'GET',
                url: '/api/enterprise/purchase-items/StatisticsMaterial',
                params: {
                    materialId,
                },
            },
            options,
        )

    submitApproval = (input: PurchaseItemsSubmitApprovalDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/submit-approval',
                data: input,
            },
            options,
        )

    approval = (input: PurchaseItemsApprovalDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/approval',
                data: input,
            },
            options,
        )

    inProcurement = (input: PurchaseItemsProcurementDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/in-procurement',
                data: input,
            },
            options,
        )

    order = (input: PurchaseItemsOrderDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/order',
                data: input,
            },
            options,
        )

    returnModify = (input: PurchaseItemsReturnModifyDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/return-modify',
                data: input,
            },
            options,
        )

    refunded = (input: PurchaseItemsRefundedDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/refunded',
                data: input,
            },
            options,
        )

    update = (input: PurchaseItemsEditDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/purchase-items',
                data: input,
            },
            options,
        )

    updateStatus = (updateStatusDto: PurchaseItemsUpdateStatusDto, options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'PUT',
                url: '/api/enterprise/purchase-items/updateStatus',
                data: updateStatusDto,
            },
            options,
        )

    completed = (inputs: PurchaseItemsCompletedDto[], options?: RequestOptions) =>
        defHttp.request<ReturnResult[]>(
            {
                method: 'PUT',
                url: '/api/enterprise/workflows/purchase-items/completed',
                data: inputs,
            },
            options,
        )

    createMaterialByTemporary = (input: PurchaseItemsCreateMaterialByTemporaryDto[], options?: RequestOptions) =>
        defHttp.request<void>(
            {
                method: 'POST',
                url: '/api/enterprise/purchase-items/create-material-by-temporary',
                data: input,
            },
            options,
        )
}
