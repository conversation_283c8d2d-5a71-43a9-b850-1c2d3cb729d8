import { getCurrentInstance } from '@tarojs/taro'
import { onMounted } from 'vue'

const Current = getCurrentInstance()

/**
 * 组件显示事件
 * @param callback
 */
export function useDidShow(callback) {
    onMounted(callback)
    Current?.page?.onShow && (Current.page.onShow = callback)
}
/**
 * 组件下拉刷新事件
 * @param callback
 */
export function usePullDownRefresh(callback) {
    Current?.page?.onPullDownRefresh && (Current.page.onPullDownRefresh = callback)
}

/**
 *组件滚动到底部事件
 * @param callback
 */
export function useReachBottom(callback) {
    Current?.page?.onReachBottom && (Current.page.onReachBottom = callback)
}
