<template>
    <view>
        <nut-tabs v-model="tabValue" :animated-time="0">
            <nut-tab-pane :title="t('text.warehouseAreaInfo')" pane-key="1">
                <view>
                    <nut-cell :title="t('text.name')" :desc="areaInfo?.name"></nut-cell>
                    <nut-cell :title="t('text.encode')" :desc="areaInfo?.encode"></nut-cell>
                    <nut-cell :title="t('text.location')" :desc="areaInfo?.location"></nut-cell>
                    <nut-cell :title="t('text.areaSize')" :desc="String(areaInfo?.areaSize)"></nut-cell>
                    <nut-cell :title="t('text.maxLoad')" :desc="String(areaInfo?.maxLoad)"></nut-cell>
                    <nut-cell :title="t('text.positionX')" :desc="String(areaInfo?.positionX)"></nut-cell>
                    <nut-cell :title="t('text.positionY')" :desc="String(areaInfo?.positionY)"></nut-cell>
                    <nut-cell :title="t('text.positionZ')" :desc="String(areaInfo?.positionZ)"></nut-cell>
                    <nut-cell
                        :title="t('text.dutyUserName')"
                        :desc="
                            dutyUserInfo?.surname && dutyUserInfo?.name ? dutyUserInfo.surname + dutyUserInfo.name : '-'
                        "
                    ></nut-cell>
                    <nut-cell :title="t('text.describe')" :desc="areaInfo?.describe || '-'"></nut-cell>
                    <nut-cell :title="t('text.creationTime')">
                        <template #desc>
                            {{
                                areaInfo?.creationTime
                                    ? dayjs(areaInfo.creationTime).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'
                            }}
                        </template>
                    </nut-cell>
                    <nut-cell :title="t('text.status')">
                        <template #desc>
                            <nut-tag size="small" :type="areaInfo?.isActivate ? 'primary' : 'danger'">
                                {{ areaInfo?.isActivate ? t('tag.enable') : t('tag.disable') }}
                            </nut-tag>
                        </template>
                    </nut-cell>
                </view>
            </nut-tab-pane>
            <nut-tab-pane :title="t('text.warehouseLocation')" pane-key="2">
                <warehouse-location-table :location-id="areaInfo?.id" />
            </nut-tab-pane>
        </nut-tabs>
    </view>
</template>

<script setup lang="ts">
import { useDidShow } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import { WmWarehouseAreaService } from '@/api/proxy/enterprise/controller/wm-warehouse-area.service'
import { WmWarehouseAreaDetailDto } from '@/api/proxy/enterprise/wms/dtos'
import { ref } from 'vue'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import dayjs from 'dayjs'
import WarehouseLocationTable from './warehouse-location-table.vue'

Taro.setNavigationBarTitle({ title: t('menu.warehouseAreaDetails') })

interface DutyUserInfo {
    surname: string
    name: string
}

const wmWarehouseAreaService = new WmWarehouseAreaService()
const identityUserService = new IdentityUserService()
const areaInfo = ref<WmWarehouseAreaDetailDto>()
const tabValue = ref('1')
const dutyUserInfo = ref<DutyUserInfo>()

const fetchData = async () => {
    const id = Taro.useRouter().params.id
    if (id) {
        try {
            const result = await wmWarehouseAreaService.getById(id)
            areaInfo.value = result
        } catch (error) {
            console.error('获取数据错误:', error.response ? error.response.data : error.message)
        }
    }
}

const fetchUserInfo = async () => {
    try {
        if (areaInfo.value?.dutyUserId) {
            const result = await identityUserService.get(areaInfo.value.dutyUserId)
            if (result.surname && result.name) {
                dutyUserInfo.value = {
                    surname: result.surname,
                    name: result.name,
                }
            }
        }
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

useDidShow(async () => {
    await fetchData()
    await fetchUserInfo()
})
</script>

<style lang="scss">
.empty-state {
    padding: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
