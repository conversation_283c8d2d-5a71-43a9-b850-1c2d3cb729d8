import { PagedResultDto } from '@/shared/models/dtos'
import { RequestOptions } from '@/utils/http/axios/model/axios'
import { defHttp } from '@/utils/http/axios'
import { GetIotAlarmLogInput, IotAlarmLogListDto } from '@/api/proxy/iot-alarm-log-service/models'

export class IotAlarmLogService {
    getPaged = (input: GetIotAlarmLogInput, cancellationToken?: any, options?: RequestOptions) =>
        defHttp.request<PagedResultDto<IotAlarmLogListDto>>(
            {
                method: 'GET',
                url: '/api/iot-service/alarm-log/paged',
                params: {
                    iotDeviceId: input.iotDeviceId,
                    sorting: input.sorting,
                    filterText: input.filterText,
                    skipCount: input.skipCount,
                    maxResultCount: input.maxResultCount,
                },
            },
            options,
        )
}
