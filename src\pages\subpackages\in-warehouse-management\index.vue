<template>
    <view>
        <view>
            <nut-menu>
                <nut-menu-item ref="searchBox" :title="t('ui.search')">
                    <nut-searchbar v-model="searchText" :clearable="true" :placeholder="t('text.pleaseEnterName')">
                        <template #leftin>
                            <Search2 />
                        </template>
                        <template #rightout>
                            <view style="display: flex; gap: 10px">
                                <nut-button type="primary" size="small" @click="handleSearch">
                                    {{ t('ui.search') }}
                                </nut-button>
                                <nut-button type="primary" plain size="small" @click="resetSearch">
                                    {{ t('ui.reset') }}
                                </nut-button>
                            </view>
                        </template>
                    </nut-searchbar>
                </nut-menu-item>
            </nut-menu>
        </view>
        <view style="display: flex; justify-content: space-between; align-items: center; padding: 12px">
            <view></view>
            <view>
                <nut-button class="custom-btn outline-btn" size="small" @click="fetchData">
                    {{ t('ui.refresh') }}
                </nut-button>
            </view>
        </view>
        <view class="content-wrapper">
            <view v-if="inWarehouseList.length > 0">
                <view v-for="(item, index) in inWarehouseList" :key="index" @click="handleDetailed(item)">
                    <InformationCard :show-empty-image="false">
                        <template #front-title> [{{ index + 1 }}] </template>
                        <template #title>
                            {{ item.name }}
                        </template>
                        <template #line-first> {{ t('text.encode') }} ： {{ item.encode }} </template>
                        <template #line-second> {{ t('text.describe') }} ： {{ item.describe || '-' }} </template>
                        <template #line-third>
                            <span>{{ t('text.creationTime') }}：{{ formatDate(item.creationTime) }}</span>
                        </template>
                        <template #space-one>
                            <nut-tag :type="getStorageTypeTagType(item.storageType)" style="margin-bottom: 0.5vh">
                                {{ getStorageTypeText(item.storageType) }}
                            </nut-tag>
                        </template>
                        <template #space-two>
                            <nut-tag :type="getAuditStatusTagType(item.status)" style="margin-bottom: 0.5vh">
                                {{ getAuditStatusText(item.status) }}
                            </nut-tag>
                        </template>
                    </InformationCard>
                </view>
            </view>
            <view v-else class="empty-state">
                <nut-empty description="暂无数据" image="empty"></nut-empty>
            </view>
        </view>
        <nut-dialog
            :title="t('text.warmReminder')"
            :content="t('text.deleteConfirm')"
            v-model:visible="isDelete"
            @cancel="isDelete = false"
            @ok="confirmDelete"
            :ok-text="t('ui.confirm')"
            :cancel-text="t('ui.cancel')"
        />
    </view>
</template>

<script lang="ts" setup>
import { useDidShow } from '@/hooks/component/hooks'
import { ref } from 'vue'
import { t } from '@/locale/fanyi'
import { Search2 } from '@nutui/icons-vue-taro'
import dayjs from 'dayjs'
import Taro from '@tarojs/taro'
import { WmInWarehouseBillService } from '@/api/proxy/enterprise/controller/wm-in-warehouse-bill.service'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { Toast, Loading, HideLoading } from '@/utils/Taro-api'
import '@/components/InformationCard.vue'

// 定义入库单列表项接口
interface InWarehouseBillItem {
    id?: string
    name: string
    encode: string
    describe?: string
    storageType: string
    status: string
    storageTime?: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties?: Record<string, object>
    concurrencyStamp?: string
}

// 初始化服务
const wmInWarehouseBillService = new WmInWarehouseBillService()
const identityUserService = new IdentityUserService()

// 搜索相关
const searchBox = ref()
const searchText = ref('')

// 入库单列表
const inWarehouseList = ref<InWarehouseBillItem[]>([])
const originalList = ref<InWarehouseBillItem[]>([])

// 删除确认
const isDelete = ref(false)
const currentItem = ref<InWarehouseBillItem | null>(null)

// 用户信息缓存
const userCache = ref<Record<string, string>>({})

// 入库类型映射
const storageTypeMap = {
    Ordinary: { type: 'default', text: '普通' },
    Purchase: { type: 'success', text: '采购' },
    FinishedProduct: { type: 'warning', text: '成品' },
    SemiFinishedProduct: { type: 'warning', text: '半成品' },
    RawMaterial: { type: 'primary', text: '原料' },
    Return: { type: 'danger', text: '退货' },
    Pending: { type: 'warning', text: '待入库' },
    Other: { type: 'default', text: '其他' },
    SalesReturn: { type: 'danger', text: '销售退货' },
}

// 审核状态映射
const auditStatusMap = {
    Newly: { type: 'default', text: '新建' },
    Pending: { type: 'warning', text: '待审批' },
    InProgress: { type: 'primary', text: '处理中' },
    Received: { type: 'success', text: '已完成' },
    Returned: { type: 'danger', text: '已退回' },
    Cancelled: { type: 'default', text: '已取消' },
    PartiallyReturned: { type: 'warning', text: '部分退回' },
}

// 获取入库类型标签类型
const getStorageTypeTagType = (type: string) => {
    return storageTypeMap[type]?.type || 'default'
}

// 获取入库类型文本
const getStorageTypeText = (type: string) => {
    return storageTypeMap[type]?.text || type
}

// 获取审核状态标签类型
const getAuditStatusTagType = (status: string) => {
    return auditStatusMap[status]?.type || 'default'
}

// 获取审核状态文本
const getAuditStatusText = (status: string) => {
    return auditStatusMap[status]?.text || status
}

// 格式化日期
const formatDate = (date?: string) => {
    if (!date) return '-'
    return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 获取用户列表
const fetchUserList = async () => {
    try {
        Loading()
        const defaultParams = {
            skipCount: 0,
            maxResultCount: 1000,
            filter: '',
            sorting: '',
        }
        const result = await identityUserService.getList(defaultParams)
        if (result && result.items) {
            result.items.forEach(user => {
                if (user.id) {
                    userCache.value[user.id] = user.userName || ''
                }
            })
        }
    } catch (error) {
        console.error('获取用户列表失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

// 搜索
const handleSearch = async () => {
    try {
        Loading()
        // 如果搜索框为空，直接重新加载所有数据
        if (searchText.value.trim() === '') {
            await fetchData()
        } else {
            // 构建搜索参数
            const searchParams = {
                filterText: searchText.value.trim(),
            }
            // 使用 API 进行搜索
            const result = await wmInWarehouseBillService.getPaged(searchParams)
            if (result && result.items) {
                originalList.value = result.items
                // 替换用户名
                inWarehouseList.value = result.items.map(item => ({
                    ...item,
                    applicantUserName: userCache.value[item.applicantUserId] || item.applicantUserId,
                    executorUserName: userCache.value[item.executorUserId] || item.executorUserId,
                }))
            } else {
                originalList.value = []
                inWarehouseList.value = []
            }
        }
        if (searchBox.value && typeof searchBox.value.toggle === 'function') {
            searchBox.value.toggle()
        }
    } catch (error) {
        console.error('搜索失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
    }
}

// 重置搜索
const resetSearch = async () => {
    searchText.value = ''
    await fetchData()
    if (searchBox.value && typeof searchBox.value.toggle === 'function') {
        searchBox.value.toggle()
    }
}

// 获取入库单列表
const fetchData = async () => {
    try {
        Loading()
        const result = await wmInWarehouseBillService.getPaged({})
        if (result && result.items) {
            originalList.value = result.items
            inWarehouseList.value = result.items.map(item => ({
                ...item,
                applicantUserName: userCache.value[item.applicantUserId] || item.applicantUserId,
                executorUserName: userCache.value[item.executorUserId] || item.executorUserId,
            }))
        } else {
            originalList.value = []
            inWarehouseList.value = []
        }
    } catch (error) {
        console.error('获取入库单列表失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
        originalList.value = []
        inWarehouseList.value = []
    } finally {
        HideLoading()
    }
}

// 处理查看详情
const handleDetailed = (item: InWarehouseBillItem) => {
    if (item.id) {
        Taro.navigateTo({
            url: `/pages/subpackages/in-warehouse-management/in-warehouse-details/index?id=${item.id}`,
        })
    }
}

// 处理删除
const handleDelete = (item: InWarehouseBillItem) => {
    currentItem.value = item
    isDelete.value = true
}

// 确认删除
const confirmDelete = async () => {
    if (!currentItem.value?.id) return
    try {
        Loading()
        await wmInWarehouseBillService.deleteById(currentItem.value.id)
        Toast(t('text.deleteSuccess'), { icon: 'success' })
        fetchData()
    } catch (error) {
        console.error('删除入库单失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        HideLoading()
        isDelete.value = false
        currentItem.value = null
    }
}

/**
 * @description 页面显示钩子函数
 */
useDidShow(() => {
    // 获取用户列表
    fetchUserList()
    // 获取入库单列表
    fetchData()
})
</script>

<style lang="scss">
.content-wrapper {
    padding: 12px;
    background: #f5f5f5;
    min-height: calc(100vh - 100px);
}

.empty-state {
    padding: 40px 0;
    text-align: center;
}

.custom-btn {
    &.primary-btn {
        background-color: #4970f2;
    }

    &.outline-btn {
        color: #4970f2;
        border-color: #4970f2;
    }
}
</style>
