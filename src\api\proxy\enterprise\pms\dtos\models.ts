import type { GetForEditInput, PagedSortedAndFilteredInputDto } from '../../../helper/shared/models'
import type { EntityDto } from '@/api/app/model/baseModel'
import type { ProductUnit } from '../product-unit.enum'

export interface GetProductAttributeForEditorOutput {
    productAttribute: ProductAttributeEditDto
}

export interface GetProductAttributeInput extends PagedSortedAndFilteredInputDto {
    productParentId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetProductItemForEditorOutput {
    productItem: ProductItemEditDto
    productUnitEnum: any
}

export interface GetProductItemInput extends PagedSortedAndFilteredInputDto {
    productParentId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetProductLibraryEditorInput extends GetForEditInput {}

export interface GetProductLibraryForEditorOutput {
    productLibrary: ProductLibraryEditDto
}

export interface GetProductLibraryInput extends PagedSortedAndFilteredInputDto {
    isDeleted?: boolean
    sorting?: string
}

export interface ProductAttributeCreateDto extends ProductAttributeCreateOrUpdateDtoBase {}

export interface ProductAttributeCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode?: string
    attValue?: string
    describe?: string
    productParentId?: string
}

export interface ProductAttributeDetailDto extends EntityDto<string> {
    name: string
    encode?: string
    attValue?: string
    describe?: string
    productParentId?: string
    productParentName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface ProductAttributeEditDto extends ProductAttributeCreateOrUpdateDtoBase {}

export interface ProductAttributeListDto extends EntityDto<string> {
    name: string
    encode?: string
    attValue?: string
    describe?: string
    productParentId?: string
    productParentName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    isDeleted: boolean
    deleterId?: string
    deletionTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface ProductItemCreateDto extends ProductItemCreateOrUpdateDtoBase {}

export interface ProductItemCreateOrUpdateDtoBase {
    id?: string
    name: string
    serialNo: number
    encode: string
    model: string
    quantity: number
    describe?: string
    images?: string
    unit: enum
    isActivate: boolean
    productParentId?: string
}

export interface ProductItemDetailDto extends EntityDto<string> {
    name: string
    serialNo: number
    encode: string
    model: string
    quantity: number
    describe?: string
    images?: string
    unit: enum
    isActivate: boolean
    productParentId?: string
    productParentName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    isDeleted: boolean
    deleterId?: string
    deletionTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface ProductItemEditDto extends ProductItemCreateOrUpdateDtoBase {}

export interface ProductItemListDto extends EntityDto<string> {
    name: string
    serialNo: number
    encode: string
    model: string
    quantity: number
    describe?: string
    images?: string
    unit: enum
    isActivate: boolean
    productParentId?: string
    productParentName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    isDeleted: boolean
    deleterId?: string
    deletionTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface ProductLibraryCreateDto extends ProductLibraryCreateOrUpdateDtoBase {}

export interface ProductLibraryCreateOrUpdateDtoBase {
    id?: string
    name: string
    serialNo: number
    encode: string
    describe?: string
    images?: string
    isActivate: boolean
    productParentId?: string
}

export interface ProductLibraryDetailDto extends EntityDto<string> {
    name: string
    serialNo: number
    encode: string
    describe?: string
    images?: string
    isActivate: boolean
    productParentId?: string
    productParentName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface ProductLibraryEditDto extends ProductLibraryCreateOrUpdateDtoBase {}

export interface ProductLibraryTreeListDto {
    id?: string
    name?: string
    serialNo: number
    encode?: string
    describe?: string
    images?: string
    isActivate: boolean
    produceDutyUserId?: string
    maintainDutyUserId?: string
    produceDutyUserName?: string
    maintainDutyUserName?: string
    productParentId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    isDeleted: boolean
    deleterId?: string
    deletionTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
    children: ProductLibraryTreeListDto[]
}

export interface ProductLibraryTreeNodesDto {
    id?: string
    name?: string
    serialNo: number
    encode?: string
    describe?: string
    isActivate: boolean
    productParentId?: string
    children: ProductLibraryTreeNodesDto[]
}
