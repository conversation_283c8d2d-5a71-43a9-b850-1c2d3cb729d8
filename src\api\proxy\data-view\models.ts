import { EntityDto } from '@/shared/models/dtos'
import { FactoryDeviceForm, FactoryDeviceType } from '@/api/proxy/factory-device-service/models'

export interface GetDataViewDeviceInput {
    factoryParentId?: string
}

export interface GetDeviceTimeUnitInput {
    factoryModelId?: string
    deviceId?: string
    timeUnit: TimeUnit
}

export interface DeviceWorkCountModelDto {
    totalNum: number
    qualifiedNum: number
    defectiveCount: number
    uncertainCount: number
    wasteNum: number
    time?: string
}

export interface DeviceStatusCountsDto {
    unknown: number
    offline: number
    stop: number
    pause: number
    runing: number
    alarm: number
    esp: number
    total: number
}

export interface FactoryDeviceIotFlowListDto extends FactoryDeviceListDto {
    iotStatus: IotDeviceStatus
    mode: DeviceOptMode
    status: DeviceStatus
    modeColor?: string
    statusColor?: string
    targetNum: number
    workCount: number
    productivity: number
    curProg?: string
    iotCreationTime?: string
    lastOnlineTime?: string
    imagesUrl?: string[]
}

export interface FactoryDeviceListDto extends EntityDto<string> {
    name?: string
    serialNo: number
    encode?: string
    describe?: string
    images?: string
    deviceForm: FactoryDeviceForm
    deviceType: FactoryDeviceType
    isActivate: boolean
    produceDutyUserId?: string
    maintainDutyUserId?: string
    produceDutyUserName?: string
    maintainDutyUserName?: string
    iotGatewayId?: string
    iotDeviceId?: string
    iotDeviceName?: string
    factoryParentId?: string
    factoryParentName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface GetDeviceDataInput {
    factoryModelId?: string
    deviceId?: string
    dateTime?: string
}

export interface DeviceUseRateDataDto {
    totalRate: DeviceUseRate
    colors: string[]
    deviceListRate: DeviceUseRate[]
}

export interface DeviceUseRate {
    deviceName?: string
    powerOnRate: number
    utilizationRate: number
    spareRate: number
    failureRate: number
    time?: string
}

export interface DeviceOptModeRateDataDto {
    totalRate: DeviceOptModeRate
    colors: string[]
    deviceListRate: DeviceOptModeRate[]
}

export interface DeviceOptModeRate {
    deviceName?: string
    mdiRate: number
    autoRate: number
    editRate: number
    jogRate: number
    handleRate: number
    zrnRate: number
    time?: string
}

export interface DeviceStatusRateDataDto {
    totalRate: DeviceStatusRate
    colors: string[]
    deviceListRate: DeviceStatusRate[]
}

export interface DeviceStatusRate {
    deviceName?: string
    unknownRate: number
    offlineRate: number
    stopRate: number
    pauseRate: number
    runingRate: number
    alarmRate: number
    espRate: number
    time?: string
}

export interface DeviceWorkCountRankDto {
    name?: string
    total: number
}

export interface DeviceProductionTotalProgressDto {
    targetNum: number
    workCount: number
    productivity: number
}

export interface GetDeviceTimeRangeInput {
    factoryModelId?: string
    deviceId?: string
    startTime?: string
    endTime?: string
}

export interface DeviceStatusHistoryDatasDto {
    startTime: number
    endTime: number
    names: string[]
    items: EChartsDataItemDto[]
}

export interface EChartsDataItemDto {
    name?: string
    status?: string
    value: number[]
    test1: string[]
    timeSpan?: string
    itemStyle: ItemStyle
}

export interface ItemStyle {
    normal: ItemsColor
}

export interface ItemsColor {
    color?: string
}

export enum TimeUnit {
    Day = 'Day',
    Week = 'Week',
    Month = 'Month',
    Year = 'Year',
}

export enum IotDeviceStatus {
    UNKNOWN = 'UNKNOWN',
    INACTIVE = 'INACTIVE',
    ONLINE = 'ONLINE',
    OFFLINE = 'OFFLINE',
    ABNORMAL = 'ABNORMAL',
    FROZEN = 'FROZEN',
}

export enum DeviceOptMode {
    Unknown = 'Unknown',
    Unavailable = 'Unavailable',
    Mdi = 'Mdi',
    Auto = 'Auto',
    Edit = 'Edit',
    Jog = 'Jog',
    Handle = 'Handle',
    Zrn = 'Zrn',
    SingleStep = 'SingleStep',
    JogTeach = 'JogTeach',
    HandleTeach = 'HandleTeach',
    PrgZrn = 'PrgZrn',
}

export enum DeviceStatus {
    Unknown = 'Unknown',
    Offline = 'Offline',
    Stop = 'Stop',
    Pause = 'Pause',
    Runing = 'Runing',
    Alarm = 'Alarm',
    Esp = 'Esp',
}
