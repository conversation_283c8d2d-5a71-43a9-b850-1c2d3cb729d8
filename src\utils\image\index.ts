import { IVabUploadData } from '../../../types/IVabUploadData'
import { AppOssObjectService } from '@/api/app/oss/appOssObjectService'

const appOssObjectService = new AppOssObjectService()

/**
 * 将字符串转换为图片数组
 * @param data 图片数组字符串
 */
export const handleImages = (data: string) => {
    if (!data || data === '{}') {
        return
    }
    const obj = JSON.parse(data)
    const imgList: string[] = []
    if (Array.isArray(obj)) {
        obj.forEach((item: IVabUploadData) => {
            const url = appOssObjectService.generateOssUrl(item.bucket, item.path, item.name)
            imgList.push(url)
        })
    }
    return imgList
}
